import json
import logging
import os
import random
import sys
import time
from datetime import datetime
from urllib.parse import quote

import requests
from PyQt6.QtCore import QUrl, QTimer, QEventLoop, pyqtSignal, QObject
from PyQt6.QtNetwork import QNetworkProxy
from PyQt6.QtWebEngineCore import QWebEngineProfile, QWebEnginePage
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget

# 可选导入pyautogui用于模拟键盘操作
try:
    import pyautogui

    HAS_PYAUTOGUI = True
except ImportError:
    HAS_PYAUTOGUI = False
    print("警告：未安装pyautogui模块，无法使用键盘模拟功能")

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cursor_registration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
SIGNUP_URL = "https://authenticator.cursor.sh/password?email={}&redirect_uri=https%3A%2F%2Fcursor.com%2Fapi%2Fauth%2Fcallback"
API_BASE_URL = "http://127.0.0.1:48080/admin-api"  # 修改为你的实际API端点
EMAIL_CODE_ENDPOINT = "/cqp/cursoremailpool/getEmailCode"
TOKEN_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/saveRecord"

# 代理配置
USE_PROXY = False  # 是否使用代理
PROXY_HOST = "127.0.0.1"  # 代理服务器地址
PROXY_PORT = 7890  # 代理服务器端口

# 控制注册线程数量的常量
DEFAULT_THREADS = 1  # 默认并发线程数
MAX_THREADS = 20  # 最大并发线程数

# 全局引用，防止被垃圾回收
browser_windows = []


class SignalHandler(QObject):
    finished = pyqtSignal(str, str, str)  # group_id, email, token
    error = pyqtSignal(str, str, str)  # group_id, email, error_msg


class SimpleBrowserWindow(QMainWindow):
    """简化版浏览器窗口，仅包含必要功能"""

    def __init__(self, profile_path=None):
        super().__init__()
        self.setWindowTitle("Cursor自动注册")
        self.resize(1200, 800)

        # 创建中央控件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        # 创建浏览器控件
        if profile_path:
            self.profile = QWebEngineProfile(profile_path)
            self.profile.setPersistentCookiesPolicy(QWebEngineProfile.PersistentCookiesPolicy.NoPersistentCookies)
            self.profile.setHttpCacheType(QWebEngineProfile.HttpCacheType.NoCache)
        else:
            self.profile = QWebEngineProfile.defaultProfile()

        self.browser = QWebEngineView()
        self.page = QWebEnginePage(self.profile, self.browser)
        self.browser.setPage(self.page)

        # 添加浏览器控件到布局
        self.layout.addWidget(self.browser)

    def load_url(self, url):
        """加载指定URL"""
        self.browser.load(QUrl(url))


class SimpleCursorAutoRegister(QObject):
    def __init__(self, group_id, headless=False):
        super().__init__()
        self.group_id = group_id
        self.headless = headless
        self.email = None
        self.browser_window = None
        self.browser = None
        self.profile_path = f"chrome-data-cursor/{self.group_id}"
        self.signals = SignalHandler()
        self.token = None
        self.code = None
        self.max_attempts = 20  # 最大尝试次数
        self.current_attempt = 0

    def generate_email(self):
        """生成随机邮箱地址"""
        timestamp = int(time.time())
        random_num = random.randint(100, 999)
        return f"a965586934{timestamp}{random_num}@2925.com"

    def setup_browser(self):
        """设置浏览器"""
        logger.info(f"[{self.group_id}] 开始设置浏览器...")
        os.makedirs(self.profile_path, exist_ok=True)

        try:
            # 创建浏览器窗口
            self.browser_window = SimpleBrowserWindow(self.profile_path)
            self.browser = self.browser_window.browser

            # 设置代理（如果启用）
            if USE_PROXY:
                proxy = QNetworkProxy()
                proxy.setType(QNetworkProxy.ProxyType.HttpProxy)
                proxy.setHostName(PROXY_HOST)
                proxy.setPort(PROXY_PORT)
                QNetworkProxy.setApplicationProxy(proxy)
                logger.info(f"[{self.group_id}] 已设置浏览器代理: {PROXY_HOST}:{PROXY_PORT}")

            # 显示窗口（除非是无头模式）
            if not self.headless:
                self.browser_window.show()

            # 保存到全局列表，防止被垃圾回收
            global browser_windows
            browser_windows.append(self.browser_window)

            logger.info(f"[{self.group_id}] 浏览器设置完成")
            return True
        except Exception as e:
            logger.exception(f"[{self.group_id}] 设置浏览器时出现异常: {e}")
            return False

    def execute_js(self, script):
        """执行JavaScript并返回结果"""
        try:
            loop = QEventLoop()
            result = [None]

            def callback(res):
                result[0] = res
                loop.quit()

            self.browser.page().runJavaScript(script, callback)
            loop.exec()
            return result[0]
        except Exception as e:
            logger.warning(f"JS执行错误: {e}")
            return None

    def check_element_exists(self, xpath):
        """检查元素是否存在"""
        script = f"""
        (function() {{
            try {{
                var xpath = {json.dumps(xpath)};
                var element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                return !!element;
            }} catch(e) {{
                console.error('XPath检查错误:', e);
                return false;
            }}
        }})();
        """
        return self.execute_js(script)

    def click_element(self, xpath):
        """通过XPath点击元素"""
        logger.info(f"[{self.group_id}] 尝试点击元素: {xpath}")
        script = f"""
        (function() {{
            try {{
                var xpath = {json.dumps(xpath)};
                var element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if(element) {{
                    element.scrollIntoView({{behavior: 'auto', block: 'center'}});
                    setTimeout(function() {{
                        element.click();
                    }}, 200);
                    return true;
                }}
                return false;
            }} catch(e) {{
                console.error('点击元素错误:', e);
                return false;
            }}
        }})();
        """
        result = self.execute_js(script)
        if result:
            logger.info(f"[{self.group_id}] 元素点击成功")
            return True
        return False

    def handle_turnstile(self):
        """处理 Turnstile 验证（通过聚焦和模拟键盘按键）"""
        logger.info(f"[{self.group_id}] 开始处理 Turnstile 人机验证...")

        # 1. 等待 cf-turnstile 元素出现（最多等待 8 秒，每 0.25 秒检测一次）
        for _ in range(32):
            if self.check_element_exists("//div[@id='cf-turnstile']"):
                break
            time.sleep(0.25)
        else:
            logger.error(f"[{self.group_id}] 未找到 cf-turnstile 元素，放弃验证")
            return False

        # 2. 通过 JS 将 turnstile 容器滚动到视图中并聚焦
        scroll_script = """
        (function() {
            try {
                var container = document.getElementById('cf-turnstile');
                if (!container) return false;
                
                // 滚动到视图中央
                container.scrollIntoView({behavior: 'auto', block: 'center'});
                
                // 尝试聚焦容器或iframe
                var iframe = container.querySelector('iframe');
                var target = iframe || container;
                target.focus();
                
                // 点击容器使iframe获得焦点
                container.click();
                
                return true;
            } catch(err) {
                console.error('Turnstile scroll/focus error:', err);
                return false;
            }
        })();
        """
        
        focus_result = self.execute_js(scroll_script)
        logger.info(f"[{self.group_id}] Turnstile 聚焦结果: {focus_result}")
        
        # 等待一小段时间确保聚焦完成
        time.sleep(0.5)
        
        # 3. 使用PyQt发送TAB和空格键
        try:
            from PyQt6.QtGui import QKeyEvent
            from PyQt6.QtCore import Qt, QEvent
            
            # 发送TAB键
            tab_event = QKeyEvent(QEvent.Type.KeyPress, Qt.Key.Key_Tab, Qt.KeyboardModifier.NoModifier)
            QApplication.postEvent(self.browser, tab_event)
            QApplication.sendEvent(self.browser, tab_event)
            
            # 短暂等待
            time.sleep(0.5)
            
            # 发送空格键
            space_event = QKeyEvent(QEvent.Type.KeyPress, Qt.Key.Key_Space, Qt.KeyboardModifier.NoModifier)
            QApplication.postEvent(self.browser, space_event)
            QApplication.sendEvent(self.browser, space_event)
            
            # 发送按键释放事件
            tab_release = QKeyEvent(QEvent.Type.KeyRelease, Qt.Key.Key_Tab, Qt.KeyboardModifier.NoModifier)
            space_release = QKeyEvent(QEvent.Type.KeyRelease, Qt.Key.Key_Space, Qt.KeyboardModifier.NoModifier)
            QApplication.sendEvent(self.browser, tab_release)
            QApplication.sendEvent(self.browser, space_release)
            
            logger.info(f"[{self.group_id}] 已发送 TAB+空格 按键事件")
            
        except Exception as e:
            logger.error(f"[{self.group_id}] 发送键盘事件失败: {e}")
            
            # 如果PyQt方法失败，尝试使用JavaScript模拟按键
            key_script = """
            (function() {
                try {
                    // 模拟Tab键
                    document.dispatchEvent(new KeyboardEvent('keydown', {key: 'Tab', code: 'Tab', keyCode: 9, which: 9, bubbles: true}));
                    document.dispatchEvent(new KeyboardEvent('keyup', {key: 'Tab', code: 'Tab', keyCode: 9, which: 9, bubbles: true}));
                    
                    // 短暂延迟
                    setTimeout(function() {
                        // 模拟空格键
                        document.dispatchEvent(new KeyboardEvent('keydown', {key: ' ', code: 'Space', keyCode: 32, which: 32, bubbles: true}));
                        document.dispatchEvent(new KeyboardEvent('keyup', {key: ' ', code: 'Space', keyCode: 32, which: 32, bubbles: true}));
                    }, 500);
                    
                    return true;
                } catch(e) {
                    console.error('JS键盘事件模拟失败:', e);
                    return false;
                }
            })();
            """
            self.execute_js(key_script)
            logger.info(f"[{self.group_id}] 已通过JS模拟 TAB+空格 按键")

        # 4. 等待验证结果，最多 5 秒
        check_success_script = """
        (function() {
            try {
                var c = document.getElementById('cf-turnstile');
                if (!c) return true; // 元素不存在=>通过
                if (document.querySelector('[data-state="success"]')) return true;

                var st = window.getComputedStyle(c);
                if (st.display === 'none' || st.visibility === 'hidden') return true;
                return false;
            } catch(e) { return false; }
        })();
        """

        for _ in range(20):  # 20 * 0.25 = 5 秒
            if self.execute_js(check_success_script):
                logger.info(f"[{self.group_id}] Turnstile 验证已通过")
                return True
            time.sleep(0.25)

        logger.warning(f"[{self.group_id}] Turnstile 验证未通过或超时")
        return False

    def input_verification_code(self, code):
        """输入验证码"""
        if not code or len(code) != 6:
            logger.error(f"[{self.group_id}] 无效的验证码: {code}")
            return False

        script = f"""
        (function() {{
            try {{
                var code = {json.dumps(code)};
                var inputs = document.querySelectorAll('input[inputmode="numeric"]');
                if (inputs.length !== 6) {{
                    return false;
                }}

                for (let i = 0; i < code.length; i++) {{
                    inputs[i].focus();
                    inputs[i].value = code.charAt(i);

                    // 触发input事件
                    var event = new Event('input', {{ bubbles: true }});
                    inputs[i].dispatchEvent(event);
                }}
                return true;
            }} catch (error) {{
                console.error('输入验证码错误:', error);
                return false;
            }}
        }})();
        """

        result = self.execute_js(script)
        logger.info(f"[{self.group_id}] 验证码输入结果: {result}")
        return result

    def get_email_code(self, email):
        """从邮件中获取验证码"""
        try:
            # 准备请求参数
            request_kwargs = {
                "params": {"email": email},
                "timeout": 10
            }

            # 添加代理配置（如果启用）
            if USE_PROXY:
                request_kwargs["proxies"] = {
                    "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
                    "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
                }

            response = requests.get(f"{API_BASE_URL}{EMAIL_CODE_ENDPOINT}", **request_kwargs)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and data.get("data"):
                    logger.info(f"[{self.group_id}] 获取到邮箱验证码: {data.get('data')}")
                    return data.get("data")
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取邮箱验证码时出错: {e}")
            return None

    def get_token(self):
        """从cookies中提取WorkosCursorSessionToken"""
        script = """
        (function() {
            try {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    cookie = cookie.trim();
                    if (cookie.startsWith('WorkosCursorSessionToken=')) {
                        return cookie.substring('WorkosCursorSessionToken='.length);
                    }
                }
                return null;
            } catch (error) {
                console.error('获取token错误:', error);
                return null;
            }
        })();
        """
        token = self.execute_js(script)
        if token and isinstance(token, str):
            if '::' in token:
                token = token.split('::')[1]
            return token
        return None

    def submit_token(self, email, token):
        """将token提交到API"""
        try:
            # 准备请求参数
            request_kwargs = {
                "json": {"email": email, "token": token},
                "timeout": 10
            }

            # 添加代理配置（如果启用）
            if USE_PROXY:
                request_kwargs["proxies"] = {
                    "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
                    "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
                }

            response = requests.post(f"{API_BASE_URL}{TOKEN_SUBMIT_ENDPOINT}", **request_kwargs)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info(f"[{self.group_id}] 成功提交{email}的token")
                    return True

            logger.error(f"[{self.group_id}] 提交token失败: {response.status_code} - {response.text}")
            return False
        except Exception as e:
            logger.error(f"[{self.group_id}] 提交token时出错: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        try:
            # 从全局列表中移除
            global browser_windows
            if self.browser_window in browser_windows:
                browser_windows.remove(self.browser_window)

            # 删除配置文件目录
            import shutil
            if os.path.exists(self.profile_path):
                shutil.rmtree(self.profile_path)

            logger.info(f"[{self.group_id}] 资源清理完成")
        except Exception as e:
            logger.error(f"[{self.group_id}] 清理资源时出错: {e}")

    def is_page_loaded(self):
        """判断页面是否加载完成"""
        script = """
        (function() {
            return document.readyState === 'complete';
        })();
        """
        return self.execute_js(script)

    def process_registration(self):
        """处理注册流程"""
        if self.current_attempt >= self.max_attempts:
            logger.warning(f"[{self.group_id}] 达到最大尝试次数 {self.max_attempts}，终止注册")
            self.signals.error.emit(self.group_id, self.email, f"达到最大尝试次数 {self.max_attempts}")
            self.cleanup()
            return

        self.current_attempt += 1
        logger.info(f"[{self.group_id}] 执行注册尝试 {self.current_attempt}/{self.max_attempts}")

        # 等待3秒
        time.sleep(3)

        # 1. 判断浏览器是否加载完成
        if not self.is_page_loaded():
            logger.info(f"[{self.group_id}] 页面尚未加载完成，继续等待")
            QTimer.singleShot(2000, self.process_registration)
            return

        # 2. 查找并点击magic-code按钮
        magic_code_xpath = "//button[@name='intent' and @type='submit' and @value='magic-code' and string-length(normalize-space(text())) > 0]"
        tflag= self.check_element_exists("//div[@inert and @aria-hidden='true' and not(@class)]")
        if  not tflag and self.check_element_exists(magic_code_xpath):
            logger.info(f"[{self.group_id}] 找到magic-code按钮，点击")
            self.click_element(magic_code_xpath)
            QTimer.singleShot(3000, self.process_registration)
            return

        # 3. 检查是否存在人机验证弹框
        if tflag and self.check_element_exists("//div[@id='cf-turnstile']"):
            logger.info(f"[{self.group_id}] 检测到人机验证")
            self.handle_turnstile()
            QTimer.singleShot(3000, self.process_registration)
            return

        # 4. 检查是否存在验证码输入框
        code_input_xpath = "//input[@inputmode='numeric' and @data-index='0']"
        if self.check_element_exists(code_input_xpath):
            logger.info(f"[{self.group_id}] 检测到验证码输入框，尝试获取验证码")
            self.process_verification_code()
            return

        # 如果什么都没检测到，继续尝试
        logger.info(f"[{self.group_id}] 未检测到关键元素，继续检查")
        QTimer.singleShot(3000, self.process_registration)

    def process_verification_code(  self, attempt=0):
        """处理验证码获取和输入"""
        if attempt >= 30:
            logger.error(f"[{self.group_id}] 验证码获取失败，达到最大尝试次数")
            self.signals.error.emit(self.group_id, self.email, "验证码获取失败")
            QTimer.singleShot(2000, self.run_registration)
            return

        code = self.get_email_code(self.email)

        if not code:
            logger.info(f"[{self.group_id}] 尚未获取到验证码，3秒后重试 ({attempt + 1}/30)")
            QTimer.singleShot(3000, lambda: self.process_verification_code(attempt + 1))
            return

        logger.info(f"[{self.group_id}] 获取到验证码，开始输入")
        if not self.input_verification_code(code):
            logger.error(f"[{self.group_id}] 验证码输入失败")
            self.signals.error.emit(self.group_id, self.email, "验证码输入失败")
            QTimer.singleShot(2000, self.run_registration)
            return

        # 等待页面加载完成并获取token
        QTimer.singleShot(5000, self.get_and_submit_token)

    def get_and_submit_token(self):
        """获取并提交token"""
        current_url = self.browser.url().toString()

        if "cursor.com" not in current_url:
            logger.warning(f"[{self.group_id}] 未跳转到cursor.com，等待中")
            if not hasattr(self, '_token_attempts'):
                self._token_attempts = 0

            self._token_attempts += 1
            if self._token_attempts > 10:
                logger.error(f"[{self.group_id}] 等待跳转超时")
                self.signals.error.emit(self.group_id, self.email, "等待跳转超时")
                QTimer.singleShot(2000, self.run_registration)
            else:
                QTimer.singleShot(2000, self.get_and_submit_token)
            return

        token = self.get_token()
        if not token:
            logger.error(f"[{self.group_id}] 获取token失败")
            self.signals.error.emit(self.group_id, self.email, "获取token失败")
            QTimer.singleShot(2000, self.run_registration)
            return

        if self.submit_token(self.email, token):
            logger.info(f"[{self.group_id}] 注册成功: {self.email}")
            self.signals.finished.emit(self.group_id, self.email, token)
        else:
            logger.error(f"[{self.group_id}] 提交token失败")
            self.signals.error.emit(self.group_id, self.email, "提交token失败")

        QTimer.singleShot(2000, self.run_registration)

    def run_registration(self):
        """运行注册流程"""
        # 生成新邮箱
        self.email = self.generate_email()
        logger.info(f"[{self.group_id}] 开始新的注册流程: {self.email}")

        # 重置状态
        self.current_attempt = 0
        self.token = None
        self.code = None
        if hasattr(self, '_token_attempts'):
            del self._token_attempts

        # 加载注册页面
        encoded_email = quote(self.email)
        url = SIGNUP_URL.format(encoded_email)
        self.browser_window.load_url(url)

        # 开始处理注册
        QTimer.singleShot(2000, self.process_registration)

    def run(self):
        """开始运行实例"""
        # 初始化浏览器
        if not self.setup_browser():
            self.signals.error.emit(self.group_id, self.email, "浏览器设置失败")
            return

        # 开始注册
        self.run_registration()


class SimpleRegistrationManager:
    def __init__(self, num_threads=DEFAULT_THREADS, headless=False):
        self.num_threads = min(num_threads, MAX_THREADS)
        self.headless = headless
        self.registers = []
        self.successful = []
        self.failed = []

    def on_registration_finished(self, group_id, email, token):
        self.successful.append({
            "group_id": group_id,
            "email": email,
            "token": token,
            "timestamp": datetime.now().isoformat()
        })
        logger.info(f"注册成功: {email} (组: {group_id})")

    def on_registration_error(self, group_id, email, error):
        self.failed.append({
            "group_id": group_id,
            "email": email,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        logger.error(f"注册失败: {email} (组: {group_id}): {error}")

    def start(self):
        """开始注册进程"""
        logger.info(f"启动 {self.num_threads} 个注册线程")

        for i in range(self.num_threads):
            group_id = f"group_{i}_{int(time.time())}_{random.randint(100, 999)}"
            register = SimpleCursorAutoRegister(group_id, self.headless)

            # 连接信号
            register.signals.finished.connect(self.on_registration_finished)
            register.signals.error.connect(self.on_registration_error)

            # 保存实例
            self.registers.append(register)

            # 运行实例
            register.run()

        logger.info("所有注册线程已启动")


def main():
    # 命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="Cursor自动注册工具")
    parser.add_argument("-t", "--threads", type=int, default=DEFAULT_THREADS,
                        help=f"并行注册线程数量 (最大 {MAX_THREADS})")
    parser.add_argument("--headless", action="store_true", help="使用无头模式运行")
    parser.add_argument("--proxy", action="store_true", help="启用代理")
    args = parser.parse_args()

    # 设置代理
    global USE_PROXY
    if args.proxy:
        USE_PROXY = True
        logger.info(f"已启用代理: {PROXY_HOST}:{PROXY_PORT}")

    # 创建QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Cursor自动注册")
    app.setQuitOnLastWindowClosed(False)

    # 启动管理器
    global manager

    def start_registration():
        global manager
        manager = SimpleRegistrationManager(args.threads, args.headless)
        manager.start()
        logger.info("注册流程已启动，请勿关闭此窗口")

    QTimer.singleShot(1000, start_registration)
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 