#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试humkt邮箱获取功能
"""

import requests
import time
import logging
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_humkt_email_api():
    """测试humkt邮箱获取API"""
    try:
        logger.info("开始测试humkt邮箱获取API")
        
        # 第一步：下单获取订单ID
        buy_url = "https://www.humkt.com/api/buy?id=1514&qty=1&token=qUunKMEIzCeAXLB6YOfAfdyyRBgesJ1Q"
        logger.info(f"发送下单请求: {buy_url}")
        
        response = requests.get(buy_url, timeout=20, verify=False)
        logger.info(f"下单响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"下单接口完整响应数据:")
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应头: {dict(response.headers)}")
            logger.info(f"完整JSON响应: {result}")
            logger.info(f"响应数据类型: {type(result)}")

            if result.get("code") == 1 and result.get("data"):
                data = result["data"]
                logger.info(f"下单data字段内容: {data}")
                logger.info(f"下单data字段类型: {type(data)}")

                order_id = data["id"]
                logger.info(f"下单成功，订单ID: {order_id}")

                # 第二步：获取订单详情（循环查询）
                email_list = get_order_details(order_id)
                if email_list:
                    logger.info(f"最终获取的邮箱列表: {email_list}")
                    logger.info(f"邮箱列表类型: {type(email_list)}")

                    # 解析第一个邮箱作为示例
                    if len(email_list) > 0:
                        email_data = email_list[0]
                        logger.info(f"第一个邮箱数据: {email_data} (类型: {type(email_data)})")
                        if "|" in email_data:
                            parts = email_data.split("|")
                            if len(parts) == 2:
                                email, password = parts
                                logger.info(f"解析成功 - 邮箱: {email}, 密码: {password}")
                                logger.info(f"总共获取到 {len(email_list)} 个邮箱")
                                return email, password
                            else:
                                logger.error(f"邮箱数据格式错误，分割后有 {len(parts)} 个部分: {email_data}")
                        else:
                            logger.error(f"邮箱数据格式错误，缺少分隔符: {email_data}")
                    else:
                        logger.error("邮箱列表为空")
                else:
                    logger.error("订单详情查询失败")
            else:
                logger.error(f"下单失败")
                logger.error(f"code字段: {result.get('code')}")
                logger.error(f"message字段: {result.get('message')}")
                logger.error(f"data字段: {result.get('data')}")
        else:
            logger.error(f"下单请求失败: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
    
    return None, None

def get_order_details(order_id):
    """获取订单详情，循环查询直到有数据或超时"""
    max_attempts = 10
    retry_delay = 3
    
    for attempt in range(max_attempts):
        try:
            logger.info(f"查询订单详情 (尝试 {attempt + 1}/{max_attempts})")
            
            order_url = f"https://www.humkt.com/api/order?id={order_id}&token=qUunKMEIzCeAXLB6YOfAfdyyRBgesJ1Q"
            response = requests.get(order_url, timeout=20, verify=False)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"订单详情接口完整响应数据:")
                logger.info(f"响应状态码: {response.status_code}")
                logger.info(f"响应头: {dict(response.headers)}")
                logger.info(f"完整JSON响应: {result}")
                logger.info(f"响应数据类型: {type(result)}")

                if result.get("code") == 1 and result.get("data"):
                    data = result["data"]
                    logger.info(f"订单详情data字段内容: {data}")
                    logger.info(f"订单详情data字段类型: {type(data)}")

                    if isinstance(data, list) and len(data) > 0:
                        logger.info(f"成功获取订单详情，共 {len(data)} 个邮箱")
                        for i, email_item in enumerate(data):
                            logger.info(f"邮箱 {i+1}: {email_item} (类型: {type(email_item)})")
                        return data  # 返回整个邮箱列表
                    else:
                        logger.info(f"订单详情暂无数据，data为空或非列表，{retry_delay}秒后重试")
                        logger.info(f"data内容: {data}, 类型: {type(data)}")
                else:
                    logger.warning(f"订单详情查询返回失败")
                    logger.warning(f"code字段: {result.get('code')}")
                    logger.warning(f"message字段: {result.get('message')}")
                    logger.warning(f"data字段: {result.get('data')}")
            else:
                logger.error(f"订单详情查询请求失败: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < max_attempts - 1:
                time.sleep(retry_delay)
                
        except Exception as e:
            logger.error(f"查询订单详情异常: {e}")
            if attempt < max_attempts - 1:
                time.sleep(retry_delay)
    
    logger.error("订单详情查询失败，已达到最大重试次数")
    return None

def write_failed_email(email_data, reason):
    """将失败的邮箱账号写入文件"""
    try:
        with open("gmail_humkt_fail.txt", "a", encoding="utf-8") as f:
            timestamp = datetime.now().isoformat()
            f.write(f"{timestamp} | {email_data} | {reason}\n")
        logger.info(f"已记录失败邮箱: {email_data}")
    except Exception as e:
        logger.error(f"写入失败邮箱文件时出错: {e}")

if __name__ == "__main__":
    logger.info("=" * 50)
    logger.info("开始测试humkt邮箱获取功能")
    logger.info("=" * 50)
    
    email, password = test_humkt_email_api()
    
    if email and password:
        logger.info("=" * 50)
        logger.info("测试成功！")
        logger.info(f"获取到的邮箱: {email}")
        logger.info(f"获取到的密码: {password}")
        logger.info("=" * 50)
    else:
        logger.error("=" * 50)
        logger.error("测试失败！")
        logger.error("=" * 50)
