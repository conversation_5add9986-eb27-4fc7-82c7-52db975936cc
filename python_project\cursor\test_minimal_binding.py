#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import logging
import json
import random

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_minimal_binding():
    """最小化绑卡测试"""
    try:
        logger.info("开始最小化绑卡测试...")
        
        # 导入DrissionPage
        from DrissionPage import ChromiumPage, ChromiumOptions
        logger.info("DrissionPage导入成功")
        
        # 创建浏览器
        co = ChromiumOptions()
        co.headless(False)
        page = ChromiumPage(co)
        logger.info("浏览器创建成功")
        
        # 设置cookie
        logger.info("访问cursor.com并设置cookie...")
        page.get("https://cursor.com")
        time.sleep(3)
        
        cookie_value = "user_01K130TGCZZVKV8TK55VHEHYA6%3A%3AeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzEzMFRHQ1paVktWOFRLNTVWSEVIWUE2IiwidGltZSI6IjE3NTM1MjE5NjYiLCJyYW5kb21uZXNzIjoiMjA2N2M5MTktYzg3Yy00ZmUxIiwiZXhwIjoxNzU4NzA1OTY2LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoid2ViIn0.os3g0uPBe5fAZjrsYFE2WaNvAMs8UAuinaY-ft3RV40"
        
        page.set.cookies([{
            'name': 'WorkosCursorSessionToken',
            'value': cookie_value,
            'domain': '.cursor.com',
            'path': '/',
            'secure': True,
            'httpOnly': True
        }])
        logger.info("Cookie设置完成")
        
        # 访问trial页面
        logger.info("访问trial页面...")
        page.get("https://cursor.com/cn/trial")
        time.sleep(5)
        
        logger.info(f"当前URL: {page.url}")
        logger.info(f"页面标题: {page.title}")
        
        # 查找所有按钮
        logger.info("查找页面按钮...")
        buttons = page.eles("tag:button")
        logger.info(f"找到 {len(buttons)} 个按钮")
        
        # 显示按钮文本
        button_texts = []
        for i, button in enumerate(buttons):
            try:
                text = button.text.strip()
                if text:
                    button_texts.append(text)
                    logger.info(f"按钮 {i+1}: '{text}'")
            except Exception as e:
                logger.warning(f"获取按钮 {i+1} 文本失败: {e}")
        
        # 查找试用相关按钮
        logger.info("查找试用相关按钮...")
        trial_keywords = ['试用', 'trial', 'start', 'continue', '开始', '继续']
        
        for i, button in enumerate(buttons):
            try:
                text = button.text.strip().lower()
                if any(keyword in text for keyword in trial_keywords):
                    logger.info(f"找到可能的试用按钮: '{button.text.strip()}'")
                    logger.info("尝试点击...")
                    button.click()
                    time.sleep(5)
                    
                    new_url = page.url
                    logger.info(f"点击后URL: {new_url}")
                    
                    if "stripe.com" in new_url or "checkout.stripe.com" in new_url:
                        logger.info("✅ 成功跳转到Stripe绑卡页面！")
                        break
                    
                    # 检查是否有Continue按钮
                    continue_button = page.ele("xpath://button[text()='Continue']", timeout=3)
                    if continue_button:
                        logger.info("发现Continue按钮，点击...")
                        continue_button.click()
                        time.sleep(5)
                        
                        final_url = page.url
                        logger.info(f"Continue后URL: {final_url}")
                        
                        if "stripe.com" in final_url or "checkout.stripe.com" in final_url:
                            logger.info("✅ 通过Continue按钮成功跳转到Stripe页面！")
                            break
            except Exception as e:
                logger.warning(f"处理按钮时出错: {e}")
        
        # 检查最终状态
        final_url = page.url
        logger.info(f"最终URL: {final_url}")
        
        if "stripe.com" in final_url or "checkout.stripe.com" in final_url:
            logger.info("🎉 成功到达绑卡页面！")
            
            # 简单测试绑卡表单
            logger.info("测试绑卡表单...")
            
            # 点击Continue按钮（如果存在）
            continue_button = page.ele("xpath://button[text()='Continue']", timeout=5)
            if continue_button:
                logger.info("点击Continue按钮")
                continue_button.click()
                time.sleep(3)
            
            # 点击信用卡选项
            card_button = page.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=5)
            if card_button:
                logger.info("点击信用卡选项")
                card_button.click()
                time.sleep(3)
            
            # 检查表单是否加载
            card_number_input = page.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                logger.info("✅ 信用卡表单加载成功！")
                
                # 加载一张测试卡
                try:
                    with open('ChinaUnionPay.json', 'r', encoding='utf-8') as f:
                        cards = json.load(f)
                    
                    if cards:
                        card = random.choice(cards)
                        logger.info(f"使用测试卡: {card['Name']} - {card['CardNumber']}")
                        
                        # 填充卡号
                        card_number_input.clear()
                        card_number_input.input(str(card['CardNumber']))
                        logger.info("已填充卡号")
                        
                        # 填充有效期
                        expiry_input = page.ele("xpath://input[@id='cardExpiry']", timeout=5)
                        if expiry_input:
                            expiry_input.clear()
                            expiry_input.input(card['Expiry'])
                            logger.info("已填充有效期")
                        
                        # 填充CVV
                        cvc_input = page.ele("xpath://input[@id='cardCvc']", timeout=5)
                        if cvc_input:
                            cvc_input.clear()
                            cvc_input.input(str(card['CVV']))
                            logger.info("已填充CVV")
                        
                        logger.info("✅ 基本信息填充完成！")
                    else:
                        logger.error("ChinaUnionPay.json文件为空")
                        
                except Exception as e:
                    logger.error(f"加载卡信息失败: {e}")
            else:
                logger.error("信用卡表单未加载")
        else:
            logger.warning("未能跳转到绑卡页面")
        
        # 保持浏览器打开
        logger.info("测试完成，浏览器保持打开...")
        input("按Enter键关闭浏览器...")
        page.quit()
        
    except Exception as e:
        logger.error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_minimal_binding()
