# 地区配置说明

## 🌍 支持的地区配置

### 当前配置：香港 (hongkong)
```python
REGION_CONFIG = 'hongkong'  # 匹配您的代理IP: *************
```

## 📋 各地区配置详情

### 🇭🇰 香港配置 (hongkong)
- **User-Agent**: 6个 Chrome 109 版本
- **时区**: `Asia/Hong_Kong`
- **语言**: `zh-HK,zh;q=0.9,en;q=0.8` 和 `en-HK,en;q=0.9,zh;q=0.8`
- **Navigator语言**: `zh-HK` 优先，支持中文和英文

### 🇯🇵 日本配置 (japan)
- **User-Agent**: 8个 Chrome 109 版本
- **时区**: `Asia/Tokyo`
- **语言**: `ja-JP,ja;q=0.9,en;q=0.8`
- **Navigator语言**: `ja-JP` 优先

### 🇺🇸 美国配置 (usa)
- **User-Agent**: 7个 Chrome 109 版本（包含Linux）
- **时区**: 4个美国时区（东部、西部、中部、山地）
- **语言**: `en-US,en;q=0.9`
- **Navigator语言**: `en-US` 优先

## 🔧 如何切换地区

### 方法1：修改配置文件
编辑 `drission_cursor_reg_2925_allproxy.py` 第44行：
```python
# 切换到日本
REGION_CONFIG = 'japan'

# 切换到美国
REGION_CONFIG = 'usa'

# 切换到香港
REGION_CONFIG = 'hongkong'
```

### 方法2：根据代理IP自动选择
建议根据您获取的代理IP地区来设置：
- 香港IP → `hongkong`
- 日本IP → `japan`
- 美国IP → `usa`

## 🛡️ 反检测优化

### 针对您的检测结果优化：
1. **地区一致性**: 所有浏览器特征与代理IP地区保持一致
2. **语言匹配**: Navigator语言与Accept-Language完全匹配
3. **时区同步**: 浏览器时区与代理IP地区时区一致
4. **增强隐藏**: 添加了更多反自动化检测参数

### 新增的反检测功能：
- 禁用自动化控制特征
- 隐藏webdriver属性
- 模拟真实硬件信息
- 模拟4G网络连接
- 删除Chrome自动化标识

## 📊 预期改善效果

使用香港配置后，应该能够：
- ✅ 提高IP信誉分数（60分 → 80分+）
- ✅ 减少VPN/代理检测概率
- ✅ 增强浏览器指纹一致性
- ✅ 降低自动化检测风险

## 🔍 测试建议

1. **重新测试**: 使用 https://iplark.com/check 重新检测
2. **多次测试**: 测试3-5次取平均分数
3. **对比测试**: 可以尝试不同地区配置对比效果
4. **实际使用**: 在实际注册流程中验证效果

## ⚠️ 注意事项

- 确保代理IP地区与配置地区匹配
- 如果更换不同地区的代理，记得同步更新配置
- 建议定期更换代理IP以避免IP被标记
