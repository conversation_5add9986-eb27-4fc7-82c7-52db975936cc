#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮箱解析功能
"""

import json
import threading
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 模拟全局邮箱池
_email_pool = []
_email_pool_lock = threading.Lock()

def write_failed_email_static(email_data, reason):
    """静态方法：将失败的邮箱账号写入文件"""
    try:
        with open("gmail_humkt_fail.txt", "a", encoding="utf-8") as f:
            timestamp = datetime.now().isoformat()
            f.write(f"{timestamp} | {email_data} | {reason}\n")
        logger.info(f"已记录失败邮箱: {email_data}")
    except Exception as e:
        logger.error(f"写入失败邮箱文件时出错: {e}")

def add_emails_to_pool(email_list):
    """将邮箱列表添加到邮箱池"""
    global _email_pool, _email_pool_lock
    
    with _email_pool_lock:
        logger.info(f"开始处理邮箱列表，共 {len(email_list)} 个邮箱")
        for i, email_data in enumerate(email_list):
            logger.info(f"处理第 {i+1} 个邮箱: {email_data}")
            if "|" in email_data:
                parts = email_data.split("|")
                if len(parts) == 2:
                    email, password = parts
                    _email_pool.append({
                        'email': email,
                        'password': password
                    })
                    logger.info(f"成功添加邮箱到池: {email}")
                else:
                    logger.error(f"邮箱数据格式错误，分割后有 {len(parts)} 个部分: {email_data}")
                    # 记录格式错误的邮箱
                    write_failed_email_static(email_data, "邮箱数据格式错误")
            else:
                logger.error(f"邮箱数据缺少分隔符: {email_data}")
                # 记录格式错误的邮箱
                write_failed_email_static(email_data, "邮箱数据缺少分隔符")
        
        logger.info(f"邮箱池更新完成，当前池中邮箱数量: {len(_email_pool)}")

def get_email_from_pool():
    """从邮箱池中获取一个邮箱"""
    global _email_pool, _email_pool_lock
    
    with _email_pool_lock:
        if _email_pool:
            email_data = _email_pool.pop(0)  # 取出第一个邮箱
            logger.info(f"从邮箱池中获取邮箱: {email_data['email']} (池中剩余: {len(_email_pool)})")
            return email_data
        else:
            logger.info("邮箱池为空")
            return None

def test_email_parsing():
    """测试邮箱解析功能"""
    logger.info("=" * 60)
    logger.info("开始测试邮箱解析功能")
    logger.info("=" * 60)
    
    # 模拟你提供的实际返回数据
    response_data = {
        "message": "请求成功",
        "code": 1,
        "data": [
            "<EMAIL>|Nxnt3979",
            "<EMAIL>|Nxnt3979"
        ]
    }
    
    logger.info(f"模拟API返回数据: {json.dumps(response_data, ensure_ascii=False)}")
    
    # 提取邮箱列表
    if response_data.get("code") == 1 and response_data.get("data"):
        email_list = response_data["data"]
        logger.info(f"提取到邮箱列表，共 {len(email_list)} 个邮箱")
        
        # 添加到邮箱池
        add_emails_to_pool(email_list)
        
        # 测试从池中获取邮箱
        logger.info("\n" + "=" * 40)
        logger.info("测试从邮箱池获取邮箱")
        logger.info("=" * 40)
        
        # 获取第一个邮箱
        email1 = get_email_from_pool()
        if email1:
            logger.info(f"第1次获取: {email1['email']} | {email1['password']}")
        
        # 获取第二个邮箱
        email2 = get_email_from_pool()
        if email2:
            logger.info(f"第2次获取: {email2['email']} | {email2['password']}")
        
        # 尝试获取第三个邮箱（应该为空）
        email3 = get_email_from_pool()
        if email3:
            logger.info(f"第3次获取: {email3['email']} | {email3['password']}")
        else:
            logger.info("第3次获取: 邮箱池已空")
    
    logger.info("\n" + "=" * 60)
    logger.info("邮箱解析测试完成")
    logger.info("=" * 60)

if __name__ == "__main__":
    test_email_parsing()
