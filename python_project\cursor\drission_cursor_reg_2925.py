from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import signal
import random
import logging
import json
import sys
from urllib.parse import quote
import requests
from datetime import datetime
import threading
import psutil
from colorama import Fore, Style
import argparse
from DrissionPage.errors import ElementLostError
import uuid
import base64
import hashlib
import secrets
# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cursor_registration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
SIGNUP_URL = "https://authenticator.cursor.sh/password?email={}&state={}&redirect_uri=https://cursor.com/api/auth/callback"
API_BASE_URL = "http://*************:8080/admin-api"  # 修改为你的实际API端点
EMAIL_CODE_ENDPOINT = "/cqp/cursoremailpool/getEmailCode?token=fe84864d7d354b53ae65e9fee25b0067"
TOKEN_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/saveRecord?token=fe84864d7d354b53ae65e9fee25b0067"

# 代理配置
USE_PROXY = True  # 是否使用代理
# PROXY_API_URL = "https://api.huashengproxy.com/get_ip?uid=18079&num=1&format=2&protocol=http&region=hk&separator=3"
PROXY_API_URL = "https://overseas.proxy.qg.net/get?key=EB914784&num=1&keep_alive=5&area=990400"
PROXY_LIFETIME_MINUTES = 5  # 每个代理的有效时间（分钟）

# 控制注册线程数量的常量
DEFAULT_THREADS = 1  # 默认并发线程数
MAX_THREADS = 20  # 最大并发线程数
DEFAULT_REGISTRATION_COUNT = 1000  # 每个线程默认注册次数

# 全局变量，用于存储浏览器进程ID
_chrome_process_ids = []

class DrissionCursorRegister:
    def __init__(self, group_id, headless=False):
        self.group_id = group_id
        self.headless = headless
        self.email = None
        self.page = None
        self.token = None
        self.code = None
        self.profile_path = f"chrome-data-cursor/group_{group_id}"  # 简化命名为固定格式
        self.max_attempts = 10  # 最大尝试次数
        self.current_attempt = 0
        self.before_pids = []
        self.success_callback = None
        self.error_callback = None
        self.proxy_host = None
        self.proxy_port = None
        self.proxy_start_time = None  # 记录代理获取的时间
        # OAuth相关参数
        self.uuid = None
        self.challenge = None
        self.verifier = None
        
    def set_callbacks(self, success_callback, error_callback):
        """设置回调函数"""
        self.success_callback = success_callback
        self.error_callback = error_callback

    def generate_oauth_params(self):
        """生成OAuth相关参数：uuid、challenge、verifier"""
        try:
            # 生成UUID
            self.uuid = str(uuid.uuid4())
            logger.info(f"[{self.group_id}] 生成UUID: {self.uuid}")

            # 生成code_verifier (32字节随机数，Base64编码)
            random_bytes = secrets.token_bytes(32)
            self.verifier = base64.urlsafe_b64encode(random_bytes).decode('utf-8').rstrip('=')
            logger.info(f"[{self.group_id}] 生成verifier: {self.verifier}")

            # 生成code_challenge (SHA-256哈希 + Base64编码)
            sha256_hash = hashlib.sha256(self.verifier.encode('utf-8')).digest()
            self.challenge = base64.urlsafe_b64encode(sha256_hash).decode('utf-8').rstrip('=')
            logger.info(f"[{self.group_id}] 生成challenge: {self.challenge}")

            return True
        except Exception as e:
            logger.error(f"[{self.group_id}] 生成OAuth参数失败: {e}")
            return False

    def get_access_token_and_refresh_token(self, token):
        """根据token获取accessToken和refreshToken"""
        try:
            logger.info(f"[{self.group_id}] 开始获取accessToken和refreshToken")

            # 处理token格式
            if not "user_01" in token:
                # 这里简化处理，实际可能需要JWT解析
                # 假设token格式需要调整
                processed_token = f"user_01::{token}"
            else:
                processed_token = token

            # URL编码处理
            encoded_token = quote(processed_token, safe='')

            # 调用API获取tokens
            api_url = f"https://api2.cursor.sh/auth/poll?uuid={self.uuid}&verifier={self.verifier}"
            logger.info(f"[{self.group_id}] 调用API: {api_url}")

            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"[{self.group_id}] API响应: {result}")

                # 提取accessToken和refreshToken
                access_token = result.get('accessToken')
                refresh_token = result.get('refreshToken')

                if access_token and refresh_token:
                    logger.info(f"[{self.group_id}] 成功获取tokens")
                    return access_token, refresh_token
                else:
                    logger.error(f"[{self.group_id}] API响应中未找到tokens")
                    return None, None
            else:
                logger.error(f"[{self.group_id}] API请求失败: {response.status_code} - {response.text}")
                return None, None

        except Exception as e:
            logger.error(f"[{self.group_id}] 获取tokens时出错: {e}")
            return None, None
        
    def generate_email(self):
        """生成随机邮箱地址"""
        timestamp = int(time.time())
        return f"a965586934{timestamp}@2925.com"
    
    def get_new_proxy(self):
        """从API获取新的代理IP"""
        logger.info(f"[{self.group_id}] 正在获取新代理...")
        try:
            response = requests.get(PROXY_API_URL, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                    proxy_info = data["data"][0]
                    server = proxy_info.get("server", "")
                    if ":" in server:
                        self.proxy_host, self.proxy_port = server.split(":")
                        # 设置代理获取时间
                        self.proxy_start_time = time.time()
                        logger.info(f"[{self.group_id}] 成功获取新代理: {self.proxy_host}:{self.proxy_port}")
                        return True
                    else:
                        logger.error(f"[{self.group_id}] 代理服务器格式错误: {server}")
                else:
                    logger.error(f"[{self.group_id}] 获取代理失败: {data}")
            else:
                logger.error(f"[{self.group_id}] 获取代理请求失败: {response.status_code}")
            return False
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取代理时发生异常: {e}")
            return False

    def test_proxy(self, max_attempts=60):
        """测试当前代理是否可用，如果不可用则重新获取，直到可用或达到最大尝试次数"""
        # 首先检查代理是否过期
        if self.is_proxy_expired():
            logger.info(f"[{self.group_id}] 当前代理已过期，尝试获取新代理")
            if self.get_new_proxy():
                logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.error(f"[{self.group_id}] 获取新代理失败")
                return False

        for attempt in range(1, max_attempts + 1):
            try:
                test_url = "https://www.google.com"
                test_proxies = {
                    "http": f"http://{self.proxy_host}:{self.proxy_port}",
                    "https": f"http://{self.proxy_host}:{self.proxy_port}"
                }
                response = requests.get(test_url, proxies=test_proxies, timeout=5)
                if response.status_code == 200:
                    logger.info(f"[{self.group_id}] 代理测试成功")
                    return True
                else:
                    logger.error(f"[{self.group_id}] 代理测试失败: {response.status_code}")
            except Exception as e:
                logger.error(f"[{self.group_id}] 代理测试异常: {e}")

            # 如果测试失败且未达到最大尝试次数，则重新获取代理
            if attempt < max_attempts:
                logger.info(f"[{self.group_id}] 尝试获取新代理 (尝试 {attempt}/{max_attempts})")
                if self.get_new_proxy():
                    logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
                    continue
                else:
                    logger.error(f"[{self.group_id}] 获取新代理失败")

        logger.error(f"[{self.group_id}] 多次尝试后未能获取可用代理")
        return False
    def setup_browser(self, use_proxy=False):
        """设置浏览器，优化启动速度"""
        logger.info(f"[{self.group_id}] 开始设置浏览器...")

        # 先尝试删除已存在的目录
        try:
            if os.path.exists(self.profile_path):
                import shutil
                shutil.rmtree(self.profile_path)
                logger.info(f"[{self.group_id}] 已删除旧的浏览器配置文件夹: {self.profile_path}")
        except Exception as e:
            logger.warning(f"[{self.group_id}] 删除旧的浏览器配置文件夹时出错: {e}")

        # 创建新的目录
        os.makedirs(self.profile_path, exist_ok=True)

        try:
            # 记录启动前的浏览器进程
            self.before_pids = self.get_browser_processes()

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 设置用户数据目录
            co.set_user_data_path(self.profile_path)

            # 优化浏览器启动参数 - 提高性能，减少资源使用
            co.set_argument("--incognito")  # 隐身模式
            co.set_argument("--disable-extensions")  # 禁用扩展
            co.set_argument("--disable-gpu")  # 禁用GPU加速
            co.set_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
            co.set_argument("--disable-infobars")  # 禁用信息栏
            co.set_argument("--disable-browser-side-navigation")  # 禁用浏览器侧导航
            co.set_argument("--disable-features=TranslateUI,BlinkGenPropertyTrees")  # 禁用翻译UI等
            co.set_argument("--disable-notifications")  # 禁用通知
            co.set_argument("--disable-popup-blocking")  # 禁用弹窗阻止
            co.set_argument("--disable-background-timer-throttling")  # 禁用后台计时器限制
            co.set_argument("--disable-backgrounding-occluded-windows")  # 禁用背景窗口
            co.set_argument("--disable-breakpad")  # 禁用崩溃报告
            co.set_argument("--disable-client-side-phishing-detection")  # 禁用钓鱼检测
            co.set_argument("--disable-default-apps")  # 禁用默认应用
            co.set_argument("--disable-hang-monitor")  # 禁用挂起监视器
            co.set_argument("--disable-prompt-on-repost")  # 禁用重新发布提示
            co.set_argument("--disable-sync")  # 禁用同步
            co.set_argument("--no-first-run")  # 禁止首次运行
            co.set_argument("--no-default-browser-check")  # 禁止默认浏览器检查
            co.set_argument("--disable-webrtc")  # 禁用WebRTC
            co.set_argument("--enforce-webrtc-ip-permission-check")  # 强制WebRTC IP权限检查

            # 设置代理（如果启用）
            if use_proxy and USE_PROXY and self.proxy_host and self.proxy_port:
                co.set_proxy(f"{self.proxy_host}:{self.proxy_port}")
                self.test_proxy()
                logger.info(f"[{self.group_id}] 已设置浏览器代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.info(f"[{self.group_id}] 当前不使用代理")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动浏览器
            start_time = time.time()
            logger.info(f"[{self.group_id}] 启动浏览器")
            self.page = ChromiumPage(co)

            # 等待浏览器准备就绪
            time.sleep(1)

            # 显示启动用时
            elapsed = time.time() - start_time
            logger.info(f"[{self.group_id}] 浏览器启动完成，用时 {elapsed:.2f} 秒")

            # 记录新增的浏览器进程
            self.track_browser_processes()

            return True

        except Exception as e:
            logger.exception(f"[{self.group_id}] 设置浏览器时出现异常: {e}")
            return False

    def get_default_browser_path(self):
        """获取默认浏览器路径"""
        # 简化示例，实际使用时可以依据不同操作系统获取更精确的路径
        if sys.platform == "win32":
            return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        elif sys.platform == "darwin":
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            return "/usr/bin/google-chrome"

    def get_browser_processes(self):
        """获取当前浏览器进程ID列表"""
        try:
            browser_process_names = ['chrome', 'chromium', 'msedge', 'edge', 'brave']
            return [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in browser_process_names)]
        except:
            return []

    def track_browser_processes(self):
        """跟踪新启动的浏览器进程"""
        global _chrome_process_ids
        try:
            after_pids = self.get_browser_processes()
            new_pids = [pid for pid in after_pids if pid not in self.before_pids]
            _chrome_process_ids.extend(new_pids)

            if new_pids:
                logger.info(f"[{self.group_id}] 跟踪到 {len(new_pids)} 个新浏览器进程")
            else:
                logger.warning(f"[{self.group_id}] 未检测到新的浏览器进程")
        except Exception as e:
            logger.error(f"[{self.group_id}] 跟踪进程失败: {e}")

    def cleanup(self):
        """清理资源，更彻底地清理浏览器"""
        try:
            # 关闭浏览器
            if self.page:
                # 先清除浏览器缓存
                try:
                    self.page.run_cdp('Network.clearBrowserCache')
                    self.page.run_cdp('Network.clearBrowserCookies')
                    self.page.run_cdp('Storage.clearDataForOrigin', {'origin': '*', 'storageTypes': 'all'})
                    logger.info(f"[{self.group_id}] 已清除浏览器缓存和Cookie")
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 清除缓存失败: {e}")

                # 关闭浏览器
                self.page.quit()
                time.sleep(1)

            # 强制结束可能残留的进程
            try:
                for pid in self.get_browser_processes():
                    if pid in self.before_pids:
                        continue  # 跳过启动前已存在的进程
                    if sys.platform == "win32":
                        os.system(f'taskkill /F /PID {pid} /T >nul 2>&1')
                    else:
                        try:
                            os.kill(pid, signal.SIGKILL)
                        except:
                            pass
                logger.info(f"[{self.group_id}] 已尝试强制结束残留进程")
            except Exception as e:
                logger.warning(f"[{self.group_id}] 结束残留进程出错: {e}")

            # 删除配置文件目录
            try:
                import shutil
                if os.path.exists(self.profile_path):
                    shutil.rmtree(self.profile_path)
                    logger.info(f"[{self.group_id}] 已删除浏览器配置目录")
                os.makedirs(self.profile_path, exist_ok=True)
            except Exception as e:
                logger.error(f"[{self.group_id}] 删除配置目录时出错: {e}")

            logger.info(f"[{self.group_id}] 资源清理完成")
        except Exception as e:
            logger.error(f"[{self.group_id}] 清理资源时出错: {e}")

    def handle_turnstile(self):
        """处理 Turnstile 验证"""
        logger.info(f"[{self.group_id}] 开始处理 Turnstile 人机验证...")

        # 等待turnstile元素出现
        turnstile = self.page.ele("@id=cf-turnstile", timeout=5)
        if not turnstile:
            logger.error(f"[{self.group_id}] 未找到 cf-turnstile 元素")
            return False

        try:
            # 将turnstile元素滚动到视图中并点击以获取焦点
            self.page.run_js("arguments[0].scrollIntoView(true);", turnstile)
            turnstile.click()
            logger.info(f"[{self.group_id}] 已点击Turnstile容器获取焦点")
            time.sleep(1)

            # 发送Tab键聚焦到复选框，然后发送空格键进行点击
            self.page.actions.key_down('TAB')
            self.page.actions.key_up('TAB')
            time.sleep(1)
            self.page.actions.key_down('space')
            self.page.actions.key_up('space')
            logger.info(f"[{self.group_id}] 已发送Tab和Space键")
            time.sleep(2)

            # 检查验证结果
            if self.check_verification_success():
                logger.info(f"[{self.group_id}] Turnstile 验证通过")
                return True

            logger.warning(f"[{self.group_id}] Turnstile 验证未通过")
            return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 处理验证时出错: {e}")
            return False

    def check_verification_success(self):
        """检查验证是否成功"""
        try:
            # 检查是否存在后续表单元素
            if (self.page.ele("@name=password", timeout=0.5) or
                self.page.ele("@name=email", timeout=0.5) or
                self.page.ele("@data-index=0", timeout=0.5) or
                self.page.ele("Account Settings", timeout=0.5)):
                return True

            # 检查是否有错误信息
            error_xpaths = [
                'xpath://div[contains(text(), "Can\'t verify the user is human")]',
                'xpath://div[contains(text(), "Error: 600010")]',
                'xpath://div[contains(text(), "Please try again")]'
            ]

            for xpath in error_xpaths:
                if self.page.ele(xpath):
                    return False

            return False
        except:
            return False

    def input_verification_code(self, code):
        """输入验证码"""
        if not code or len(code) != 6:
            logger.error(f"[{self.group_id}] 无效的验证码: {code}")
            return False

        try:
            # 查找验证码输入框
            for i, digit in enumerate(code):
                input_ele = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='" + str(i) + "']")
                if input_ele:
                    input_ele.input(digit)
                    time.sleep(0.2)
                else:
                    logger.error(f"[{self.group_id}] 未找到第{i}个验证码输入框")
                    return False

            logger.info(f"[{self.group_id}] 验证码输入成功")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 输入验证码出错: {e}")
            return False

    def get_email_code(self, email):
        """从邮件中获取验证码"""
        try:
            # 准备请求参数
            request_kwargs = {
                "params": {"email": email},
                "timeout": 10
            }

            # 添加代理配置（如果启用）
            # if USE_PROXY:
            #     request_kwargs["proxies"] = {
            #         "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
            #         "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
            #     }

            response = requests.get(f"{API_BASE_URL}{EMAIL_CODE_ENDPOINT}", **request_kwargs)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and data.get("data"):
                    logger.info(f"[{self.group_id}] 获取到邮箱验证码: {data.get('data')}")
                    return data.get("data")
                else:
                    logger.error(f"[{self.group_id}] 获取邮箱"+email+"验证码失败: {data.get('message')}")
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取邮箱验证码时出错: {e}")
            return None

    def get_token(self):
        """从cookies中提取WorkosCursorSessionToken"""
        try:
            cookies = self.page.cookies()
            for cookie in cookies:
                if cookie.get('name') == 'WorkosCursorSessionToken':
                    token = cookie.get('value')
                    # if token and '::' in token:
                    #     return token.split('::')[1]
                    return token
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取token出错: {e}")
            return None

    def submit_token(self, email, token, access_token=None, refresh_token=None):
        """将token提交到API（异步方式）"""
        # 创建异步线程执行提交操作
        submit_thread = threading.Thread(
            target=self._async_submit_token,
            args=(email, token, access_token, refresh_token),
            daemon=True
        )
        submit_thread.start()
        # 立即返回，不等待提交完成
        return True

    def _async_submit_token(self, email, token, access_token=None, refresh_token=None):
        """异步执行token提交和日志记录"""
        try:
            # 准备请求参数
            submit_data = {
                "email": email,
                "token": token
            }

            # 添加accessToken和refreshToken（如果有）
            if access_token:
                submit_data["accessToken"] = access_token
            if refresh_token:
                submit_data["refreshToken"] = refresh_token

            request_kwargs = {
                "json": submit_data,
                "timeout": 20
            }

            # 添加代理配置（如果启用）
            # if USE_PROXY:
            #     request_kwargs["proxies"] = {
            #         "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
            #         "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
            #     }

            response = requests.post(f"{API_BASE_URL}{TOKEN_SUBMIT_ENDPOINT}", **request_kwargs)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info(f"[{self.group_id}] 成功提交{email}的token和tokens (异步)")
                    if self.success_callback:
                        self.success_callback(self.group_id, email, token)
                    return

            logger.error(f"[{self.group_id}] 提交token失败: {response.status_code} - {response.text}")
            if self.error_callback:
                self.error_callback(self.group_id, email, "提交token失败")
        except Exception as e:
            logger.error(f"[{self.group_id}] 提交token时出错: {e}")
            if self.error_callback:
                self.error_callback(self.group_id, email, f"提交token异常: {str(e)}")

    def process_registration(self):
        """处理注册流程"""
        if self.current_attempt >= self.max_attempts:
            logger.warning(f"[{self.group_id}] 达到最大尝试次数 {self.max_attempts}，终止当前注册")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, f"达到最大尝试次数 {self.max_attempts}")
            # 重置为无代理模式以便下次注册
            try:
                if self.page:
                    self.page.quit()
                    time.sleep(2)
                self.setup_browser(use_proxy=False)
                logger.info(f"[{self.group_id}] 尝试失败后已重置为无代理模式")
            except Exception as e:
                logger.error(f"[{self.group_id}] 重置为无代理模式失败: {e}")
            return False

        self.current_attempt += 1
        logger.info(f"[{self.group_id}] 执行注册尝试 {self.current_attempt}/{self.max_attempts}")

        # 等待页面加载
        time.sleep(3)
        has_overlay = bool(self.page.ele('xpath://div[@inert and @aria-hidden="true" and not(@class)]',timeout=1))
        # 1. 查找并点击magic-code按钮

        if not has_overlay:
            magic_code_button = self.page.ele("xpath://button[@name='intent' and @type='submit' and @value='magic-code' and not(@data-disabled='true')]",timeout=1)
            if bool(magic_code_button):
                logger.info(f"[{self.group_id}] 找到magic-code按钮，点击")
                try:
                    magic_code_button.click()
                except ElementLostError:
                    logger.info(f"[{self.group_id}] 点击按钮后元素失效，属正常跳转，已忽略")
                time.sleep(3)

        # 2. 检查是否存在人机验证
        turnstile = self.page.ele("@id=cf-turnstile", timeout=1)
        if has_overlay :
            if bool(turnstile):
                logger.info(f"[{self.group_id}] 检测到人机验证")
                self.handle_turnstile()
                time.sleep(3)

        # 3. 检查是否存在验证码输入框
        code_input = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='0']", timeout=1)
        if bool(code_input):
            logger.info(f"[{self.group_id}] 检测到验证码输入框，尝试获取验证码")
            self.process_verification_code()
            return True

        # 等待一段时间后继续尝试
        time.sleep(2)
        return self.process_registration()

    def process_verification_code(self, attempt=0):
        """处理验证码获取和输入"""
        if attempt >= 30:
            logger.error(f"[{self.group_id}] 验证码获取失败，达到最大尝试次数")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "验证码获取失败")
            return

        code = self.get_email_code(self.email)

        if not code:
            logger.info(f"[{self.group_id}] 尚未获取到验证码，3秒后重试 ({attempt + 1}/30)")
            time.sleep(3)
            self.process_verification_code(attempt + 1)
            return

        logger.info(f"[{self.group_id}] 获取到验证码，准备重启浏览器")

        # 保存当前URL和验证码
        current_url = self.page.url

        # 获取新代理，只有当当前代理过期时才获取
        if USE_PROXY:
            # 检查当前代理是否过期
            if self.is_proxy_expired():
                logger.info(f"[{self.group_id}] 当前代理已过期，正在获取新的代理IP...")
                self.get_new_proxy()
                logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.info(f"[{self.group_id}] 当前代理仍在有效期内，继续使用当前代理")

        # 关闭浏览器
        if self.page:
            self.page.quit()
            time.sleep(2)

        # 重启浏览器并启用代理
        if not self.setup_browser(use_proxy=True):
            logger.error(f"[{self.group_id}] 重启浏览器失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "重启浏览器失败")
            return

        # 重新访问页面
        self.page.get(current_url)
        time.sleep(3)

        # 输入验证码
        logger.info(f"[{self.group_id}] 启用代理后，开始输入验证码")
        if not self.input_verification_code(code):
            logger.error(f"[{self.group_id}] 验证码输入失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "验证码输入失败")
            return

        # 等待页面加载完成
        time.sleep(5)

        # 检查是否跳转到登录确认页面
        current_url = self.page.url
        if "loginDeepControl" in current_url:
            logger.info(f"[{self.group_id}] 检测到登录确认页面，查找登录按钮")
            # 查找并点击登录按钮
            login_button = self.page.ele("xpath://button[.//span[contains(text(), 'Log In')]]", timeout=10)
            if login_button:
                logger.info(f"[{self.group_id}] 找到登录按钮，点击确认")
                login_button.click()
                time.sleep(5)
            else:
                logger.warning(f"[{self.group_id}] 未找到登录按钮")

        # 获取并提交token
        self.get_and_submit_token()

    def get_and_submit_token(self):
        """获取并提交token"""
        try:
            # 检查是否跳转到cursor.com
            current_url = self.page.url

            # 获取token
            time.sleep(5)
            token = self.get_token()
            if not token:
                logger.error(f"[{self.group_id}] 获取token失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "获取token失败")
                return

            # 获取accessToken和refreshToken
            access_token, refresh_token = self.get_access_token_and_refresh_token(token)

            if access_token and refresh_token:
                logger.info(f"[{self.group_id}] 成功获取accessToken和refreshToken")
                # 提交token和tokens到API (异步)
                self.submit_token(self.email, token, access_token, refresh_token)
                logger.info(f"[{self.group_id}] 注册流程完成: {self.email} (token和tokens提交已触发异步处理)")
            else:
                logger.warning(f"[{self.group_id}] 未能获取accessToken和refreshToken，仅提交原token")
                # 仅提交原token到API (异步)
                self.submit_token(self.email, token)
                logger.info(f"[{self.group_id}] 注册流程完成: {self.email} (仅token提交已触发异步处理)")

        except Exception as e:
            logger.error(f"[{self.group_id}] token处理过程中出错: {e}")
        finally:
            # 清理资源并重启为无代理模式，为下一轮注册做准备
            try:
                logger.info(f"[{self.group_id}] 当前注册流程已完成，清理资源并准备下一轮")
                
                # 使用cleanup彻底清理当前浏览器
                self.cleanup()
                
                # 重启无代理浏览器
                time.sleep(1)  # 等待资源释放
                if self.setup_browser(use_proxy=False):
                    logger.info(f"[{self.group_id}] 已重启浏览器为无代理模式，准备下一轮注册")
                else:
                    logger.error(f"[{self.group_id}] 重启无代理浏览器失败")
            except Exception as e:
                logger.error(f"[{self.group_id}] 重置浏览器过程中出错: {e}")
                # 即使出错也尝试再次启动
                try:
                    time.sleep(2)  # 多等待一会儿
                    self.setup_browser(use_proxy=False)
                    logger.info(f"[{self.group_id}] 二次尝试重启浏览器成功")
                except:
                    logger.error(f"[{self.group_id}] 二次尝试重启浏览器也失败，将在下一轮注册时重试")
        
    def run(self):
        """运行注册流程"""
        # 初始化浏览器（无代理模式）
        if not self.setup_browser(use_proxy=False):
            logger.error(f"[{self.group_id}] 浏览器设置失败，线程终止")
            if self.error_callback:
                self.error_callback(self.group_id, "None", "浏览器初始化失败")
            return
            
        logger.info(f"[{self.group_id}] 浏览器初始化成功（无代理模式），准备开始注册流程")
        
        # 运行指定次数的注册流程
        registration_count = 0
        
        try:
            while registration_count < DEFAULT_REGISTRATION_COUNT:
                registration_count += 1
                # 记录开始时间
                registration_start_time = time.time()
                logger.info(f"[{self.group_id}] 开始第 {registration_count}/{DEFAULT_REGISTRATION_COUNT} 次注册流程")
                
                # 重置当前尝试次数
                self.current_attempt = 0
                
                # 生成新邮箱
                self.email = self.generate_email()
                if not self.email:
                    logger.error(f"[{self.group_id}] 获取邮箱失败，跳过本次注册")
                    continue

                logger.info(f"[{self.group_id}] 准备注册新账号: {self.email}")

                # 生成OAuth参数
                if not self.generate_oauth_params():
                    logger.error(f"[{self.group_id}] 生成OAuth参数失败，跳过本次注册")
                    continue

                # 构建state参数
                state_data = {
                    "returnTo": f"https://cursor.com/cn/loginDeepControl?challenge={self.challenge}&uuid={self.uuid}&mode=login"
                }
                state_json = json.dumps(state_data)
                encoded_state = quote(state_json)

                # 加载注册页面（无代理模式）
                encoded_email = quote(self.email)
                url = SIGNUP_URL.format(encoded_email, encoded_state)
                logger.info(f"[{self.group_id}] 访问注册URL: {url}")
                self.page.get(url)
                
                # 开始处理注册
                time.sleep(2)
                self.process_registration()
                
                # 记录结束时间和耗时
                registration_end_time = time.time()
                elapsed_time = registration_end_time - registration_start_time
                logger.info(f"[{self.group_id}] 完成注册流程: {self.email} - 耗时: {elapsed_time:.2f} 秒")
                
                # 根据注册耗时动态调整休息时间
                # if elapsed_time >= 90:
                #     rest_time = 2
                # else:
                #     rest_time = 92 - elapsed_time
                    
                # logger.info(f"[{self.group_id}] 注册耗时 {elapsed_time:.2f} 秒，休息 {rest_time:.2f} 秒")
                time.sleep(2)
            
            logger.info(f"[{self.group_id}] 完成全部 {DEFAULT_REGISTRATION_COUNT} 次注册流程")
        finally:
            # 所有注册完成后，清理资源
            self.cleanup()
            logger.info(f"[{self.group_id}] 线程资源已清理")

    def is_proxy_expired(self):
        """检查当前代理是否已过期"""
        if not self.proxy_start_time:
            return True  # 如果没有开始时间，视为已过期
            
        current_time = time.time()
        elapsed_minutes = (current_time - self.proxy_start_time) / 60
        
        if elapsed_minutes >= PROXY_LIFETIME_MINUTES:
            logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，超过设定的 {PROXY_LIFETIME_MINUTES} 分钟有效期")
            return True
            
        logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，还在有效期内 ({PROXY_LIFETIME_MINUTES} 分钟)")
        return False


class RegistrationManager:
    def __init__(self, num_threads=DEFAULT_THREADS, headless=False):
        self.num_threads = min(num_threads, MAX_THREADS)
        self.headless = headless
        self.registers = []
        self.successful = []
        self.failed = []
        
    def on_registration_finished(self, group_id, email, token):
        self.successful.append({
            "group_id": group_id,
            "email": email,
            "token": token,
            "timestamp": datetime.now().isoformat()
        })
        logger.info(f"注册成功: {email} (组: {group_id})")
            
#         # 将成功信息保存到文件
#         with open("successful_registrations.txt", "a", encoding="utf-8") as f:
#             f.write(f"{datetime.now().isoformat()} | {email} | {token}\n")
            
    def on_registration_error(self, group_id, email, error):
        self.failed.append({
            "group_id": group_id,
            "email": email,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        logger.error(f"注册失败: {email} (组: {group_id}): {error}")
        
    def start(self):
        """开始注册进程"""
        logger.info(f"启动 {self.num_threads} 个注册线程")
        
        # 清理浏览器进程
        cleanup_chrome_processes()
        
        for i in range(self.num_threads):
            group_id = f"{i}"  # 简化为数字索引
            register = DrissionCursorRegister(group_id, self.headless)
            
            # 设置回调
            register.set_callbacks(self.on_registration_finished, self.on_registration_error)
            
            # 创建线程运行注册实例
            thread = threading.Thread(target=register.run, daemon=True)
            
            # 保存实例
            self.registers.append(register)
            
            # 启动线程
            thread.start()
            
        logger.info("所有注册线程已启动")


def cleanup_chrome_processes():
    """清理Chrome进程"""
    global _chrome_process_ids
    
    if not _chrome_process_ids:
        logger.info("没有需要清理的Chrome进程...")
        return
        
    logger.info(f"清理 {len(_chrome_process_ids)} 个Chrome进程...")
    try:
        if os.name == 'nt':
            for pid in _chrome_process_ids:
                try:
                    os.system(f'taskkill /F /PID {pid} /T 2>nul')
                except:
                    pass
        else:
            for pid in _chrome_process_ids:
                try:
                    os.kill(pid, signal.SIGTERM)
                except:
                    pass
        _chrome_process_ids = []
    except Exception as e:
        logger.error(f"清理进程时出错: {e}")


def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info("接收到退出信号，正在清理资源...")
    cleanup_chrome_processes()
    sys.exit(0)


def main():
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 全局变量声明
    global USE_PROXY
    global DEFAULT_REGISTRATION_COUNT
    global PROXY_LIFETIME_MINUTES
    
    # 命令行参数
    parser = argparse.ArgumentParser(description="Cursor自动注册工具")
    parser.add_argument("-t", "--threads", type=int, default=DEFAULT_THREADS,
                        help=f"并行注册线程数量 (最大 {MAX_THREADS})")
    parser.add_argument("--headless", action="store_true", help="使用无头模式运行")
    parser.add_argument("--proxy", action="store_true", help="启用代理")
    parser.add_argument("-n", "--count", type=int, default=DEFAULT_REGISTRATION_COUNT,
                        help="每个线程的注册次数")
    parser.add_argument("-p", "--proxy-lifetime", type=int, default=PROXY_LIFETIME_MINUTES,
                        help="每个代理的有效时间（分钟）")
    args = parser.parse_args()
    
    # 设置代理参数
    USE_PROXY = True
    
    # 设置每个代理的有效时间
    if args.proxy_lifetime and args.proxy_lifetime > 0:
        PROXY_LIFETIME_MINUTES = args.proxy_lifetime
    logger.info(f"已设置每个代理最多使用 {PROXY_LIFETIME_MINUTES} 分钟")
        
    # 设置注册次数
    if args.count and args.count > 0:
        DEFAULT_REGISTRATION_COUNT = args.count
        logger.info(f"每个线程的注册次数已设置为: {DEFAULT_REGISTRATION_COUNT}")
        
    # 启动管理器
    manager = RegistrationManager(args.threads, args.headless)
    manager.start()
    
    # 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到用户中断，正在退出...")
        cleanup_chrome_processes()


if __name__ == "__main__":
    main() 