#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import logging
import random
from DrissionPage import ChromiumPage, ChromiumOptions

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_card_info():
    """从txt文件加载信用卡信息"""
    try:
        with open('card_info.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            logger.error("信用卡信息文件为空")
            return None
            
        # 解析第一行信用卡信息
        first_line = lines[0].strip()
        if '|' not in first_line:
            logger.error("信用卡信息格式错误")
            return None
            
        parts = first_line.split('|')
        if len(parts) < 4:
            logger.error("信用卡信息字段不完整")
            return None
            
        card_info = {
            'Name': parts[0].strip(),
            'CardNumber': parts[1].strip(),
            'Expiry': parts[2].strip(),
            'CVV': parts[3].strip()
        }
        
        logger.info(f"加载信用卡信息: {card_info['Name']}")
        
        # 删除已使用的信用卡信息
        remaining_lines = lines[1:]
        with open('card_info.txt', 'w', encoding='utf-8') as f:
            f.writelines(remaining_lines)
            
        logger.info(f"已删除使用的信用卡信息，剩余 {len(remaining_lines)} 张卡")
        return card_info
        
    except Exception as e:
        logger.error(f"加载信用卡信息时出错: {e}")
        return None

def format_expiry_date(expiry):
    """格式化有效期"""
    try:
        if '/' in expiry:
            month, year = expiry.split('/')
            if len(year) == 4:
                year = year[-2:]  # 只取后两位
            return f"{month.zfill(2)}/{year}"
        return expiry
    except:
        return expiry

def test_binding_with_url():
    """使用指定URL测试绑卡流程"""
    try:
        # 创建浏览器
        co = ChromiumOptions()
        co.headless(False)
        page = ChromiumPage(co)
        
        # 使用提供的URL
        test_url = "https://checkout.stripe.com/c/pay/cs_live_a1d3uuOB4j84V9pOvoUmJfOf5US3P8N9YD2myN0ZMtsU07lsTXfVDXspdC#fid2cGd2ZndsdXFsamtQa2x0cGBrYHZ2QGtkZ2lgYSc%2FY2RpdmApJ2R1bE5gfCc%2FJ3VuWmlsc2BaMDRJZzBJf0cxUV9SfVZMQlAxSWZkV3hzUTB9UjRMcj1fNkA0bEp1cEZnaUdJakltanQ2fVRrcTdQM3ZXNW5jdzNyclFhSWFURnxuY39La3IyMj1VZEoyazc1NXF2V2hTYDI3JyknY3dqaFZgd3Ngdyc%2FcXdwYCknaWR8anBxUXx1YCc%2FJ3Zsa2JpYFpscWBoJyknYGtkZ2lgVWlkZmBtamlhYHd2Jz9xd3BgeCUl"
        
        logger.info(f"访问测试URL: {test_url}")
        page.get(test_url)
        time.sleep(5)
        
        # 最多重试2次
        max_retry_attempts = 2
        for attempt in range(max_retry_attempts):
            logger.info(f"信用卡绑定尝试 {attempt + 1}/{max_retry_attempts}")
            
            # 如果是重试，先刷新页面
            if attempt > 0:
                logger.info("人机验证完成但未跳转说明绑卡失败，刷新页面重试...")
                page.refresh()
                time.sleep(5)
                logger.info("等待页面完全加载...")
                time.sleep(3)
                logger.info("页面加载完成")
            
            # 检查页面状态
            current_url = page.url
            logger.info(f"当前页面URL: {current_url}")
            
            # 点击Continue按钮
            continue_button = page.ele("xpath://button[text()='Continue']", timeout=10)
            if continue_button:
                logger.info("找到Continue按钮，点击")
                continue_button.click()
                time.sleep(5)
            else:
                logger.info("未找到Continue按钮，可能已跳过")
            
            # 点击信用卡选项按钮
            card_button = page.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
            if card_button:
                logger.info("找到信用卡选项按钮，点击")
                card_button.click()
                time.sleep(3)
                
                # 选择银行卡支付方式
                logger.info("尝试选择银行卡支付方式...")
                card_option_selectors = [
                    "xpath://input[@id='payment-method-accordion-item-title-card']",
                    "xpath://label[contains(text(),'Card')]",
                    "xpath://button[contains(text(),'Card')]",
                    "xpath://*[@data-testid='payment-method-card']",
                    "xpath://div[contains(@class,'payment-method')]//span[contains(text(),'Card')]",
                    "xpath://div[contains(@class,'accordion')]//span[contains(text(),'Card')]"
                ]

                card_option_selected = False
                for selector in card_option_selectors:
                    card_option = page.ele(selector, timeout=3)
                    if card_option:
                        logger.info(f"找到银行卡支付选项: {selector}")
                        card_option.click()
                        time.sleep(2)
                        card_option_selected = True
                        break

                if card_option_selected:
                    logger.info("已选择银行卡支付方式")
                else:
                    logger.warning("未找到银行卡支付方式选项，可能已经默认选中")
                
                # 等待信用卡表单加载
                logger.info("等待信用卡表单加载...")
                card_form_loaded = False
                for i in range(15):
                    card_number_input = page.ele("xpath://input[@id='cardNumber']", timeout=1)
                    if card_number_input:
                        logger.info("信用卡表单加载完成")
                        card_form_loaded = True
                        break
                    time.sleep(1)
                
                if card_form_loaded:
                    # 加载信用卡信息
                    card_info = load_card_info()
                    if card_info:
                        logger.info("开始填充信用卡表单")

                        # 1. 填充卡号
                        card_number_input = page.ele("xpath://input[@id='cardNumber']", timeout=10)
                        if card_number_input:
                            card_number_input.clear()
                            card_number_input.input(str(card_info.get('CardNumber', '')))
                            logger.info("已填充卡号")
                        else:
                            logger.error("未找到卡号输入框")
                            continue

                        time.sleep(1)

                        # 2. 填充有效期
                        card_expiry_input = page.ele("xpath://input[@id='cardExpiry']", timeout=10)
                        if card_expiry_input:
                            formatted_expiry = format_expiry_date(card_info.get('Expiry', ''))
                            card_expiry_input.clear()
                            card_expiry_input.input(str(formatted_expiry))
                            logger.info("已填充有效期")
                        else:
                            logger.error("未找到有效期输入框")
                            continue

                        time.sleep(1)

                        # 3. 填充CVV
                        card_cvc_input = page.ele("xpath://input[@id='cardCvc']", timeout=10)
                        if card_cvc_input:
                            card_cvc_input.clear()
                            card_cvc_input.input(str(card_info.get('CVV', '')))
                            logger.info("已填充CVV")
                        else:
                            logger.error("未找到CVV输入框")
                            continue

                        time.sleep(1)

                        # 4. 填充姓名
                        billing_name_input = page.ele("xpath://input[@id='billingName']", timeout=10)
                        if billing_name_input:
                            billing_name_input.clear()
                            billing_name_input.input(str(card_info.get('Name', '')))
                            logger.info("已填充姓名")
                        else:
                            logger.error("未找到姓名输入框")
                            continue

                        time.sleep(1)

                        # 5. 选择国家 CN
                        billing_country_select = page.ele("@id=billingCountry", timeout=10)
                        if billing_country_select:
                            try:
                                billing_country_select.select.by_value('CN')
                                logger.info("已选择国家 CN")
                                page.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                            except:
                                try:
                                    billing_country_select.select.by_text('China')
                                    logger.info("已选择国家 China")
                                    page.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                                except:
                                    logger.warning("选择国家失败，继续其他字段")

                        time.sleep(3)

                        # 6. 填充地址
                        billing_address_input = page.ele("xpath://input[@id='billingAddressLine1']", timeout=10)
                        if billing_address_input:
                            billing_address_input.clear()
                            billing_address_input.input("福建省三明市")
                            logger.info("已填充地址")

                        time.sleep(1)

                        # 7. 填充邮政编码
                        billing_postal_input = page.ele("xpath://input[@id='billingPostalCode']", timeout=10)
                        if billing_postal_input:
                            billing_postal_input.clear()
                            billing_postal_input.input("353000")
                            logger.info("已填充邮政编码")

                        time.sleep(1)

                        # 8. 填充城市
                        billing_locality_input = page.ele("xpath://input[@id='billingLocality']", timeout=10)
                        if billing_locality_input:
                            billing_locality_input.clear()
                            billing_locality_input.input("福建省三明市")
                            logger.info("已填充城市")

                        time.sleep(1)

                        # 9. 选择省份
                        billing_admin_select = page.ele("@id=billingAdministrativeArea", timeout=10)
                        if billing_admin_select:
                            try:
                                options = billing_admin_select.eles("tag:option")
                                for option in options:
                                    if '福建' in option.text:
                                        option.click()
                                        logger.info(f"已选择省份: {option.text}")
                                        break
                            except Exception as e:
                                logger.warning(f"选择省份时出错: {e}")

                        time.sleep(1)

                        # 10. 填充区域
                        billing_dependent_input = page.ele("xpath://input[@id='billingDependentLocality']", timeout=10)
                        if billing_dependent_input:
                            billing_dependent_input.clear()
                            billing_dependent_input.input("福建省三明市")
                            logger.info("已填充区域")

                        time.sleep(2)
                        logger.info("信用卡表单填充完成")
                        
                        # 提交表单
                        logger.info("查找并点击提交按钮...")
                        submit_selectors = [
                            "xpath://button[@type='submit']",
                            "xpath://button[contains(text(),'Submit')]",
                            "xpath://button[contains(text(),'Pay')]",
                            "xpath://button[contains(text(),'Complete')]",
                            "xpath://input[@type='submit']"
                        ]
                        
                        submit_button = None
                        for selector in submit_selectors:
                            submit_button = page.ele(selector, timeout=3)
                            if submit_button:
                                logger.info(f"找到提交按钮: {selector}")
                                break
                        
                        if submit_button:
                            logger.info("点击提交按钮")
                            submit_button.click()
                            time.sleep(3)
                            
                            # 检测人机验证
                            logger.info("检测人机验证...")
                            hcaptcha_elements = [
                                "xpath://div[contains(@class,'h-captcha')]",
                                "xpath://iframe[contains(@src,'hcaptcha')]",
                                "xpath://div[@id='h-captcha']"
                            ]
                            
                            hcaptcha_found = False
                            for selector in hcaptcha_elements:
                                hcaptcha_root = page.ele(selector, timeout=3)
                                if hcaptcha_root:
                                    logger.info(f"检测到人机验证: {selector}")
                                    hcaptcha_found = True
                                    break
                            
                            if hcaptcha_found:
                                logger.info("发现人机验证，请手动完成验证...")
                                logger.info("验证完成后，脚本将自动检测结果")
                                
                                # 等待验证完成
                                for i in range(120):  # 最多等待2分钟
                                    time.sleep(1)
                                    
                                    # 检查验证是否完成
                                    response_element = page.ele("xpath://textarea[@name='h-captcha-response']", timeout=1)
                                    if response_element:
                                        response_value = response_element.attr('value') or page.run_js("return arguments[0].value;", response_element)
                                        if response_value and response_value.strip():
                                            logger.info("检测到h-captcha-response有值，验证完成")
                                            
                                            # 验证完成后等待跳转
                                            logger.info("人机验证已完成，等待页面跳转...")
                                            for j in range(10):
                                                time.sleep(1)
                                                current_url = page.url
                                                if "cursor.com" in current_url:
                                                    logger.info(f"人机验证完成，已跳转到cursor.com: {current_url}")
                                                    logger.info("绑卡测试成功！")
                                                    input("按Enter键关闭浏览器...")
                                                    page.quit()
                                                    return
                                            
                                            # 如果验证完成但没有跳转，说明绑卡失败
                                            logger.warning(f"人机验证完成但未跳转，当前URL: {page.url}，绑卡失败")
                                            break
                                    
                                    # 检查是否直接跳转了
                                    current_url = page.url
                                    if "cursor.com" in current_url:
                                        logger.info(f"人机验证完成，已跳转到cursor.com: {current_url}")
                                        logger.info("绑卡测试成功！")
                                        input("按Enter键关闭浏览器...")
                                        page.quit()
                                        return
                                
                                if attempt < max_retry_attempts - 1:
                                    logger.info("准备刷新重试...")
                                    continue
                                else:
                                    logger.error("绑卡失败，已达到最大重试次数")
                                    break
                            else:
                                logger.info("未检测到人机验证，检查是否直接成功...")
                                time.sleep(3)
                                current_url = page.url
                                if "cursor.com" in current_url:
                                    logger.info(f"绑卡成功，已跳转到cursor.com: {current_url}")
                                    logger.info("绑卡测试成功！")
                                    input("按Enter键关闭浏览器...")
                                    page.quit()
                                    return
                                else:
                                    logger.warning(f"绑卡可能失败，当前URL: {current_url}")
                                    if attempt < max_retry_attempts - 1:
                                        continue
                                    else:
                                        break
                        else:
                            logger.error("未找到提交按钮")
                            break
                    else:
                        logger.error("加载信用卡信息失败")
                        break
                else:
                    logger.error("等待信用卡表单加载失败")
                    break
            else:
                logger.error("未找到信用卡选项按钮")
                break
        
        # 保持浏览器打开
        logger.info("测试完成，浏览器将保持打开状态...")
        input("按Enter键关闭浏览器...")
        page.quit()
        
    except Exception as e:
        logger.error(f"测试异常: {e}")

if __name__ == "__main__":
    test_binding_with_url()
