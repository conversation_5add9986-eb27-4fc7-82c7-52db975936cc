#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试失败邮箱记录功能
"""

import os
import sys
import tempfile

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from drission_cursor_reg_gamail_humkt import DrissionCursorRegister

def test_failed_email_logging():
    """测试失败邮箱记录功能"""
    print("开始测试失败邮箱记录功能...")
    
    # 创建临时文件用于测试
    temp_file = "test_gmail_humkt_fail.txt"
    
    try:
        # 清理可能存在的测试文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
        
        # 临时修改失败邮箱文件名
        original_file = "gmail_humkt_fail.txt"
        
        # 创建测试实例
        register = DrissionCursorRegister("test", headless=True)
        
        # 测试场景1：有完整的邮箱和密码
        print("\n测试场景1：有完整的邮箱和密码")
        register.email = "<EMAIL>"
        register.email_password = "password123"
        register.write_failed_email("<EMAIL>", "测试失败")
        
        # 测试场景2：有邮箱但没有密码
        print("\n测试场景2：有邮箱但没有密码")
        register.email = "<EMAIL>"
        register.email_password = None
        register.write_failed_email("<EMAIL>|password456", "测试失败")
        
        # 测试场景3：没有实例变量，从参数获取
        print("\n测试场景3：没有实例变量，从参数获取")
        register.email = None
        register.email_password = None
        register.write_failed_email("<EMAIL>|password789", "测试失败")
        
        # 测试场景4：参数格式不正确
        print("\n测试场景4：参数格式不正确")
        register.email = None
        register.email_password = None
        register.write_failed_email("<EMAIL>", "测试失败")
        
        # 测试静态方法
        print("\n测试静态方法")
        DrissionCursorRegister.write_failed_email_static("<EMAIL>|static_password", "静态方法测试")
        
        # 读取并显示结果
        print("\n测试结果:")
        if os.path.exists("gmail_humkt_fail.txt"):
            with open("gmail_humkt_fail.txt", "r", encoding="utf-8") as f:
                content = f.read()
                print(content)
        else:
            print("失败邮箱文件未创建")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists("gmail_humkt_fail.txt"):
            os.remove("gmail_humkt_fail.txt")
            print("\n已清理测试文件")

if __name__ == "__main__":
    test_failed_email_logging()
