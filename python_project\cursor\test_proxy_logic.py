#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理获取逻辑
"""

import requests
import time
import logging
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 代理配置
PROXY_API_URL = "https://overseas.proxy.qg.net/get?key=EB914784&num=1&keep_alive=5&area=990201"
BACKUP_PROXY_API_URL = "https://overseas.proxy.qg.net/query?key=EB914784"
PROXY_RETRY_TIMES = 3
PROXY_RETRY_INTERVAL = 3

class ProxyTester:
    def __init__(self):
        self.proxy_host = None
        self.proxy_port = None
        self.proxy_start_time = None

    def _try_get_proxy_from_main_api(self):
        """尝试从主要API获取代理，重试3次"""
        for attempt in range(1, PROXY_RETRY_TIMES + 1):
            try:
                logger.info(f"主要API第 {attempt}/{PROXY_RETRY_TIMES} 次尝试...")
                response = requests.get(PROXY_API_URL, timeout=10, verify=False)

                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"主要API响应: {data}")
                    if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                        proxy_info = data["data"][0]
                        server = proxy_info.get("server", "")
                        if ":" in server:
                            self.proxy_host, self.proxy_port = server.split(":")
                            self.proxy_start_time = time.time()
                            logger.info(f"主要API成功获取代理: {self.proxy_host}:{self.proxy_port}")
                            return True
                        else:
                            logger.error(f"代理服务器格式错误: {server}")
                    else:
                        logger.error(f"主要API返回失败: {data}")
                else:
                    logger.error(f"主要API请求失败: {response.status_code}")

            except Exception as e:
                logger.error(f"主要API请求异常: {e}")

            # 如果不是最后一次尝试，等待3秒
            if attempt < PROXY_RETRY_TIMES:
                logger.info(f"{PROXY_RETRY_INTERVAL}秒后重试主要API...")
                time.sleep(PROXY_RETRY_INTERVAL)

        return False

    def _try_get_proxy_from_backup_api(self):
        """尝试从备用API获取正在使用的代理"""
        try:
            logger.info("尝试备用API获取正在使用的代理...")
            response = requests.get(BACKUP_PROXY_API_URL, timeout=10, verify=False)

            if response.status_code == 200:
                data = response.json()
                logger.info(f"备用API响应: {data}")
                if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                    # 选择第一个可用的代理
                    proxy_info = data["data"][0]
                    server = proxy_info.get("server", "")
                    if ":" in server:
                        self.proxy_host, self.proxy_port = server.split(":")
                        self.proxy_start_time = time.time()
                        deadline = proxy_info.get("deadline", "未知")
                        logger.info(f"备用API成功获取代理: {self.proxy_host}:{self.proxy_port} (到期时间: {deadline})")
                        return True
                    else:
                        logger.error(f"备用API代理格式错误: {server}")
                else:
                    logger.error(f"备用API返回失败: {data}")
            else:
                logger.error(f"备用API请求失败: {response.status_code}")

        except Exception as e:
            logger.error(f"备用API请求异常: {e}")

        return False

    def get_new_proxy(self):
        """从API获取新的代理IP，支持重试机制和备用API"""
        logger.info("正在获取新代理...")

        # 首先尝试主要API（重试3次）
        if self._try_get_proxy_from_main_api():
            return True

        # 主要API失败后，尝试备用API（查询正在使用的代理）
        logger.warning("主要API获取代理失败，尝试备用API...")
        if self._try_get_proxy_from_backup_api():
            return True

        logger.error("所有代理获取方式都失败了")
        return False

    def test_proxy(self):
        """测试代理是否可用"""
        if not self.proxy_host or not self.proxy_port:
            logger.error("没有可用的代理进行测试")
            return False

        try:
            test_url = "https://www.google.com"
            test_proxies = {
                "http": f"http://{self.proxy_host}:{self.proxy_port}",
                "https": f"http://{self.proxy_host}:{self.proxy_port}"
            }
            response = requests.get(test_url, proxies=test_proxies, timeout=10)
            if response.status_code == 200:
                logger.info("代理测试成功")
                return True
            else:
                logger.error(f"代理测试失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"代理测试异常: {e}")
            return False

def test_proxy_logic():
    """测试代理获取逻辑"""
    logger.info("=" * 60)
    logger.info("开始测试代理获取逻辑")
    logger.info("=" * 60)

    tester = ProxyTester()
    
    # 测试代理获取
    if tester.get_new_proxy():
        logger.info(f"成功获取代理: {tester.proxy_host}:{tester.proxy_port}")
        
        # 测试代理可用性
        if tester.test_proxy():
            logger.info("代理测试通过")
        else:
            logger.warning("代理测试失败")
    else:
        logger.error("代理获取失败")

    logger.info("=" * 60)
    logger.info("代理获取逻辑测试完成")
    logger.info("=" * 60)

if __name__ == "__main__":
    test_proxy_logic()
