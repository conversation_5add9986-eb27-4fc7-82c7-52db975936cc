#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2925邮箱验证码监控程序
自动获取Cursor注册验证码并提交到后端接口
"""

from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import signal
import random
import logging
import json
import sys
import requests
from datetime import datetime
import threading
import psutil
import re
from urllib.parse import urlencode

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("2925_email_monitor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 2925邮箱账密配置 - 请在此处修改为实际账密
EMAIL_USERNAME = "a965586934"  # 2925邮箱用户名
EMAIL_PASSWORD = "1234qwer!"  # 2925邮箱密码

# 配置参数
LOGIN_URL = "https://www.2925.com/login/"
BACKUP_LOGIN_URLS = [
    "https://www.2925.com/",
    "https://2925.com/login/",
    "https://2925.com/"
]
API_BASE_URL = "http://119.29.20.123:8080/admin-api"
EMAIL_CODE_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/sendEmailCode?token=fe84864d7d354b53ae65e9fee25b0067"

# 邮件API配置
MAIL_LIST_URL = "https://2925.com/mailv2/maildata/MailList/mails"
MAIL_READ_URL = "https://2925.com/mailv2/maildata/MailRead/mails/read"

# 监控配置
SCAN_INTERVAL = 5  # 扫描间隔（秒）

# 全局变量，用于存储浏览器进程ID
_chrome_process_ids = []

class Email2925Monitor:
    def __init__(self, headless=True):
        self.headless = headless
        self.page = None
        self.cookies_map = {}  # 存储登录后的cookies
        self.jwt_token = None  # 存储jwt_token
        self.token_expire_time = 0  # token过期时间戳
        self.profile_path = "chrome-data-2925"
        self.before_pids = []
        self.is_running = False  # 防止重复运行
        self.is_logged_in = False  # 登录状态标记
        self.browser_pid = None  # 当前浏览器进程ID
        self.browser_pids = []  # 所有相关浏览器进程ID
        self.last_health_check = 0  # 上次健康检查时间
        self.health_check_interval = 15  # 健康检查间隔（秒）- 缩短间隔
        self.max_retry_count = 3  # 最大重试次数
        self.current_retry_count = 0  # 当前重试次数
        self.last_successful_operation = time.time()  # 上次成功操作时间
        self.max_idle_time = 300  # 最大空闲时间（5分钟）
        self.login_check_interval = 60  # 登录状态检查间隔（秒）
        self.last_login_check = 0  # 上次登录检查时间

    def sleep_random(self, min_ms, max_ms):
        """随机等待"""
        sleep_time = random.randint(min_ms, max_ms) / 1000.0
        time.sleep(sleep_time)

    def setup_browser(self):
        """设置浏览器 - 增强稳定性配置"""
        logger.info("开始设置浏览器...")

        # 先尝试删除已存在的目录
        try:
            if os.path.exists(self.profile_path):
                import shutil
                shutil.rmtree(self.profile_path)
                logger.info(f"已删除旧的浏览器配置文件夹: {self.profile_path}")
        except Exception as e:
            logger.warning(f"删除旧的浏览器配置文件夹时出错: {e}")

        # 创建新的目录
        os.makedirs(self.profile_path, exist_ok=True)

        try:
            # 记录启动前的浏览器进程
            self.before_pids = self.get_browser_processes()

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            logger.info(f"使用浏览器路径: {browser_path}")

            # 检查浏览器是否存在
            if not os.path.exists(browser_path):
                logger.error(f"浏览器路径不存在: {browser_path}")
                return False

            co.set_browser_path(browser_path)

            # 设置用户数据目录
            co.set_user_data_path(self.profile_path)

            # 核心稳定性参数 - 防止自动关闭
            co.set_argument("--no-first-run")
            co.set_argument("--no-default-browser-check")
            co.set_argument("--disable-default-apps")
            co.set_argument("--disable-popup-blocking")
            co.set_argument("--disable-translate")
            co.set_argument("--disable-background-timer-throttling")
            co.set_argument("--disable-backgrounding-occluded-windows")
            co.set_argument("--disable-renderer-backgrounding")
            co.set_argument("--disable-features=TranslateUI")
            co.set_argument("--disable-ipc-flooding-protection")

            # 防止检测自动化 - 增强版
            co.set_argument("--disable-blink-features=AutomationControlled")
            co.set_argument("--exclude-switches=enable-automation")
            co.set_argument("--disable-extensions-http-throttling")
            co.set_argument("--disable-automation")
            co.set_argument("--disable-plugins")
            co.set_argument("--disable-plugins-discovery")
            co.set_argument("--disable-component-extensions-with-background-pages")

            # 模拟真实用户行为
            co.set_argument("--disable-features=VizDisplayCompositor")
            co.set_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # 禁用一些可能暴露自动化的功能
            co.set_argument("--disable-logging")
            co.set_argument("--disable-login-animations")
            co.set_argument("--disable-notifications")

            # 内存和性能优化
            co.set_argument("--max_old_space_size=4096")
            co.set_argument("--disable-dev-shm-usage")
            co.set_argument("--disable-gpu-sandbox")

            # 网络相关
            co.set_argument("--disable-webrtc")
            co.set_argument("--disable-web-security")
            co.set_argument("--allow-running-insecure-content")

            # 禁用可能导致崩溃的功能
            co.set_argument("--disable-software-rasterizer")
            co.set_argument("--disable-background-networking")
            co.set_argument("--disable-sync")
            co.set_argument("--disable-plugins-discovery")
            co.set_argument("--disable-preconnect")

            # 系统特定设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")
                co.set_argument("--disable-setuid-sandbox")
            elif sys.platform == "win32":
                co.set_argument("--disable-gpu")  # Windows下禁用GPU加速

            # 设置随机端口
            co.auto_port()

            # 设置无头模式 - 调试时先使用有头模式
            if self.headless:
                logger.info("使用无头模式")
                co.headless(True)
                # 无头模式额外参数
                co.set_argument("--disable-gpu")
                co.set_argument("--no-sandbox")
            else:
                logger.info("使用有头模式（调试）")
                co.headless(False)

            # 启动浏览器
            logger.info("正在启动浏览器...")
            self.page = ChromiumPage(co)

            # 等待更长时间确保浏览器完全启动
            time.sleep(3)

            # 设置反检测脚本
            self.setup_anti_detection()

            # 设置浏览器进程保护
            self.setup_browser_protection()

            # 测试浏览器是否正常工作
            try:
                current_url = self.page.url
                logger.info(f"浏览器启动成功，当前URL: {current_url}")
            except Exception as e:
                logger.error(f"浏览器启动后无法获取URL: {e}")
                return False

            # 记录新增的浏览器进程
            self.track_browser_processes()

            logger.info("浏览器设置完成")
            return True

        except Exception as e:
            logger.exception(f"设置浏览器时出现异常: {e}")
            return False

    def setup_anti_detection(self):
        """设置反检测脚本"""
        try:
            # 移除webdriver属性
            anti_detection_script = """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // 移除自动化相关属性
            delete navigator.__proto__.webdriver;

            // 模拟真实的Chrome属性
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });

            // 覆盖权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Cypress.env('NOTIFICATION_PERMISSION') || 'denied' }) :
                    originalQuery(parameters)
            );
            """

            self.page.run_js(anti_detection_script)
            logger.info("反检测脚本设置完成")
        except Exception as e:
            logger.warning(f"设置反检测脚本失败: {e}")

    def setup_browser_protection(self):
        """设置浏览器进程保护"""
        try:
            # 设置页面保活脚本
            protection_script = """
            // 防止页面被意外关闭
            window.addEventListener('beforeunload', function(e) {
                // 不显示确认对话框，但记录事件
                console.log('页面即将关闭');
            });

            // 定期发送心跳，保持连接活跃
            setInterval(function() {
                try {
                    // 简单的DOM操作，保持页面活跃
                    document.title = document.title;
                } catch(e) {
                    console.log('心跳检测异常:', e);
                }
            }, 30000); // 每30秒一次心跳

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    console.log('页面被隐藏');
                } else {
                    console.log('页面重新可见');
                }
            });
            """

            self.page.run_js(protection_script)
            logger.info("浏览器保护脚本设置完成")
        except Exception as e:
            logger.warning(f"设置浏览器保护脚本失败: {e}")

    def get_default_browser_path(self):
        """获取默认浏览器路径"""
        if sys.platform == "win32":
            # Windows系统可能的Chrome路径
            possible_paths = [
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
                os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe"),
                "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe"
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    logger.info(f"找到Chrome浏览器: {path}")
                    return path

            # 如果都找不到，返回默认路径
            logger.warning("未找到Chrome浏览器，使用默认路径")
            return possible_paths[0]

        elif sys.platform == "darwin":
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            return "/usr/bin/google-chrome"

    def get_browser_processes(self):
        """获取当前浏览器进程ID列表"""
        try:
            browser_process_names = ['chrome', 'chromium', 'msedge', 'edge', 'brave']
            return [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in browser_process_names)]
        except:
            return []

    def track_browser_processes(self):
        """跟踪新启动的浏览器进程"""
        global _chrome_process_ids
        try:
            after_pids = self.get_browser_processes()
            new_pids = [pid for pid in after_pids if pid not in self.before_pids]
            _chrome_process_ids.extend(new_pids)

            if new_pids:
                # 记录所有相关浏览器进程
                self.browser_pids = new_pids
                self.browser_pid = new_pids[0] if new_pids else None
                logger.info(f"跟踪到 {len(new_pids)} 个新浏览器进程，主进程ID: {self.browser_pid}")
                logger.info(f"所有进程ID: {self.browser_pids}")
            else:
                logger.warning("未检测到新的浏览器进程")
        except Exception as e:
            logger.error(f"跟踪进程失败: {e}")

    def get_all_browser_processes_status(self):
        """获取所有浏览器进程的状态"""
        alive_pids = []
        dead_pids = []

        for pid in self.browser_pids:
            try:
                process = psutil.Process(pid)
                if process.is_running() and process.status() != psutil.STATUS_ZOMBIE:
                    alive_pids.append(pid)
                else:
                    dead_pids.append(pid)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                dead_pids.append(pid)

        return alive_pids, dead_pids

    def is_browser_alive(self):
        """检查浏览器进程是否还存活"""
        if not self.browser_pid:
            return False

        try:
            # 检查进程是否存在
            process = psutil.Process(self.browser_pid)
            return process.is_running() and process.status() != psutil.STATUS_ZOMBIE
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False
        except Exception as e:
            logger.warning(f"检查浏览器进程状态失败: {e}")
            return False

    def is_page_responsive(self):
        """检查页面是否响应"""
        if not self.page:
            return False

        try:
            # 尝试获取当前URL，这是一个轻量级的操作
            _ = self.page.url
            return True
        except Exception as e:
            logger.warning(f"页面无响应: {e}")
            return False

    def check_browser_health(self):
        """检查浏览器健康状态 - 增强版"""
        current_time = time.time()

        # 如果距离上次检查时间不够，跳过
        if current_time - self.last_health_check < self.health_check_interval:
            return True

        self.last_health_check = current_time

        # 检查所有浏览器进程状态
        alive_pids, dead_pids = self.get_all_browser_processes_status()

        if dead_pids:
            logger.warning(f"检测到 {len(dead_pids)} 个死亡的浏览器进程: {dead_pids}")
            # 更新存活的进程列表
            self.browser_pids = alive_pids
            if alive_pids:
                self.browser_pid = alive_pids[0]
            else:
                self.browser_pid = None

        # 检查主浏览器进程
        if not self.is_browser_alive():
            logger.error("主浏览器进程已死亡")
            return False

        # 检查页面响应
        if not self.is_page_responsive():
            logger.error("浏览器页面无响应")
            return False

        # 检查是否长时间无操作
        if current_time - self.last_successful_operation > self.max_idle_time:
            logger.warning(f"浏览器空闲时间过长 ({current_time - self.last_successful_operation:.0f}秒)，可能需要刷新")
            # 尝试简单的页面操作来测试响应性
            try:
                _ = self.page.title
                self.last_successful_operation = current_time
            except:
                logger.error("浏览器响应测试失败")
                return False

        logger.debug("浏览器健康检查通过")
        return True

    def cleanup(self):
        """清理资源"""
        try:
            # 关闭浏览器
            if self.page:
                try:
                    self.page.quit()
                except:
                    pass  # 忽略关闭时的异常

            # 重置状态
            self.page = None
            self.browser_pid = None
            self.browser_pids = []
            self.is_logged_in = False
            self.jwt_token = None
            self.cookies_map = {}
            self.last_successful_operation = time.time()

            # 删除配置文件目录
            import shutil
            if os.path.exists(self.profile_path):
                try:
                    shutil.rmtree(self.profile_path)
                except:
                    pass  # 忽略删除时的异常

            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")

    def restart_browser(self):
        """重启浏览器"""
        logger.info("开始重启浏览器...")

        # 清理当前资源
        self.cleanup()

        # 等待一段时间确保资源完全释放
        time.sleep(3)

        # 重新初始化浏览器
        if self.setup_browser():
            logger.info("浏览器重启成功")
            return True
        else:
            logger.error("浏览器重启失败")
            return False

    def ensure_browser_ready(self):
        """确保浏览器就绪，如果有问题则自动重启"""
        # 检查浏览器健康状态
        if not self.check_browser_health():
            logger.warning("浏览器健康检查失败，尝试重启...")

            # 增加重试计数
            self.current_retry_count += 1

            if self.current_retry_count > self.max_retry_count:
                logger.error(f"浏览器重启次数超过最大限制 ({self.max_retry_count})，停止重试")
                return False

            # 重启浏览器
            if self.restart_browser():
                # 重新登录
                if self.login_2925():
                    logger.info("浏览器重启并重新登录成功")
                    self.current_retry_count = 0  # 重置重试计数
                    return True
                else:
                    logger.error("浏览器重启后重新登录失败")
                    return False
            else:
                logger.error("浏览器重启失败")
                return False

        return True

    def check_if_already_logged_in(self):
        """检查是否已经登录"""
        try:
            # 先检查当前页面是否有duoyi-app元素（登录后的主要标识）
            duoyi_app = self.page.ele("@id=duoyi-app", timeout=2)
            if duoyi_app:
                logger.info("检测到duoyi-app元素，用户已登录")
                # 保存当前cookies
                self.save_cookies()
                self.is_logged_in = True
                return True

            # 检查是否在邮箱主页面
            current_url = self.page.url
            if "2925.com" in current_url and ("mail" in current_url or "/#/" in current_url):
                # 尝试检查页面是否有邮箱相关元素
                mail_elements = [
                    "xpath://div[contains(@class, 'mail')]",
                    "xpath://div[contains(@class, 'inbox')]",
                    "xpath://a[contains(@href, 'mail')]",
                    "css:.mail-list",
                    "css:.mailbox"
                ]

                for selector in mail_elements:
                    try:
                        element = self.page.ele(selector, timeout=1)
                        if element:
                            logger.info(f"检测到邮箱页面元素，用户已登录: {selector}")
                            self.save_cookies()
                            self.is_logged_in = True
                            return True
                    except:
                        continue

            logger.info("未检测到登录状态，需要登录")
            return False

        except Exception as e:
            logger.warning(f"检查登录状态时出错: {e}")
            return False

    def login_2925(self):
        """登录2925邮箱 - 增强异常处理"""
        logger.info("开始登录2925邮箱...")

        # 首先检查是否已经登录
        if self.check_if_already_logged_in():
            logger.info("用户已经登录，跳过登录流程")
            return True

        # 最多重试3次登录
        max_login_retries = 3
        for retry_count in range(max_login_retries):
            try:
                if retry_count > 0:
                    logger.info(f"登录重试第 {retry_count} 次...")
                    # 重试前检查浏览器状态
                    if not self.check_browser_health():
                        logger.warning("浏览器状态异常，尝试重启...")
                        if not self.restart_browser():
                            logger.error("浏览器重启失败")
                            continue

                # 访问登录页面 - 简化逻辑
                logger.info(f"正在访问登录页面: {LOGIN_URL}")

                # 先访问首页，模拟真实用户行为
                self.page.get("https://www.2925.com/")
                time.sleep(2)

                # 再访问登录页面
                self.page.get(LOGIN_URL)
                self.wait_for_page_load()

                # 检查页面是否正确加载
                current_url = self.page.url
                logger.info(f"当前页面URL: {current_url}")

                # 如果被重定向到首页，可能已经登录了
                if "/#/" in current_url or current_url.endswith("/"):
                    logger.info("检测到重定向到首页，检查是否已经登录")
                    if self.check_if_already_logged_in():
                        return True
                    # 可能是单页应用，登录表单在首页
                    time.sleep(5)  # 等待页面完全加载
                else:
                    # 等待页面元素加载完成
                    time.sleep(3)

                # 检查浏览器是否还活着
                if not self.is_browser_alive():
                    logger.error("浏览器进程已死亡，尝试重启")
                    continue

                # 填写邮箱账号 - 多种定位策略
                logger.info("正在查找账号输入框...")
                account_input = None

                # 尝试多种定位方式
                selectors = [
                    "@id=ipt_account",
                    "xpath://input[@id='ipt_account']",
                    "xpath://input[@placeholder='请先输入您的邮箱账号']",
                    "xpath://input[contains(@class, 'account') or contains(@name, 'account')]",
                    "css:input[type='text']",
                    "css:input[type='email']"
                ]

                for selector in selectors:
                    try:
                        account_input = self.page.ele(selector, timeout=3)
                        if account_input:
                            logger.info(f"使用选择器找到账号输入框: {selector}")
                            break
                    except:
                        continue

                if account_input:
                    self.fill_input_field(account_input, EMAIL_USERNAME)
                    logger.info("已填写邮箱账号")
                else:
                    logger.error("未找到账号输入框")
                    continue  # 继续重试

                # 检查浏览器连接
                if not self.is_page_responsive():
                    logger.error("页面无响应，跳过本次重试")
                    continue

                # 填写密码 - 多种定位策略
                logger.info("正在查找密码输入框...")
                password_input = None

                password_selectors = [
                    "@id=ipt_password",
                    "xpath://input[@id='ipt_password']",
                    "xpath://input[@type='password']",
                    "xpath://input[@placeholder='请输入您的密码']",
                    "css:input[type='password']"
                ]

                for selector in password_selectors:
                    try:
                        password_input = self.page.ele(selector, timeout=3)
                        if password_input:
                            logger.info(f"使用选择器找到密码输入框: {selector}")
                            break
                    except:
                        continue

                if password_input:
                    self.fill_input_field(password_input, EMAIL_PASSWORD)
                    logger.info("已填写密码")
                else:
                    logger.error("未找到密码输入框")
                    continue

                self.sleep_random(500, 1000)

                # 勾选"已阅读"复选框
                try:
                    logger.info("正在查找同意条款复选框...")
                    agree_checkbox = self.page.ele("xpath://span[@class='icheckbox_minimal']", timeout=5)
                    if agree_checkbox:
                        agree_checkbox.click()
                        self.sleep_random(500, 1000)
                        logger.info("已勾选同意条款")
                    else:
                        logger.warning("未找到同意条款复选框，继续登录")
                except Exception as e:
                    logger.warning(f"勾选同意条款失败: {e}")

                self.sleep_random(100, 300)

                # 点击登录按钮 - 多种定位策略
                logger.info("正在查找登录按钮...")
                login_btn = None

                login_selectors = [
                    "@id=btn_login",
                    "xpath://button[@id='btn_login']",
                    "xpath://input[@type='submit']",
                    "xpath://button[contains(text(), '登录')]",
                    "xpath://a[contains(text(), '登录')]",
                    "css:button[type='submit']",
                    "css:.login-btn",
                    "css:.btn-login"
                ]

                for selector in login_selectors:
                    try:
                        login_btn = self.page.ele(selector, timeout=3)
                        if login_btn:
                            logger.info(f"使用选择器找到登录按钮: {selector}")
                            break
                    except:
                        continue

                if login_btn:
                    login_btn.click()
                    logger.info("已点击登录按钮，等待登录结果...")
                else:
                    logger.error("未找到登录按钮")
                    continue

                # 等待登录完成，检查是否出现duoyi-app元素
                logger.info("等待登录完成...")
                login_success = False
                for i in range(15):  # 增加等待时间到15秒
                    time.sleep(1)

                    # 检查浏览器是否还活着
                    if not self.is_browser_alive():
                        logger.error("登录过程中浏览器进程死亡")
                        break

                    # 检查页面是否响应
                    if not self.is_page_responsive():
                        logger.error("登录过程中页面失去响应")
                        break

                    try:
                        # 使用更全面的登录检测
                        if self.check_if_already_logged_in():
                            logger.info("登录成功")
                            login_success = True
                            return True

                        # 检查是否有错误信息
                        error_msg = self.page.ele("xpath://div[contains(@class, 'error') or contains(@class, 'alert')]", timeout=0.5)
                        if error_msg:
                            logger.error(f"登录出现错误信息: {error_msg.text}")
                            break

                        logger.debug(f"等待登录中... ({i+1}/15)")
                    except Exception as check_e:
                        logger.warning(f"检查登录状态时出错: {check_e}")
                        break

                if login_success:
                    return True

                # 超时后检查当前页面状态
                try:
                    current_url = self.page.url
                    logger.error(f"登录超时，当前URL: {current_url}")
                except Exception as debug_e:
                    logger.warning(f"获取页面URL失败: {debug_e}")

                logger.error(f"登录重试 {retry_count + 1} 失败")

            except Exception as e:
                logger.error(f"登录过程出错 (重试 {retry_count + 1}): {e}")
                # 如果是连接断开错误，尝试重启浏览器
                if "连接已断开" in str(e) or "connection" in str(e).lower():
                    logger.warning("检测到连接断开，将在下次重试时重启浏览器")
                continue

        logger.error(f"登录失败，已重试 {max_login_retries} 次")
        return False

    def wait_for_page_load(self):
        """等待页面加载完成"""
        time.sleep(2)



    def fill_input_field(self, element, text):
        """填写输入框 - 模拟真实用户输入"""
        try:
            # 先点击输入框获得焦点
            element.click()
            time.sleep(0.3)

            # 清空输入框
            element.clear()
            time.sleep(0.2)

            # 模拟逐字符输入
            for char in text:
                element.input(char)
                time.sleep(random.uniform(0.05, 0.15))  # 随机延迟

            time.sleep(0.3)
        except Exception as e:
            logger.error(f"填写输入框失败: {e}")

    def save_cookies(self):
        """保存登录后的cookies"""
        try:
            cookies = self.page.cookies()
            self.cookies_map = {}

            for cookie in cookies:
                self.cookies_map[cookie.get('name')] = cookie.get('value')

            # 特别保存jwt_token
            if 'jwt_token' in self.cookies_map:
                self.jwt_token = self.cookies_map['jwt_token']
                # 设置token过期时间（假设token有效期为2小时）
                self.token_expire_time = time.time() + 7200  # 2小时后过期
                logger.info(f"成功保存jwt_token: {self.jwt_token[:20]}...")
            else:
                logger.warning("未找到jwt_token")

            logger.info(f"成功保存 {len(self.cookies_map)} 个cookies")

            # 立即强制设置cookie到浏览器（参考Java代码）
            self.force_set_cookies_to_browser()

        except Exception as e:
            logger.error(f"保存cookies失败: {e}")

    def force_set_cookies_to_browser(self):
        """强制设置cookie到浏览器（参考Java代码的doLoginNew方法）"""
        try:
            logger.info("开始强制设置cookies到浏览器...")

            # 首先访问2925.com域名（必须先访问域名才能设置cookie）
            self.page.get("https://www.2925.com")
            time.sleep(2)

            # 删除现有cookies
            self.page.run_js("document.cookie.split(';').forEach(function(c) { document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/'); });")

            # 设置所有cookies到浏览器
            for name, value in self.cookies_map.items():
                try:
                    # 使用JavaScript设置cookie到2925.com域名
                    js_code = f"""
                    document.cookie = '{name}={value}; domain=2925.com; path=/; expires=' + new Date(Date.now() + 86400000 * 30).toUTCString();
                    """
                    self.page.run_js(js_code)
                    logger.debug(f"设置cookie: {name}")
                except Exception as e:
                    logger.warning(f"设置cookie {name} 失败: {e}")

            # 刷新页面使cookie生效
            self.page.refresh()
            time.sleep(2)

            # 再次访问确保cookie生效
            self.page.get("https://www.2925.com")
            time.sleep(2)

            logger.info("强制设置cookies完成")

        except Exception as e:
            logger.error(f"强制设置cookies失败: {e}")

    def refresh_cookies_from_browser(self):
        """从浏览器刷新cookies（参考Java代码的refreshCookies方法）"""
        try:
            logger.info("开始从浏览器刷新cookies...")

            # 确保在2925.com域名下
            current_url = self.page.url
            if "2925.com" not in current_url:
                self.page.get("https://www.2925.com")
                time.sleep(2)

            # 获取当前浏览器中的所有cookies
            browser_cookies = self.page.cookies()

            # 更新cookies_map
            old_cookie_count = len(self.cookies_map)
            self.cookies_map = {}

            for cookie in browser_cookies:
                name = cookie.get('name')
                value = cookie.get('value')
                if name and value:
                    self.cookies_map[name] = value

            # 更新jwt_token
            if 'jwt_token' in self.cookies_map:
                old_token = self.jwt_token
                self.jwt_token = self.cookies_map['jwt_token']
                self.token_expire_time = time.time() + 7200  # 2小时后过期

                if old_token != self.jwt_token:
                    logger.info(f"jwt_token已更新: {self.jwt_token[:20]}...")
                else:
                    logger.debug("jwt_token未变化")
            else:
                logger.warning("刷新后仍未找到jwt_token")

            logger.info(f"cookies刷新完成，从 {old_cookie_count} 个更新为 {len(self.cookies_map)} 个")

            # 强制设置到浏览器确保一致性
            self.force_set_cookies_to_browser()

            return True

        except Exception as e:
            logger.error(f"刷新cookies失败: {e}")
            return False

    def is_token_valid(self):
        """检查token是否有效（未过期）"""
        if not self.jwt_token:
            return False

        current_time = time.time()
        if current_time >= self.token_expire_time:
            logger.info("Token已过期")
            return False

        # 如果还有30分钟就过期，也认为需要重新登录
        if current_time >= (self.token_expire_time - 1800):
            logger.info("Token即将过期（30分钟内），需要重新登录")
            return False

        return True

    def reset_login_status(self):
        """重置登录状态"""
        self.is_logged_in = False
        self.jwt_token = None
        self.token_expire_time = 0
        self.cookies_map = {}
        logger.info("已重置登录状态")

    def check_and_maintain_login(self):
        """独立的登录状态检测和维护任务"""
        current_time = time.time()

        # 检查是否需要进行登录状态检查
        if current_time - self.last_login_check < self.login_check_interval:
            return True

        self.last_login_check = current_time
        logger.debug("执行定期登录状态检查...")

        try:
            # 检查浏览器是否就绪
            if not self.check_browser_health():
                logger.warning("浏览器健康检查失败，尝试重启")
                if self.restart_browser():
                    logger.info("浏览器重启成功，尝试重新登录")
                    return self.login_2925()
                else:
                    logger.error("浏览器重启失败")
                    return False

            # 检查是否已经登录
            if not self.check_if_already_logged_in():
                logger.info("检测到未登录状态，尝试登录")
                return self.login_2925()
            else:
                # 已登录，刷新cookies确保API调用有效
                logger.debug("检测到已登录，刷新cookies")
                self.refresh_cookies_from_browser()

            # 检查token是否有效
            if not self.jwt_token or not self.is_token_valid():
                logger.info("token无效或过期，尝试刷新")
                # 先尝试从浏览器刷新cookies
                if self.refresh_cookies_from_browser():
                    if self.jwt_token and self.is_token_valid():
                        logger.info("从浏览器刷新token成功")
                    else:
                        logger.warning("从浏览器刷新token失败，尝试重新登录")
                        return self.login_2925()
                else:
                    logger.warning("刷新cookies失败，尝试重新登录")
                    return self.login_2925()

            logger.debug("登录状态检查通过")
            return True

        except Exception as e:
            logger.error(f"登录状态检查出错: {e}")
            return False

    def get_email_code_new(self):
        """获取邮箱验证码 - 固定API调用，不检查登录状态"""
        logger.debug("开始获取邮件验证码...")

        # 更新最后成功操作时间
        self.last_successful_operation = time.time()

        try:
            # 构建邮件列表请求参数
            params = {
                'Folder': 'Inbox',
                'MailBox': f'{EMAIL_USERNAME}@2925.com',
                'FilterType': '0',
                'PageIndex': '1',
                'PageCount': '10'
            }

            # 构建请求头（参考Java代码）
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': 'https://www.2925.com/',
                'Origin': 'https://www.2925.com'
            }

            # 设置authorization header（必须）
            if self.jwt_token:
                headers['authorization'] = f'Bearer {self.jwt_token}'
                logger.debug(f"设置authorization header: Bearer {self.jwt_token[:20]}...")
            else:
                logger.warning("没有jwt_token，API请求可能失败")

            # 构建完整的cookie字符串（参考Java代码）
            if self.cookies_map:
                cookie_parts = []
                for k, v in self.cookies_map.items():
                    cookie_parts.append(f'{k}={v}')
                cookie_str = '; '.join(cookie_parts)
                headers['Cookie'] = cookie_str
                logger.debug(f"设置Cookie header，包含 {len(self.cookies_map)} 个cookies")
            else:
                logger.warning("没有cookies，API请求可能失败")

            # 请求邮件列表 - 增加更短的超时时间
            response = requests.get(MAIL_LIST_URL, params=params, headers=headers, timeout=5)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    mail_list = data.get('result', {}).get('list', [])
                    logger.info(f"获取到 {len(mail_list)} 封邮件")

                    # 处理未读邮件
                    processed_count = 0
                    for mail in mail_list:
                        unread = mail.get('unRead', False)
                        subject = mail.get('subject', '')

                        # 检查是否是Cursor验证码邮件且未读
                        if 'Sign up for Cursor' in subject and unread:
                            message_id = mail.get('messageId')
                            logger.info(f"找到未读Cursor邮件: {subject}")

                            # 获取邮件详情
                            if self.process_email_detail(message_id, headers):
                                processed_count += 1

                    if processed_count > 0:
                        logger.info(f"成功处理 {processed_count} 封Cursor验证码邮件")
                        # 更新成功操作时间
                        self.last_successful_operation = time.time()
                    else:
                        logger.info("未找到新的Cursor验证码邮件")

                    return True

                else:
                    logger.error(f"获取邮件列表失败: {data}")
                    # 检查是否是认证相关错误，但不阻塞后续尝试
                    error_code = data.get('code')
                    if error_code in [401, 403, 1001, 1002]:  # 常见的认证失败错误码
                        logger.info("检测到认证失败，标记需要重新登录")
                        self.reset_login_status()  # 重置登录状态，下次会重新登录
                    return False
            else:
                logger.error(f"请求邮件列表失败: {response.status_code}")
                # HTTP状态码401或403表示认证失败，但不阻塞
                if response.status_code in [401, 403]:
                    logger.info("检测到HTTP认证失败，标记需要重新登录")
                    self.reset_login_status()  # 重置登录状态，下次会重新登录
                return False

        except requests.exceptions.Timeout:
            logger.error("请求邮件列表超时")
            # 超时可能是浏览器问题，检查浏览器状态
            if not self.check_browser_health():
                logger.warning("超时后检测到浏览器异常，将在下次扫描时重启")
            return False
        except requests.exceptions.ConnectionError:
            logger.error("网络连接错误")
            return False
        except Exception as e:
            logger.error(f"获取邮件验证码出错: {e}")
            # 出错时检查是否是浏览器相关问题
            if not self.check_browser_health():
                logger.warning("异常后检测到浏览器问题，将在下次扫描时重启")
                return False
            # 如果是认证相关异常，重置登录状态
            if "401" in str(e) or "403" in str(e) or "认证" in str(e):
                logger.info("检测到认证相关异常，重置登录状态")
                self.reset_login_status()
            return False

    def process_email_detail(self, message_id, headers):
        """处理邮件详情，提取验证码"""
        try:
            # 构建邮件详情请求参数
            params = {
                'MessageID': message_id,
                'FolderName': 'Inbox',
                'MailBox': f'{EMAIL_USERNAME}@2925.com',
                'IsPre': 'false'
            }

            # 确保headers包含完整的认证信息
            detail_headers = headers.copy()
            if self.jwt_token and 'authorization' not in detail_headers:
                detail_headers['authorization'] = f'Bearer {self.jwt_token}'

            if self.cookies_map and 'Cookie' not in detail_headers:
                cookie_str = '; '.join([f'{k}={v}' for k, v in self.cookies_map.items()])
                detail_headers['Cookie'] = cookie_str

            # 请求邮件详情 - 使用更短的超时时间
            response = requests.get(MAIL_READ_URL, params=params, headers=detail_headers, timeout=5)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    result = data.get('result', {})
                    content = result.get('bodyText', '')

                    # 检查邮件内容是否包含Cursor验证码
                    if 'You requested to sign up for Cursor' in content:
                        # 提取验证码 - 根据Java代码的逻辑
                        code_text = content.replace("You requested to sign up for Cursor. Your one-time code is:", "").strip()
                        code = code_text[:11].replace(" ", "")  # 取前11个字符并去除空格

                        # 提取邮箱地址
                        mail_to_list = result.get('mailTo', [])
                        if mail_to_list:
                            email = mail_to_list[0].get('emailAddress', '')

                            if code and email:
                                logger.info(f"提取到验证码: {code}, 邮箱: {email}")

                                # 提交验证码到后端
                                if self.submit_email_code(email, code):
                                    return True
                            else:
                                logger.warning("验证码或邮箱地址提取失败")
                        else:
                            logger.warning("未找到收件人邮箱信息")
                    else:
                        logger.info("邮件内容不符合Cursor验证码格式")
                else:
                    logger.error(f"获取邮件详情失败: {data}")
                    # 检查是否是认证相关错误
                    error_code = data.get('code')
                    if error_code in [401, 403, 1001, 1002]:
                        logger.warning("邮件详情获取认证失败")
                        return False
            else:
                logger.error(f"请求邮件详情失败: {response.status_code}")
                if response.status_code in [401, 403]:
                    logger.warning("邮件详情请求认证失败")
                    return False

        except requests.exceptions.Timeout:
            logger.error("请求邮件详情超时")
        except requests.exceptions.ConnectionError:
            logger.error("邮件详情请求网络连接错误")
        except Exception as e:
            logger.error(f"处理邮件详情出错: {e}")

        return False

    def submit_email_code(self, email, code):
        """提交验证码到后端接口"""
        try:
            # 构建提交数据
            data = {
                'email': email,
                'code': code
            }

            # 提交到后端接口 - 使用更短的超时时间
            response = requests.post(f"{API_BASE_URL}{EMAIL_CODE_SUBMIT_ENDPOINT}", json=data, timeout=5)

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    logger.info(f"验证码提交成功: {email} -> {code}")
                    return True
                else:
                    logger.error(f"验证码提交失败: {result.get('message')}")
                    return False
            else:
                logger.error(f"验证码提交请求失败: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            logger.error("提交验证码超时")
            return False
        except requests.exceptions.ConnectionError:
            logger.error("提交验证码网络连接错误")
            return False
        except Exception as e:
            logger.error(f"提交验证码出错: {e}")
            return False

    def scan_email_code(self):
        """定时扫描邮箱验证码"""
        if self.is_running:
            logger.debug("已经有正在运行的邮箱任务，跳过本次扫描")
            return

        self.is_running = True
        start_time = time.time()
        logger.debug("开始扫描邮箱验证码...")

        try:
            # 独立的登录状态检测（不阻塞邮件获取）
            self.check_and_maintain_login()

            # 直接获取邮件，不依赖登录状态检测结果
            success = self.get_email_code_new()
            if success:
                logger.debug("邮件扫描完成")
                # 重置重试计数
                self.current_retry_count = 0
            else:
                logger.debug("邮件扫描失败")

        except Exception as e:
            logger.error(f"扫描邮箱验证码出错: {e}")
        finally:
            end_time = time.time()
            logger.debug(f"扫描邮箱验证码结束，耗时: {end_time - start_time:.2f}秒")
            self.is_running = False

    def setup_browser_conservative(self):
        """保守的浏览器设置 - 最小化参数"""
        logger.info("使用保守模式设置浏览器...")

        try:
            # 记录启动前的浏览器进程
            self.before_pids = self.get_browser_processes()

            # 设置浏览器选项 - 最小化配置
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            logger.info(f"使用浏览器路径: {browser_path}")

            if not os.path.exists(browser_path):
                logger.error(f"浏览器路径不存在: {browser_path}")
                return False

            co.set_browser_path(browser_path)

            # 只使用最基本的参数
            co.set_argument("--no-first-run")
            co.set_argument("--disable-default-apps")

            # 系统特定设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置模式
            if self.headless:
                logger.info("使用无头模式（保守）")
                co.headless(True)
                co.set_argument("--disable-gpu")
            else:
                logger.info("使用有头模式（保守）")
                co.headless(False)

            # 启动浏览器
            logger.info("正在启动浏览器（保守模式）...")
            self.page = ChromiumPage(co)

            # 等待浏览器启动
            time.sleep(5)

            # 测试浏览器
            current_url = self.page.url
            logger.info(f"浏览器启动成功（保守模式），当前URL: {current_url}")

            # 记录进程
            self.track_browser_processes()

            logger.info("浏览器设置完成（保守模式）")
            return True

        except Exception as e:
            logger.exception(f"保守模式设置浏览器失败: {e}")
            return False

    def start_monitoring(self):
        """开始监控邮箱"""
        logger.info("开始启动2925邮箱监控...")

        # 首先尝试标准配置
        browser_setup_success = self.setup_browser()

        # 如果标准配置失败，尝试保守配置
        if not browser_setup_success:
            logger.warning("标准浏览器配置失败，尝试保守配置...")
            self.cleanup()  # 清理之前的尝试
            time.sleep(2)
            browser_setup_success = self.setup_browser_conservative()

        if not browser_setup_success:
            logger.error("浏览器初始化失败（已尝试标准和保守配置）")
            return False

        # 执行登录
        if not self.login_2925():
            logger.error("登录失败")
            self.cleanup()
            return False

        logger.info("2925邮箱监控启动成功")
        return True

    def run_scheduled_task(self):
        """运行定时任务 - 持续监控邮箱"""
        logger.info(f"启动定时任务，每{SCAN_INTERVAL}秒扫描一次邮箱...")
        logger.info("程序将持续运行，按 Ctrl+C 停止监控")

        scan_count = 0

        while True:
            try:
                scan_count += 1
                logger.info(f"--- 第 {scan_count} 次扫描 ---")

                # 执行邮件扫描（内部包含登录状态检测）
                self.scan_email_code()

                logger.info(f"等待 {SCAN_INTERVAL} 秒后进行下次扫描...")
                time.sleep(SCAN_INTERVAL)

            except KeyboardInterrupt:
                logger.info("接收到中断信号，停止监控")
                break
            except Exception as e:
                logger.error(f"定时任务出错: {e}")
                logger.info(f"等待 {SCAN_INTERVAL} 秒后继续...")
                time.sleep(SCAN_INTERVAL)





def cleanup_chrome_processes():
    """清理Chrome进程"""
    global _chrome_process_ids

    if not _chrome_process_ids:
        logger.info("没有需要清理的Chrome进程...")
        return

    logger.info(f"清理 {len(_chrome_process_ids)} 个Chrome进程...")
    try:
        if os.name == 'nt':
            for pid in _chrome_process_ids:
                try:
                    os.system(f'taskkill /F /PID {pid} /T 2>nul')
                except:
                    pass
        else:
            for pid in _chrome_process_ids:
                try:
                    os.kill(pid, signal.SIGTERM)
                except:
                    pass
        _chrome_process_ids = []
    except Exception as e:
        logger.error(f"清理进程时出错: {e}")


def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info("接收到退出信号，正在清理资源...")
    cleanup_chrome_processes()
    sys.exit(0)


def main():
    """主函数"""
    import argparse

    # 命令行参数解析
    parser = argparse.ArgumentParser(description="2925邮箱验证码监控程序")
    parser.add_argument("--debug", action="store_true", help="启用调试模式（显示浏览器窗口）")
    parser.add_argument("--headless", action="store_true", help="强制使用无头模式")
    args = parser.parse_args()

    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    print("=" * 60)
    print("           2925邮箱验证码监控程序")
    print("=" * 60)
    print(f"邮箱账号: {EMAIL_USERNAME}@2925.com")
    print(f"扫描间隔: {SCAN_INTERVAL}秒")
    print(f"后端接口: {API_BASE_URL}{EMAIL_CODE_SUBMIT_ENDPOINT}")
    print(f"日志文件: 2925_email_monitor.log")

    # 显示运行模式
    if args.debug:
        print(f"运行模式: 调试模式（显示浏览器）")
        headless_mode = False
    elif args.headless:
        print(f"运行模式: 无头模式（后台运行）")
        headless_mode = True
    else:
        print(f"运行模式: 调试模式（显示浏览器）- 首次运行建议")
        headless_mode = False

    print("=" * 60)
    print("程序功能:")
    print("1. 自动登录2925邮箱（无痕模式）")
    print("2. 定时扫描未读邮件")
    print("3. 自动提取Cursor验证码")
    print("4. 提交验证码到后端接口")
    print("5. 持续运行，按 Ctrl+C 停止")
    print("=" * 60)
    print("使用提示:")
    print("- 首次运行建议使用调试模式: python drission_cursor_get2925.py --debug")
    print("- 确认正常后可使用无头模式: python drission_cursor_get2925.py --headless")
    print("=" * 60)

    logger.info("=== 2925邮箱验证码监控程序启动 ===")
    logger.info(f"运行模式: {'调试模式' if not headless_mode else '无头模式'}")

    # 创建监控实例
    monitor = Email2925Monitor(headless=headless_mode)

    try:
        # 启动监控
        if monitor.start_monitoring():
            # 运行定时任务（会一直运行）
            monitor.run_scheduled_task()
        else:
            logger.error("监控启动失败")
            print("❌ 监控启动失败，请检查配置和网络连接")
            print("💡 建议:")
            print("1. 检查Chrome浏览器是否正确安装")
            print("2. 检查网络连接是否正常")
            print("3. 检查2925邮箱账密是否正确")
            print("4. 使用调试模式查看详细信息: python drission_cursor_get2925.py --debug")
    except KeyboardInterrupt:
        logger.info("接收到用户中断，正在退出...")
        print("\n⏹️  用户手动停止程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        print(f"❌ 程序运行出错: {e}")
    finally:
        # 清理资源
        print("🧹 正在清理资源...")
        monitor.cleanup()
        cleanup_chrome_processes()
        print("✅ 程序已安全退出")
        logger.info("程序已退出")


if __name__ == "__main__":
    main()