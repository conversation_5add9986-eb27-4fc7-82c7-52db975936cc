#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# 模拟失败邮箱记录功能
def test_write_failed_email():
    """测试失败邮箱记录功能"""
    
    class MockRegister:
        def __init__(self):
            self.group_id = "test"
            self.email = "<EMAIL>"
            self.email_password = "password123"
        
        def write_failed_email(self, email_data, reason):
            """将失败的邮箱账号写入文件"""
            try:
                written_content = ""
                with open("gmail_humkt_fail.txt", "a", encoding="utf-8") as f:
                    # 优先使用实例变量中的邮箱和密码
                    if self.email and self.email_password:
                        written_content = f"{self.email}|{self.email_password}"
                        f.write(f"{written_content}\n")
                        print(f"[{self.group_id}] 使用实例变量记录失败邮箱")
                    elif self.email and not self.email_password:
                        # 有邮箱但没有密码，尝试从email_data中提取
                        if "|" in str(email_data):
                            parts = str(email_data).split("|")
                            if len(parts) >= 2:
                                email, password = parts[0], parts[1]
                                written_content = f"{email}|{password}"
                                f.write(f"{written_content}\n")
                                print(f"[{self.group_id}] 从参数中提取密码记录失败邮箱")
                            else:
                                written_content = f"{self.email}|未知密码"
                                f.write(f"{written_content}\n")
                                print(f"[{self.group_id}] 邮箱密码为空，记录为未知密码")
                        else:
                            written_content = f"{self.email}|未知密码"
                            f.write(f"{written_content}\n")
                            print(f"[{self.group_id}] 邮箱密码为空，记录为未知密码")
                    else:
                        written_content = str(email_data) if email_data else "未知邮箱"
                        f.write(f"{written_content}\n")
                        print(f"[{self.group_id}] 无法获取完整邮箱信息，记录: {written_content}")
                
                print(f"[{self.group_id}] 已记录失败邮箱: {written_content}")
            except Exception as e:
                print(f"[{self.group_id}] 写入失败邮箱文件时出错: {e}")
    
    # 清理测试文件
    if os.path.exists("gmail_humkt_fail.txt"):
        os.remove("gmail_humkt_fail.txt")
    
    register = MockRegister()
    
    print("测试场景1：有完整的邮箱和密码")
    register.email = "<EMAIL>"
    register.email_password = "password123"
    register.write_failed_email("<EMAIL>", "测试失败")
    
    print("\n测试场景2：有邮箱但没有密码")
    register.email = "<EMAIL>"
    register.email_password = None
    register.write_failed_email("<EMAIL>|password456", "测试失败")
    
    print("\n测试场景3：没有实例变量")
    register.email = None
    register.email_password = None
    register.write_failed_email("<EMAIL>|password789", "测试失败")
    
    print("\n测试结果:")
    if os.path.exists("gmail_humkt_fail.txt"):
        with open("gmail_humkt_fail.txt", "r", encoding="utf-8") as f:
            content = f.read()
            print(content)
    
    # 清理测试文件
    if os.path.exists("gmail_humkt_fail.txt"):
        os.remove("gmail_humkt_fail.txt")

if __name__ == "__main__":
    test_write_failed_email()
