#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮箱池功能
"""

import threading
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 模拟邮箱池
_email_pool = []
_email_pool_lock = threading.Lock()

def add_emails_to_pool(email_list):
    """将邮箱列表添加到邮箱池"""
    global _email_pool, _email_pool_lock
    
    with _email_pool_lock:
        for email_data in email_list:
            if "|" in email_data:
                parts = email_data.split("|")
                if len(parts) == 2:
                    email, password = parts
                    _email_pool.append({
                        'email': email,
                        'password': password
                    })
                    logger.info(f"添加邮箱到池: {email}")
                else:
                    logger.error(f"邮箱数据格式错误: {email_data}")
            else:
                logger.error(f"邮箱数据缺少分隔符: {email_data}")
        
        logger.info(f"邮箱池更新完成，当前池中邮箱数量: {len(_email_pool)}")

def get_email_from_pool():
    """从邮箱池中获取一个邮箱"""
    global _email_pool, _email_pool_lock
    
    with _email_pool_lock:
        if _email_pool:
            email_data = _email_pool.pop(0)  # 取出第一个邮箱
            logger.info(f"从邮箱池中获取邮箱: {email_data['email']} (池中剩余: {len(_email_pool)})")
            return email_data
        else:
            logger.info("邮箱池为空")
            return None

def get_pool_size():
    """获取邮箱池大小"""
    global _email_pool, _email_pool_lock
    
    with _email_pool_lock:
        return len(_email_pool)

def simulate_registration_thread(thread_id, registration_count):
    """模拟注册线程"""
    logger.info(f"线程 {thread_id} 开始，计划注册 {registration_count} 次")
    
    for i in range(registration_count):
        logger.info(f"线程 {thread_id} - 第 {i+1} 次注册")
        
        # 检查邮箱池大小
        pool_size = get_pool_size()
        logger.info(f"线程 {thread_id} - 当前邮箱池大小: {pool_size}")
        
        # 尝试从邮箱池获取邮箱
        email_data = get_email_from_pool()
        
        if email_data:
            logger.info(f"线程 {thread_id} - 使用邮箱: {email_data['email']}")
        else:
            logger.info(f"线程 {thread_id} - 邮箱池为空，需要下单获取新邮箱")
            # 模拟下单获取多个邮箱
            new_emails = [
                f"test{thread_id}_{i}<EMAIL>|password123",
                f"test{thread_id}_{i}<EMAIL>|password456",
                f"test{thread_id}_{i}<EMAIL>|password789"
            ]
            logger.info(f"线程 {thread_id} - 模拟下单获取到 {len(new_emails)} 个邮箱")
            add_emails_to_pool(new_emails)
            
            # 再次尝试获取邮箱
            email_data = get_email_from_pool()
            if email_data:
                logger.info(f"线程 {thread_id} - 使用邮箱: {email_data['email']}")
        
        # 模拟注册耗时
        time.sleep(1)
        
        logger.info(f"线程 {thread_id} - 第 {i+1} 次注册完成，剩余邮箱池大小: {get_pool_size()}")
    
    logger.info(f"线程 {thread_id} 完成所有注册")

def test_email_pool():
    """测试邮箱池功能"""
    logger.info("=" * 50)
    logger.info("开始测试邮箱池功能")
    logger.info("=" * 50)
    
    # 初始添加一些邮箱到池中
    initial_emails = [
        "<EMAIL>|pass1",
        "<EMAIL>|pass2",
        "<EMAIL>|pass3"
    ]
    logger.info("添加初始邮箱到池中")
    add_emails_to_pool(initial_emails)
    
    # 创建多个线程模拟并发注册
    threads = []
    thread_count = 3
    registrations_per_thread = 5
    
    for i in range(thread_count):
        thread = threading.Thread(
            target=simulate_registration_thread,
            args=(i, registrations_per_thread),
            daemon=True
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    logger.info("=" * 50)
    logger.info("邮箱池测试完成")
    logger.info(f"最终邮箱池大小: {get_pool_size()}")
    logger.info("=" * 50)

if __name__ == "__main__":
    test_email_pool()
