#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import time

print("Python版本:", sys.version)
print("开始测试环境...")

try:
    print("测试导入DrissionPage...")
    from DrissionPage import ChromiumPage, ChromiumOptions
    print("✅ DrissionPage导入成功")
    
    print("测试创建浏览器选项...")
    co = ChromiumOptions()
    co.headless(False)
    print("✅ 浏览器选项创建成功")
    
    print("测试创建浏览器页面...")
    page = ChromiumPage(co)
    print("✅ 浏览器页面创建成功")
    
    print("测试访问网页...")
    page.get("https://www.baidu.com")
    time.sleep(3)
    
    print(f"当前URL: {page.url}")
    print(f"页面标题: {page.title}")
    
    print("关闭浏览器...")
    page.quit()
    print("✅ 环境测试完成")
    
except Exception as e:
    print(f"❌ 环境测试失败: {e}")
    import traceback
    traceback.print_exc()

print("测试结束")
