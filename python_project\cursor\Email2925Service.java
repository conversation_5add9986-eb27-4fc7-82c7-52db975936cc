package cn.iocoder.yudao.module.cqp.service;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWT;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.github.bonigarcia.wdm.WebDriverManager;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeDriverLogLevel;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 邮件登录获取验证码服务
 */
@Component
@Slf4j
public class Email2925Service {
    private static final String LOGIN_URL = "https://www.2925.com/login/";
    @Resource
    @Lazy
    private StringRedisTemplate stringRedisTemplate;
    @Value("${email2925.username}")
    private String username;

    @Value("${email2925.password}")
    private String password;

    @Value("${email2925.headless}")
    private boolean headless;

    @Value("${email2925.chromeDataPath:D:/chrome-data-cursor-2925email/}")
    private String chromeDataPath;
    @Value("${email2925.enable:true}")
    private boolean enable;
    // 使用AtomicBoolean替代普通布尔值
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean isLogining = new AtomicBoolean(false);
    private final AtomicBoolean isRefreshingCookies = new AtomicBoolean(false);
    public static Map<String, String> cookieMap = new HashMap<>();
    private WebDriver driver;
    static {
        cookieMap.put("jwt_token", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q_rQEvlftUj3OFpL_LCe8SoX_WbNYtdW51xbouOZdK4");
        cookieMap.put(".AspNetCore.Cookies", "chunks-2");
        cookieMap.put(".AspNetCore.CookiesC1", "CfDJ8LvNtK7K79pCgl25c3WvKz2hRNSU53mE6jkNpk3QtMXESaKo7f-p3VWIQiZtVZdZrJpqSGUuHkb8Vz91toTjmFJcYwFgo7t9bVXaa4ZP3e_bb-cKdsXhpNDcuTUjWl8e-NlNgf4tgC8RqtEMgd4N0bV_RwLXy5P_vM7zdOP7rmuJFu7YjxAV3YM9CAPxC4CjS-ka8qe9BJiTkFxlpkM-vHiK-j0Z6QbWwT0fiM8BLpH3TI_0N2zasRQbFA2mShax5FvdBW1-C81udvGQBX69puBqPMpkU3-RJhbt6BwIve7gydOUsY6wU7iLivnkpr0ktheubutz98Z21hkMoeaZ1aypZPXhEiU_enyoJkUnvvUjjOCMspqBepY8e3pJLdR1HObQZFYuCNykiawxk44IKWOplvaFPc__n8IoAZXz9bXw__HzUQk4xpadyfQZkRrUP4Hx7Vgfw_mRzwRaGMRfsgvkZcRyVj8VKviFfkFhbbgzB-v-Bm1ddh6Lr8numbe6AgX5QmzGvxTo0ChTR1IoOS8lkAtODriSWNGk75DjSUTP4hdySr-Jm2KJec3E5OwtorgAM_iXzlR2Te2pua0JiDIu9HzHlprG_wtkeE4cLdcdkpW4trKKQEhD7GlwM37RNiKbO4_tjYxxU6df8LJdqYFqMej9eAVOmk44tCzLwGGtASsxlSycGPihX_G8lysLDi-i5nyH3-4dT6Moj6lisnloU7VZkq0h6gCDJaPx9zoX8_8qjknCmJ6t3XDvvTWIYyclGLEcqGV5K-avYXp4j5Vy4tb5T1IvuGbBvpHeODoknLVyeWi18mAdC1UMveawFq823lN0Sg-BEQBMplrFSWtykUU2bG1XWUT0fi1yKU9LNc4xjjS9c9o43a7yNw9w1mcgXlPJMsg1lpXXqjFs7Rkk3f67lgpwqgysZEgQ96wJ6uS4RUecS_TbNASHQZ8jQ-W1ZGS6GacNYBrEKHVeYAVVpwIGTDHI5wTry7_pRcuEn6jk9_4cXBhDq8aXlxJnOTD_72sX73Mj2pauwHYPgHriystPyrS59nzIPw_ZM6n9aybz-ou7ExnRREwsTeD-X0tWH-ovElCKQ-yU-7IIE_PVuMItDttsaSKgbLdxskdCPSlCDDVmDJ0AU5dhuMJ3rYjJK4t-Ahclz7EtIcuA8ldiVr0YpKVETNYkONZSgrk9wva0OmnBGlH3bQ50tw1NqFVN0a0kAanzfmPlyxJOoshxJUo0c2y6v4RqL0B9zUrQ59cG32iAOaprY9VfMJZdyHNaY_KAde8fofvva37iMPCZeltm7VVyON2LQIhG4SQWApp2jQcgcoSeC8J3gN8KZ2seGT-rqPOtdexbrJq2JAQXWUTYhp4kH9nLF_SouI2I1gj-buS2NyyvuSnkBxAf2b8IXnkDwCoUfL0TugLMUbeZOdZD09UmoEYa9mRiPsYQRKs3evPtjYzZpa9scWFpJH-9AKLlAEHZ4c17NSj8PbyZLbSYFPC9jSRjals33zDOzNzuaSKisYOWk5ftj_eYibV5IYJcplDM7g3sj7T7vHPKjtJydJn58LwAy1-vnm7fbg0cONAnOxxSkn0s-j1WhJdj5jGLPDlsKyVFtM_1Kz2lsTugCNk91mSya7qN3dY5Grup01Mt0zojXGGG1XUwAY2XddjZNDw-nV2SkNsDXPW17erF-YtC1mut7oZaee1Mm-4lYxSZKPPPZ7OvWmfu-rQREtxKlUsIAK8ONTi6mFboaol5y4ljmATb0rgoWJ-ex9l_WDxhUtwv1ruTJFyK2OGmlPHmDKUcUm2EzXKpXE4eOiMJNg3O1qt6z3SxdxJf4YBcxVMYFKfJskbOQgTmJkpRvzSMpzP6zdPx457Y9XBC0k-BKvyr_65hjk1KIlTfrcWbL_tvXkibY8roDqlV3nUtyIYPQP-yBa0UVMQWoO4ZlDualYI8KolfAU3QQ6ZgwjuaDvVKSNPePyVng6wQ2GtD_7Gjoz8aODkHfY5DGdOW5xrlZSeJhFFwiIlbDEdLkr1_GqUSA5KR-harh1iXTFnxOdwoZz9HPKyxc225YWyCDbmVgP_CSSxYSnoWAqdSul0nJkzX_OidfFjm1obSUARDRRiR1_R5URPW34A2S0G6Qc1YdRCyIybAyNwLMMQr3Kr1bbBFX5j_ZIY4_hfmzQiM7Q7_yPm_j3-30dreN8DnyLeH0KU4RLZYpjgjmFJSH8dkTANdWDU9IWopnXhJ0olZ95KAj55xtrru5k-BYi3HQh0xCoPiKz1ncCkxyNQ1qzVE__hiAXi2xvAgcdSk5_Ny2THCQ2lKIEg56lprafJ9Nj2n8TTlcMIKUE64sL-7etqDUr9gq2qGevcmE74cl78k5RrOSSzYzSUZEeQeG3k_pYhdKRFxHg0jEs-YRhu2bPyusGbTR7MJ52xMQVUdKiitEJwb3sale1Y2lVppH6X16oQf8HXGymTliglHQ77PAGrqTDYkBzKRAvMN8w34e6idkye5rHhfJYIr99OAdJdAEUGZWuf5_tLiL7xguAaZTljdiSgKPgFbnqyJbxLC-5uZ_KMFsItD6C5RaeIUUbS-iHR4AavSvnNDNh449wWFq6WLbgHoS5f5UkEwX1Ka7HLUTdwMdNMLGcZYXA5aanIXMXM1IAeETsb3t-4uP4fxr8NqrY4mwRaA0fOGrijtBcIfMPvcKGYQYU4vTkO_KT3CH_fMaaCZO7bQMgmXlpY4s4SreTRgmmU1jA-UYw_wfAECzd4AAySxSZGtUwO_wFbmF5L3pDf7ai5Lz-RG0t8qFui68NpblCjZob2GvwOImJisUlVoGPD9ABYgGLxNT2dM4l00F74-KlqXXepUgq69tQ6FyuL0cmrhWJep3cb9Sn-sSFi66t0XVCgV0Uy-0eOp9usLtJfLC8jXU9oLxCV_OBRkpW1W_AKaYNFv6XJmeSoaWljEdGo3l2odp8B0m5JcuX8aoD4n9Aj93XSHQDvlkXw9nUEaWHv5PnbUKkt1Y1HKsu0ilGaQSH7QKYYZGKC8C8oud-CC2n3GT3nK_lG8Te8jbkbkqLMReSDPqQoJFAR5x1eSG7XiO2XUaoPG1P9PBOusrA0IDiI6SYGc3HD0xQXG69suD2O4mdqeTtVegTVmVJbrhAFD_iKriCUF7i9ZMo6gazAz2vckoQzb9r6F5GKTpCyjNDLjBCF7vCBjWBWxJ9jPCCVQutEtcMszIvFSWE_xdy52EQmJ8vAhxJvBPU0c4qXpejy2gBFfFvfJiY0mIn5pAb831NH1EbwTTQVTCMs4FJf-3Fv8raEGPHiT8yJfdqWOQiYZINJ1-lmrjipsBHqUCP4vgb45PxFASHIWAoyaQWQkB6HAMcs7ktz844gmH5QpPYy1_4CLk7h51fmjOEWs7mijp63OxHv0_w2dTJq4DJpPpvUdqhfUoEQcOkP2Taze3Y8V8WB4ubc4JNspSbmBJIfdyeRQ8wPPro-YC3noGei5M5eiRK4b_ZdSXloO56EPc2hk9nW4JUXVgfRo9iSHV6XutgREPDEmIjC6NA_52ww-x86FSOQgBuRkjwFfjX7nwoZwF357QiK6j1Yy49BUrI7IALdqv3aBmuPhyoaqdTTi92eIqbsKhuRV8O5Nkcz5sLDEQY1HDJO1UPeBPA2yb1NPwQ3EBfet2NCMjYpfe1-HJfdMoPmAuaULU0WiRT87lwjJOh8bu8G6jcAFX7XsZs8JX5LGt79L8dZiXXr-_AO3ym1-aHI4irlIFPd-UqteiMzNf8t2184EK273QVxPo-JxDULGAVHdEYaFwpa2HrB9ufErlXkgzG_5iP1cGYkQUeDLm0cDVcYcnPsfduUC4mDHGht4AlzJTmRbDBVUQoqbxaXGPRv4WSP3q73EyLw-o7LvW-SwzHbYw-RmNwS2W4mnQCb9dzS_YYRY-tK2Qj5O7qZLRG4TtgwW11xAYkZxFtCBaVm_S7tMcYhvtEupLZN_o-yNKHaoaH18yAA9z");
        cookieMap.put(".AspNetCore.CookiesC2", "HVmkdASythVzvnyHhllK71Vtd8ePUIp3dAOWPJ2Y458-iwi0csAfygA2nemr8ivneDVUoDa9A4blMr66XBmmM47Qy33sOTxImtrkHnnwA90ygUzUnMp8Xr-4JNAmvApg04p5otpQUju-ByJthzKyz_MCACj-1t295FO7QdvW7bhjSiIeQ7aylpjCfBDaOOGO19gwPNdsxoboJ22CpJv8YOZLNq_0pReJtfMMt5GBq3VcFIPSR8_OQPRj0Sca1eKC0FfYuQUQ4bLqvxNM8weRgW1TX-oI4mcgcj5qfgOOCFvtx5p9gKaIVd03upAiEI_KuLra5J4La1vd1-Kz_uz2hQMh9HFLg9-d3dYx8gvLjoVJypoZuOekZewtBTbb01MwwhWMkNnVlhklsbVHSRleBkjxJMyC538gSVz8e-EEaJaeEyBTk8xkbP9mChdYWd2DM1xSyibzmkZCQk8JoTVYJHpqRv31sPi42UQCSe_eNbPjcBHpJQTwrGqPIq04COKhd3K3CbFdXUGSwfJwLYFd_7h02mgv0Q8RxOroahLNygHbXh2OR6ii73UMKs");
    }
    @PostConstruct
    public void init() {
        if (!enable) {
            return; // 如果功能未启用，则不初始化
        }
        try {
            initBrowser();
//            doLoginNew();
            doLogin();
        } catch (Exception e) {
            log.error("浏览器初始化失败", e);
        }
    }
    /**
     * 初始化浏览器
     */
    public void initBrowser() {
        try {
            System.setProperty("webdriver.chrome.driver", "C:\\Users\\<USER>\\.cache\\selenium\\chromedriver\\win64\\137.0.7151.119\\chromedriver.exe");
            // 设置WebDriver - 使用WebDriverManager自动管理驱动
//            WebDriverManager.chromedriver()
//                    .browserVersion("131.0.6778.264")  // 使用空字符串表示自动检测
//                    .setup();

            ChromeOptions options = new ChromeOptions();
            // 日志关闭
            options.setLogLevel(ChromeDriverLogLevel.OFF);

            // 无头模式配置
            if (headless) {
                options.addArguments("--headless=new");
                // 添加更多伪装参数
                options.addArguments("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36");
                options.addArguments("--disable-blink-features=AutomationControlled");
                options.addArguments("--window-size=1920,1080");

                // 设置语言和地区
                options.addArguments("--lang=zh-CN");
                options.addArguments("--accept-lang=zh-CN,zh;q=0.9");

            }
// 关键修复参数 - 解决DevToolsActivePort问题
            options.addArguments("--no-sandbox");
            options.addArguments("--disable-dev-shm-usage");
            options.addArguments("--disable-gpu");
            options.addArguments("--remote-allow-origins=*");
            options.addArguments("--window-size=1920,1080");

            // 性能优化参数
            options.addArguments("--disable-extensions");
            options.addArguments("--disable-plugins");
            options.addArguments("--disable-images");
            // 注意：不要禁用JavaScript，否则网页功能会失效
            // options.addArguments("--disable-javascript");
            options.addArguments("--disable-default-apps");
            options.addArguments("--disable-background-networking");
            options.addArguments("--disable-background-timer-throttling");
            options.addArguments("--disable-renderer-backgrounding");
            options.addArguments("--disable-backgrounding-occluded-windows");
            options.addArguments("--disable-client-side-phishing-detection");
            options.addArguments("--disable-sync");
            options.addArguments("--disable-translate");
            options.addArguments("--hide-scrollbars");
            options.addArguments("--mute-audio");
            options.addArguments("--no-first-run");
            options.addArguments("--safebrowsing-disable-auto-update");
            options.addArguments("--ignore-certificate-errors");
            options.addArguments("--allow-running-insecure-content");
            options.addArguments("--disable-web-security");
            options.addArguments("-disable-features=IsolateOrigins,site-per-process");

            // 基本参数配置
            options.addArguments("--no-sandbox");
            options.addArguments("--disable-gpu");
            options.addArguments("--disable-dev-shm-usage");
            options.addArguments("--window-size=1920,1080");
            options.addArguments("--disable-web-security");
            // 解决WebSocket 403错误
            options.addArguments("--remote-allow-origins=*");

            // 禁用自动化检测
            options.setExperimentalOption("excludeSwitches", Arrays.asList("enable-automation"));
            options.addArguments("--disable-blink-features=AutomationControlled");
            options.setExperimentalOption("useAutomationExtension", false);

            // 禁用浏览器后台节能机制
            options.addArguments("--disable-background-timer-throttling");
            options.addArguments("--disable-backgrounding-occluded-windows");
            options.addArguments("--disable-renderer-backgrounding");

            // 设置用户数据目录
//            String userDataDir = System.getProperty("java.io.tmpdir") + "/chrome-data-2925/" + System.currentTimeMillis();
            String tpath = "D:/chrome-data-cursor-email/";
//            String userDataDir = tpath + System.currentTimeMillis();
            String userDataDir = tpath + "testb";
            try {
//                FileUtil.del(tpath);
            } catch (IORuntimeException e) {
                log.error("删除浏览器目录失败");
            }
//            String userDataDir = System.getProperty("user.home") + "/chrome-data-2925";
            options.addArguments("--user-data-dir=" + userDataDir);

            // 添加其他配置
            Map<String, Object> prefs = new HashMap<>();
            prefs.put("credentials_enable_service", false);
            prefs.put("profile.password_manager_enabled", false);
            prefs.put("profile.default_content_setting_values.notifications", 2);
            options.setExperimentalOption("prefs", prefs);

            try {
                driver = new ChromeDriver(options);
                System.out.println("Chrome浏览器启动成功");
            } catch (Exception e) {
                System.err.println("Chrome浏览器启动失败，错误信息: " + e.getMessage());
                System.err.println("尝试使用备用配置...");

                // 备用配置 - 更简化的选项
                ChromeOptions fallbackOptions = new ChromeOptions();
                fallbackOptions.setLogLevel(ChromeDriverLogLevel.OFF);
                fallbackOptions.addArguments("--no-sandbox");
                fallbackOptions.addArguments("--disable-dev-shm-usage");
                fallbackOptions.addArguments("--disable-gpu");
                fallbackOptions.addArguments("--remote-allow-origins=*");
                fallbackOptions.addArguments("--disable-web-security");
                fallbackOptions.addArguments("--ignore-certificate-errors");
                fallbackOptions.addArguments("--disable-extensions");

                // 使用系统临时目录
                String fallbackUserDataDir = System.getProperty("java.io.tmpdir") + "/chrome-fallback-" + System.currentTimeMillis();
                fallbackOptions.addArguments("--user-data-dir=" + fallbackUserDataDir);

                try {
                    driver = new ChromeDriver(fallbackOptions);
                    System.out.println("备用配置启动成功");
                } catch (Exception fallbackException) {
                    System.err.println("备用配置也失败了: " + fallbackException.getMessage());
                    throw new RuntimeException("Chrome浏览器无法启动，请检查Chrome安装和ChromeDriver版本", fallbackException);
                }
            }
            driver.manage().window().maximize();
            driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));

            // 隐藏自动化控制条幅
            if (driver instanceof JavascriptExecutor) {
                ((JavascriptExecutor) driver).executeScript(
                        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})");
            }

            System.out.println("浏览器已初始化");
        } catch (Exception e) {
            System.err.println("浏览器初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("浏览器初始化失败", e);
        }
    }

    /**
     * 抓取邮箱的验证码，到缓存
     * @return
     */
    @Scheduled(fixedDelayString = "5000")
    public void scanEmailCode() {
        if (!enable) {
            return;
        }
        // 原子操作检查并设置运行状态
        if (!isRunning.compareAndSet(false, true)) {
            log.info("已经有正在运行的邮箱任务，已放弃该任务");
            return;
        }
        // 登录成功后处理邮件并解析内容（优先处理未读邮件，没有则处理第一封邮件）
        int start = DateUtil.date().millisecond();
        log.info("开始处理邮件...");
        try {
            // 开始处理邮件...
//            processEmail(null);
            getEmailCodeNew();
        } catch (Exception e) {
            log.error("处理邮件出错", e);
        } finally {
            int end = DateUtil.date().millisecond();
            log.info("处理邮件结束，耗时：" + (end - start)/1000 + "秒");
            // 任务结束，重置运行状态
            isRunning.set(false);
        }
    }

    private CommonResult<Object> doLogin() {
        if (!enable) {
            return CommonResult.success();
        }
        // 直接访问需要登录的页面
        driver.get("https://www.2925.com");

        // 检查是否已登录
        try {
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(15));
            waitForPageLoad();
            ThreadUtil.sleep(2000);
//            boolean isLoggedIn = wait.until(ExpectedConditions.presenceOfElementLocated(
//                    By.xpath("//div[@id='duoyi-app']"))).isDisplayed();
            //判断cookie 是否有jwt_token，并且解析jwt token判断 exp 的秒时间戳时间已过期
            boolean isLoggedIn = false;
            Cookie jwt_token = driver.manage().getCookieNamed("jwt_token");
            if (jwt_token != null) {
                String token = jwt_token.getValue();
                if (StrUtil.isNotBlank(token)) {
                    JWT jwt = JWT.of(token);
                    Object exp = jwt.getPayload("exp");
                    if (exp != null) {
                        //过期时间超过15 分钟
                        long expTime = Long.parseLong(exp.toString());
                        if (expTime < System.currentTimeMillis() / 1000 + 900) {
                            isLoggedIn = true;
                        }
                    }
                }
            }
            if (isLoggedIn) {
                System.out.println("已检测到登录状态，无需重新登录");
            } else {
                System.out.println("未检测到登录状态，正在重新登录...");
                throw new Exception("未检测到登录状态");
            }
        } catch (Exception e) {
            // 如果未登录，执行登录过程
            System.out.println("需要重新登录");

            // 设置登录信息
//            String email = "a965586934";
//            String password = "1234qwer!";

            // 执行登录
            boolean loginSuccess = login(username, password);
            if (!loginSuccess) {
                System.err.println("登录失败");
                return CommonResult.error(500, "登录失败");
            }
            doLoginNew();

        }
        return CommonResult.success();
    }

    /**
     * 通过设置cookie实现免登录
     * @return 登录结果
     */
    private CommonResult<Object> doLoginNew() {
        if (!enable) {
            return CommonResult.success();
        }
        
        try {
            // 首先访问域名（必须先访问域名才能设置cookie）
            driver.get("https://www.2925.com");
            
            // 设置Cookie
            for (Map.Entry<String, String> entry : cookieMap.entrySet()) {
                Cookie cookie = new Cookie(entry.getKey(), entry.getValue(), "2925.com", "/", null);
                driver.manage().addCookie(cookie);
            }
            // 刷新页面使cookie生效
            driver.navigate().refresh();
            driver.get("https://www.2925.com");
            // 等待页面加载，检查是否已登录
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(15));
            ThreadUtil.sleep(2000);
            try {
                boolean isLoggedIn = wait.until(ExpectedConditions.presenceOfElementLocated(
                        By.xpath("//div[@id='duoyi-app']"))).isDisplayed();
                
                if (isLoggedIn) {
                    log.info("通过cookie设置成功登录");
                    return CommonResult.success();
                } else {
                    log.error("通过cookie设置登录失败");
                    return CommonResult.error(500, "通过cookie设置登录失败");
                }
            } catch (Exception e) {
                log.error("通过cookie设置登录异常", e);
                return CommonResult.error(500, "通过cookie设置登录异常: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("cookie设置过程异常", e);
            return CommonResult.error(500, "cookie设置过程异常: " + e.getMessage());
        }
    }

    /**
     * 检查邮件并打开
     * 首先查找未读邮件，如果没有则查找任何邮件
     * @return 是否找到并点击了邮件
     */
    public void processEmail(String findemail) {

        try {
            WebElement webElement = getUnreadMails();
            if (webElement != null) {
                try {
                    //统计查找
                    WebElement subjectElement = webElement.findElement(
                            By.xpath(".//span[@class='mail-content-title' and contains(text(), 'Sign up for Cursor')]")
                    );
                    System.out.println("找到未读邮件: " + subjectElement.getText());
                    // 点击第一封未读邮件
                    ((JavascriptExecutor) driver).executeScript("arguments[0].click();", webElement);
                    System.out.println("已点击未读邮件");
                    Map<String, String> emailContent = parseEmailContent();
                    String email = emailContent.get("email");
                    String code = emailContent.get("code");
                    String originalEmail = emailContent.get("originalEmail");
                    if (StrUtil.isNotBlank(email) && StrUtil.isNotBlank(originalEmail) && StrUtil.isNotBlank(code)) {
                        log.info("已找到邮件：" + email + "，验证码为：" + code);
                        String redisKey = "cursor:email:code:" + email;
                        stringRedisTemplate.opsForValue().set(redisKey, code,30, TimeUnit.MINUTES);
                    }else{
                        log.info("已找到邮件不符合");
                    }
                } catch (Exception e) {
                    System.out.println("找到未读邮件，非符合主题");

                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private WebElement getUnreadMails() {
        List<WebElement> unreadMails = null;
        try {
            driver.get("https://www.2925.com");
            waitForPageLoad();
            driver.get("https://www.2925.com/#/mailList");
            waitForPageLoad();

            // 等待页面加载完成
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(5));
            wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//table[contains(@class, 'maillist-table')]")));
            // 优先查找未读邮件
            unreadMails = driver.findElements(By.xpath("//tr[contains(@class, 'unread-mail') and .//*[contains(text(), 'Sign up for Cursor')]]"));
            if (CollUtil.isEmpty(unreadMails)) {
                System.out.println("未找到未读邮件");
                return null;
            }
        } catch (Exception e) {
            System.out.println("找未读邮件异常");
            return null;
        }
        return unreadMails.get(0);
    }


    /**
     * 解析邮件内容，提取邮箱和验证码
     * @return 包含邮箱和验证码的Map，key分别为email、originalEmail和code
     */
    public Map<String, String> parseEmailContent() {
        Map<String, String> result = new HashMap<>();
        try {
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));

            // 等待邮件内容加载完成
//            wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//div[contains(@class, 'mail-content-body')]")));
//            wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//div[contains(@class, 'sender-main-body')]")));

            // 提取收件人邮箱信息
            WebElement senderToElement = wait.until(ExpectedConditions.presenceOfElementLocated(
                    By.xpath("//div[contains(@class, 'sender-to')]")));

            String senderToText = senderToElement.getText();
            System.out.println("提取的收件人信息: " + senderToText);

            // 使用正则表达式提取邮箱地址
            // 提取转发邮箱
            String forwardEmailPattern = "发送至：\\s*([\\w.-]+@[\\w.-]+\\.[a-z]+)";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(forwardEmailPattern);
            java.util.regex.Matcher matcher = pattern.matcher(senderToText);

            if (matcher.find()) {
                String forwardEmail = matcher.group(1);
                result.put("email", forwardEmail);
                System.out.println("提取到转发邮箱: " + forwardEmail);
            }

            // 提取原始接收邮箱
            String originalEmailPattern = "（([\\w.-]+@[\\w.-]+\\.[a-z]+)接收）";
            pattern = java.util.regex.Pattern.compile(originalEmailPattern);
            matcher = pattern.matcher(senderToText);

            if (matcher.find()) {
                String originalEmail = matcher.group(1);
                result.put("originalEmail", originalEmail);
                System.out.println("提取到原始接收邮箱: " + originalEmail);
            }

            // 提取验证码
            // 方法1：直接通过XPath定位验证码元素
            try {
                WebElement codeElement = driver.findElement(By.xpath("//div[contains(@style, 'font-size:28px') and contains(@style, 'letter-spacing:2px')]"));
                String code = codeElement.getText().trim();
                result.put("code", code);
                System.out.println("提取到验证码(方法1): " + code);
            } catch (Exception e) {
                System.out.println("方法1提取验证码失败: " + e.getMessage());

                // 方法2：解析邮件内容中的6位数字
                try {
                    // 获取邮件内容
                    WebElement contentElement = driver.findElement(By.xpath("//div[contains(@class, 'sender-main-body')]"));
                    String content = contentElement.getText();

                    // 使用正则表达式匹配6位数字
                    pattern = java.util.regex.Pattern.compile("\\b(\\d{6})\\b");
                    matcher = pattern.matcher(content);

                    if (matcher.find()) {
                        String code = matcher.group(1);
                        result.put("code", code);
                        System.out.println("提取到验证码(方法2): " + code);
                    } else {
                        System.out.println("未能在邮件内容中找到6位数字验证码");
                    }
                } catch (Exception ex) {
                    System.out.println("方法2提取验证码失败: " + ex.getMessage());
                }
            }

            return result;
        } catch (Exception e) {
            System.err.println("解析邮件内容失败: " + e.getMessage());
            e.printStackTrace();
            return result;
        }
    }
    private void waitForPageLoad() {
        new WebDriverWait(driver, Duration.ofSeconds(10)).until(webDriver ->
                ((JavascriptExecutor) webDriver).executeScript("return document.readyState").equals("complete"));
    }
    // 辅助方法
    private void fillInputField(By locator, String text) {
        WebElement element = driver.findElement(locator);
        element.clear();
        element.sendKeys(text);
        sleepRandom(300, 500);
    }
    private void clickElement(By locator) {
        driver.findElement(locator).click();
        sleepRandom(100, 500);
    }
    private void sleepRandom(int min, int max) {
        try {
            Thread.sleep(min + (long) (Math.random() * (max - min)));
        } catch (InterruptedException ignored) {
        }
    }
    /**
     * 登录2925邮箱
     * @param email 邮箱账号
     * @param password 密码
     * @return 是否登录成功
     */
    public boolean login(String email, String password) {
        if (!isLogining.compareAndSet(false, true)) {
            log.info("已经有正在运行的登录邮箱任务，已放弃该任务");
            return false;
        }
        try {
            // 访问登录页面
            driver.get(LOGIN_URL);
            waitForPageLoad();

            // 填写邮箱账号和密码
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));

            // 等待页面元素加载完成
            wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//input[@id='ipt_account']")));
            ThreadUtil.sleep(2000);
            // 填写邮箱账号
            fillInputField(By.xpath("//input[@id='ipt_account']"), email);

            // 填写密码
            fillInputField(By.xpath("//input[@id='ipt_password']"), password);

            // 勾选"30天内免登录"复选框
            try {
                // 尝试点击iCheck-helper元素
                WebElement rememberCheckbox = driver.findElement(By.xpath("//input[@id='ipt_isreturn']/following-sibling::ins[contains(@class, 'iCheck-helper')]"));
                ((JavascriptExecutor) driver).executeScript("arguments[0].click();", rememberCheckbox);
                sleepRandom(500, 1000); // 增加等待时间
            } catch (Exception e) {
                // 备选方案：尝试点击父元素
                WebElement rememberParent = driver.findElement(By.xpath("//div[contains(@class, 'icheckbox_minimal') and .//input[@id='ipt_isreturn']]"));
                ((JavascriptExecutor) driver).executeScript("arguments[0].click();", rememberParent);
                sleepRandom(500, 1000);
            }

            // 勾选"已阅读"复选框
            try {
                // 尝试点击iCheck-helper元素
                WebElement agreeCheckbox = driver.findElement(By.xpath("//span[@class='icheckbox_minimal']"));
                ((JavascriptExecutor) driver).executeScript("arguments[0].click();", agreeCheckbox);
                sleepRandom(500, 1000); // 增加等待时间
            } catch (Exception e) {
                // 备选方案：尝试点击父元素
//                WebElement agreeParent = driver.findElement(By.xpath("//div[contains(@class, 'icheckbox_minimal') and .//input[@id='login_isreturn']]"));
//                ((JavascriptExecutor) driver).executeScript("arguments[0].click();", agreeParent);
//                sleepRandom(500, 1000);
            }
            sleepRandom(100, 300);
            // 点击登录按钮
            clickElement(By.xpath("//input[@id='btn_login']"));

            // 等待登录完成
            try {
                wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//div[@id='duoyi-app']")));
                String token = driver.manage().getCookieNamed("jwt_token").getValue();
                //将cookie全部放到cookieMap
                for (Cookie cookie : driver.manage().getCookies()) {
                    cookieMap.put(cookie.getName(), cookie.getValue());
                }
                System.out.println("登录成功：" + token);
                return true;
            } catch (TimeoutException e) {
                System.err.println("登录失败: 超时或登录错误");
                return false;
            }
        } catch (Exception e) {

            e.printStackTrace();
//            String token = driver.manage().getCookieNamed("jwt_token").getValue();
            //将cookie全部放到cookieMap
            for (Cookie cookie : driver.manage().getCookies()) {
                if ("jwt_token".equals(cookie.getName())) {
                    //判断jwt 过期时间exp 是否过期，过期的不放进来
                    JWT jwt = JWT.of(cookie.getValue());
                    Object exp = jwt.getPayload("exp");
                    if (exp != null) {
                        //过期时间超过15 分钟
                        long expTime = Long.parseLong(exp.toString());
                        if (expTime > System.currentTimeMillis() / 1000 + 90) {
                            cookieMap.put(cookie.getName(), cookie.getValue());
                        }
                    }
                }else{
                cookieMap.put(cookie.getName(), cookie.getValue());
                }
            }
            return false;
        }finally {
            isLogining.set(false);
        }
    }

    /**
     * 定时刷新cookie，防止被强制登出
     * 每分钟执行一次
     */
//    @Scheduled(fixedRate = 60000)
    public void refreshCookies() {
        if (!enable) {
            return; // 如果功能未启用，则不执行
        }
        
        // 检查是否已有刷新cookie的任务在执行
        if (!isRefreshingCookies.compareAndSet(false, true)) {
            log.debug("已有cookie刷新任务在执行，本次跳过");
            return;
        }
        
        try {
            log.debug("开始刷新cookie...");
            if (driver == null) {
                log.warn("浏览器未初始化，无法刷新cookie");
                return;
            }
            
            // 获取当前URL
            String currentUrl = driver.getCurrentUrl();
            
            // 如果当前不在2925.com域名下，先访问该域名
            if (!currentUrl.contains("2925.com")) {
                driver.get("https://www.2925.com");
                // 等待页面加载
                waitForPageLoad();
            }
            
            // 清除当前所有cookie
            driver.manage().deleteAllCookies();
            
            // 重新设置cookie
            for (Map.Entry<String, String> entry : cookieMap.entrySet()) {
                try {
                    Cookie cookie = new Cookie(entry.getKey(), entry.getValue(), "2925.com", "/", null);
                    driver.manage().addCookie(cookie);
                } catch (Exception e) {
                    log.warn("设置cookie[{}]失败: {}", entry.getKey(), e.getMessage());
                }
            }
            
            // 刷新页面使cookie生效
            driver.navigate().refresh();
            
            // 验证是否仍然保持登录状态
            try {
                WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(5));
                boolean isLoggedIn = wait.until(ExpectedConditions.presenceOfElementLocated(
                        By.xpath("//div[@id='duoyi-app']"))).isDisplayed();
                
                if (isLoggedIn) {
                    log.debug("cookie刷新成功，保持登录状态");
                } else {
                    log.warn("cookie刷新后登录状态验证失败，尝试重新登录");
                    doLoginNew();
                }
            } catch (Exception e) {
                log.warn("cookie刷新后验证登录状态异常，尝试重新登录", e);
                doLoginNew();
            }
        } catch (Exception e) {
            log.error("刷新cookie过程发生异常", e);
            // 出现异常时尝试重新初始化
            try {
                initBrowser();
                doLoginNew();
            } catch (Exception ex) {
                log.error("重新初始化浏览器失败", ex);
            }
        } finally {
            // 任务完成，重置标志
            isRefreshingCookies.set(false);
            log.debug("cookie刷新任务完成");
        }
    }

    public static void main(String[] args) {
//        getEmailCodeNew();
    }

    private  void getEmailCodeNew() {
        HttpRequest get = HttpUtil.createGet("https://2925.com/mailv2/maildata/MailList/mails?Folder=Inbox&MailBox=a965586934%402925.com&FilterType=0&PageIndex=1&PageCount=10");
        String token = "Bearer " + cookieMap.get("jwt_token");
        get.header("authorization", token);
        HttpResponse execute = get.execute();
        String body = execute.body();
        if (StrUtil.isNotBlank(body)) {
            JSONObject jsonObject = null;
            try {
                jsonObject = JSONUtil.parseObj(body);
            } catch (Exception e) {
                login(username, password);
                return;
            }
            if (jsonObject.getInt("code") == 200) {
                JSONArray list = jsonObject.getJSONObject("result").getJSONArray("list");
                if (CollUtil.isNotEmpty(list)) {
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject obj = list.getJSONObject(i);
                        Boolean unRead = obj.getBool("unRead", false);
                        //todo 测试写死
//                        unRead = true;
                        if (obj.getStr("subject").contains("Sign up for Cursor") && unRead) {
                            String messageId = obj.getStr("messageId");
                            HttpRequest detailGet = HttpUtil.createGet("https://2925.com/mailv2/maildata/MailRead/mails/read?MessageID=" + messageId + "&FolderName=Inbox&MailBox=a965586934%402925.com&IsPre=false");
                            detailGet.header("authorization", token);
                            //将cookieMap设置到cookie里
                            String cookie = "";
                            for (Map.Entry<String, String> entry : cookieMap.entrySet()) {
                                cookie += entry.getKey() + "=" + entry.getValue() + "; ";
                            }
                            detailGet.cookie(cookie);
                            HttpResponse detailExecute = detailGet.execute();
                            String detailBody = detailExecute.body();
                            if (StrUtil.isNotBlank(detailBody)) {
                                JSONObject detailJsonObject = JSONUtil.parseObj(detailBody);
                                if (detailJsonObject.getInt("code") == 200) {
                                    JSONObject detailResult = detailJsonObject.getJSONObject("result");
                                    String content = detailResult.getStr("bodyText");
                                    if (StrUtil.isNotBlank(content) && content.contains("You requested to sign up for Cursor")) {
                                        String code = content.replace("You requested to sign up for Cursor. Your one-time code is:", "").trim().substring(0, 11).replace(" ", "");
                                        String email = detailResult.getJSONArray("mailTo").getJSONObject(0).getStr("emailAddress");
                                        String redisKey = "cursor:email:code:" + email;
                                        stringRedisTemplate.opsForValue().set(redisKey, code,30, TimeUnit.MINUTES);
                                        System.out.println(email + "获取到验证码：" + code);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }else {
            System.out.println("获取响应体为空");
        }
    }
}
