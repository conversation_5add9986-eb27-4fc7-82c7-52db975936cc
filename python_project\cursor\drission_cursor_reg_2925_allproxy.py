from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import signal
import random
import logging
import json
import sys
from urllib.parse import quote
import requests
from datetime import datetime
import threading
import psutil
from colorama import Fore, Style
import argparse
from DrissionPage.errors import ElementLostError
import uuid
import base64
import hashlib
import secrets
import platform
# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cursor_registration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
SIGNUP_URL = "https://authenticator.cursor.sh/password?email={}&state={}&redirect_uri=https://cursor.com/api/auth/callback"
API_BASE_URL = "http://119.29.20.123:8080/admin-api"  # 修改为你的实际API端点
EMAIL_CODE_ENDPOINT = "/cqp/cursoremailpool/getEmailCode?token=fe84864d7d354b53ae65e9fee25b0067"
TOKEN_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/saveRecord?token=fe84864d7d354b53ae65e9fee25b0067"

# 代理配置
USE_PROXY = True  # 是否使用代理
USE_PROXY_FULL_PROCESS = False  # 是否全程使用代理（仅当USE_PROXY=True时生效）
# 当USE_PROXY=True且USE_PROXY_FULL_PROCESS=True时：全程使用代理，获取验证码后不关闭浏览器，直接输入验证码
# 当USE_PROXY=True且USE_PROXY_FULL_PROCESS=False时：部分代理模式，获取验证码后重启浏览器并启用代理
# 当USE_PROXY=False时：无代理模式，获取验证码后不关闭浏览器，直接输入验证码

# 主要代理API
PROXY_API_URL = "https://overseas.proxy.qg.net/get?key=EB914784&num=1&keep_alive=5&area=990400"
# 备用代理API（查询正在使用的代理）
BACKUP_PROXY_API_URL = "https://overseas.proxy.qg.net/query?key=EB914784"
PROXY_LIFETIME_MINUTES = 5  # 每个代理的有效时间（分钟）
PROXY_RETRY_TIMES = 5  # 代理获取重试次数
PROXY_RETRY_INTERVAL = 3  # 代理获取重试间隔（秒）

# 控制注册线程数量的常量
DEFAULT_THREADS = 1  # 默认并发线程数
MAX_THREADS = 20  # 最大并发线程数
DEFAULT_REGISTRATION_COUNT = 100000  # 每个线程默认注册次数

# 信用卡读取控制常量
USE_FIXED_FIRST_CARD = True  # 是否固定读取第一条信用卡数据（True：固定读取第一条且不删除，False：读取第一条并删除）

# 全局变量，用于存储浏览器进程ID
_chrome_process_ids = []

class DrissionCursorRegister:
    def __init__(self, group_id, headless=False):
        self.group_id = group_id
        self.headless = headless
        self.email = None
        self.page = None
        self.token = None
        self.code = None
        self.profile_path = f"chrome-data-cursor/group_{group_id}"  # 简化命名为固定格式
        self.max_attempts = 10  # 最大尝试次数
        self.current_attempt = 0
        self.before_pids = []
        self.success_callback = None
        self.error_callback = None
        self.proxy_host = None
        self.proxy_port = None
        self.proxy_start_time = None  # 记录代理获取的时间
        # OAuth相关参数
        self.uuid = None
        self.challenge = None
        self.verifier = None
        
    def set_callbacks(self, success_callback, error_callback):
        """设置回调函数"""
        self.success_callback = success_callback
        self.error_callback = error_callback

    def generate_oauth_params(self):
        """生成OAuth相关参数：uuid、challenge、verifier"""
        try:
            # 生成UUID
            self.uuid = str(uuid.uuid4())
            logger.info(f"[{self.group_id}] 生成UUID: {self.uuid}")

            # 生成code_verifier (32字节随机数，Base64编码)
            random_bytes = secrets.token_bytes(32)
            self.verifier = base64.urlsafe_b64encode(random_bytes).decode('utf-8').rstrip('=')
            logger.info(f"[{self.group_id}] 生成verifier: {self.verifier}")

            # 生成code_challenge (SHA-256哈希 + Base64编码)
            sha256_hash = hashlib.sha256(self.verifier.encode('utf-8')).digest()
            self.challenge = base64.urlsafe_b64encode(sha256_hash).decode('utf-8').rstrip('=')
            logger.info(f"[{self.group_id}] 生成challenge: {self.challenge}")

            return True
        except Exception as e:
            logger.error(f"[{self.group_id}] 生成OAuth参数失败: {e}")
            return False

    def generate_random_browser_profile(self):
        """生成随机的浏览器特征以避免指纹识别"""
        try:
            # 随机User-Agent列表（Windows Chrome）
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36"
            ]

            # 随机屏幕分辨率
            resolutions = [
                "1920,1080", "1366,768", "1536,864", "1440,900", "1600,900",
                "1280,720", "1920,1200", "2560,1440", "1680,1050"
            ]

            # 随机时区
            timezones = [
                "America/New_York", "America/Los_Angeles", "America/Chicago",
                "Europe/London", "Europe/Berlin", "Asia/Tokyo", "Asia/Shanghai",
                "Australia/Sydney", "America/Toronto"
            ]

            # 随机语言
            languages = [
                "en-US,en;q=0.9", "en-GB,en;q=0.9", "zh-CN,zh;q=0.9,en;q=0.8",
                "ja-JP,ja;q=0.9,en;q=0.8", "de-DE,de;q=0.9,en;q=0.8"
            ]

            profile = {
                "user_agent": random.choice(user_agents),
                "resolution": random.choice(resolutions),
                "timezone": random.choice(timezones),
                "language": random.choice(languages),
                "platform": random.choice(["Win32", "Win64"]),
                "memory": random.choice([4, 8, 16, 32]),  # GB
                "cpu_cores": random.choice([4, 6, 8, 12, 16])
            }

            logger.info(f"[{self.group_id}] 生成随机浏览器特征: UA={profile['user_agent'][:50]}...")
            return profile

        except Exception as e:
            logger.error(f"[{self.group_id}] 生成随机浏览器特征失败: {e}")
            return None

    def inject_anti_fingerprint_script(self):
        """注入反指纹脚本"""
        if not self.page:
            return

        try:
            # 反指纹JavaScript代码
            anti_fingerprint_script = """
            // 随机化Canvas指纹
            const originalGetContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(type, ...args) {
                const context = originalGetContext.apply(this, [type, ...args]);
                if (type === '2d') {
                    const originalFillText = context.fillText;
                    context.fillText = function(text, x, y, maxWidth) {
                        // 添加微小的随机偏移
                        const randomOffset = Math.random() * 0.1 - 0.05;
                        return originalFillText.apply(this, [text, x + randomOffset, y + randomOffset, maxWidth]);
                    };
                }
                return context;
            };

            // 随机化WebGL指纹
            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === this.RENDERER || parameter === this.VENDOR) {
                    return 'Generic Renderer';
                }
                return originalGetParameter.apply(this, arguments);
            };

            // 随机化屏幕信息
            Object.defineProperty(screen, 'width', {
                get: function() { return 1920 + Math.floor(Math.random() * 100); }
            });
            Object.defineProperty(screen, 'height', {
                get: function() { return 1080 + Math.floor(Math.random() * 100); }
            });

            // 随机化时区
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {
                return -480 + Math.floor(Math.random() * 60); // 随机时区偏移
            };

            // 隐藏自动化特征
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });

            // 随机化语言
            Object.defineProperty(navigator, 'language', {
                get: function() {
                    const languages = ['en-US', 'en-GB', 'zh-CN', 'ja-JP', 'de-DE'];
                    return languages[Math.floor(Math.random() * languages.length)];
                }
            });

            console.log('Anti-fingerprint script injected');
            """

            self.page.run_js(anti_fingerprint_script)
            logger.info(f"[{self.group_id}] 已注入反指纹脚本")

        except Exception as e:
            logger.warning(f"[{self.group_id}] 注入反指纹脚本失败: {e}")

    def deep_clean_browser_data(self):
        """深度清理浏览器数据以避免指纹追踪"""
        try:
            import shutil

            # 删除主配置目录
            if os.path.exists(self.profile_path):
                shutil.rmtree(self.profile_path)
                logger.info(f"[{self.group_id}] 已删除浏览器配置目录: {self.profile_path}")

            # 清理可能的临时文件和缓存
            temp_dirs = [
                os.path.join(os.environ.get('TEMP', ''), 'chrome_*'),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Google', 'Chrome', 'User Data', 'ShaderCache'),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Google', 'Chrome', 'User Data', 'GrShaderCache'),
            ]

            for temp_pattern in temp_dirs:
                try:
                    import glob
                    for temp_dir in glob.glob(temp_pattern):
                        if os.path.exists(temp_dir):
                            shutil.rmtree(temp_dir, ignore_errors=True)
                except:
                    pass

            # 清理系统临时文件
            try:
                temp_dir = os.environ.get('TEMP', '')
                if temp_dir:
                    for item in os.listdir(temp_dir):
                        if 'chrome' in item.lower() or 'chromium' in item.lower():
                            item_path = os.path.join(temp_dir, item)
                            try:
                                if os.path.isdir(item_path):
                                    shutil.rmtree(item_path, ignore_errors=True)
                                else:
                                    os.remove(item_path)
                            except:
                                pass
            except:
                pass

            logger.info(f"[{self.group_id}] 完成深度清理浏览器数据")

        except Exception as e:
            logger.warning(f"[{self.group_id}] 深度清理浏览器数据时出错: {e}")

    def apply_anti_fingerprint_settings(self, co):
        """应用防指纹设置（仅在代理模式下使用）"""
        try:
            logger.info(f"[{self.group_id}] 应用防指纹浏览器设置...")

            # 生成随机浏览器特征
            browser_profile = self.generate_random_browser_profile()
            if browser_profile:
                co.set_user_agent(browser_profile["user_agent"])
                co.set_argument(f"--window-size={browser_profile['resolution'].replace(',', 'x')}")

                # 随机窗口位置
                random_x = random.randint(0, 200)
                random_y = random.randint(0, 200)
                co.set_argument(f"--window-position={random_x},{random_y}")

                logger.info(f"[{self.group_id}] 已应用随机浏览器特征")

            # 防指纹Chrome参数
            co.set_argument("--disable-canvas-aa")  # 禁用Canvas抗锯齿
            co.set_argument("--disable-2d-canvas-clip-aa")  # 禁用2D Canvas裁剪抗锯齿
            co.set_argument("--disable-gl-drawing-for-tests")  # 禁用GL绘制测试
            co.set_argument("--disable-font-subpixel-positioning")  # 禁用字体子像素定位
            co.set_argument("--disable-lcd-text")  # 禁用LCD文本渲染
            co.set_argument("--disable-sensors")  # 禁用传感器
            co.set_argument("--disable-device-discovery-notifications")  # 禁用设备发现通知
            co.set_argument("--disable-geolocation")  # 禁用地理位置
            co.set_argument("--disable-plugins-discovery")  # 禁用插件发现
            co.set_argument("--disable-speech-api")  # 禁用语音API

            # 设置代理相关偏好
            co.set_pref("webrtc.ip_handling_policy", "disable_non_proxied_udp")
            co.set_pref("webrtc.multiple_routes_enabled", False)
            co.set_pref("webrtc.nonproxied_udp_enabled", False)
            co.set_pref("media.peerconnection.enabled", False)
            co.set_pref("media.peerconnection.ice.default_address_only", True)
            co.set_pref("media.peerconnection.ice.no_host", True)

            logger.info(f"[{self.group_id}] 防指纹设置应用完成")

        except Exception as e:
            logger.warning(f"[{self.group_id}] 应用防指纹设置失败: {e}")

    def apply_proxy_anti_fingerprint_measures(self):
        """在代理模式下应用额外的防指纹措施"""
        try:
            logger.info(f"[{self.group_id}] 应用代理模式防指纹措施...")

            # 注入反指纹脚本
            self.inject_anti_fingerprint_script()

            # 验证代理有效性
            self.verify_proxy_effectiveness()

            # 检查WebRTC泄露
            self.check_webrtc_leak()

            logger.info(f"[{self.group_id}] 代理模式防指纹措施应用完成")

        except Exception as e:
            logger.warning(f"[{self.group_id}] 应用代理防指纹措施失败: {e}")
        except Exception as e:
            logger.error(f"[{self.group_id}] 生成OAuth参数失败: {e}")
            return False

    def get_access_token_and_refresh_token(self, token):
        """根据token获取accessToken和refreshToken"""
        try:
            logger.info(f"[{self.group_id}] 开始获取accessToken和refreshToken")

            # 处理token格式
            if not "user_01" in token:
                # 这里简化处理，实际可能需要JWT解析
                # 假设token格式需要调整
                processed_token = f"user_01::{token}"
            else:
                processed_token = token

            # URL编码处理
            encoded_token = quote(processed_token, safe='')

            # 调用API获取tokens
            api_url = f"https://api2.cursor.sh/auth/poll?uuid={self.uuid}&verifier={self.verifier}"
            logger.info(f"[{self.group_id}] 调用API: {api_url}")

            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"[{self.group_id}] API响应: {result}")

                # 提取accessToken和refreshToken
                access_token = result.get('accessToken')
                refresh_token = result.get('refreshToken')

                if access_token and refresh_token:
                    logger.info(f"[{self.group_id}] 成功获取tokens")
                    return access_token, refresh_token
                else:
                    logger.error(f"[{self.group_id}] API响应中未找到tokens")
                    return None, None
            else:
                logger.error(f"[{self.group_id}] API请求失败: {response.status_code} - {response.text}")
                return None, None

        except Exception as e:
            logger.error(f"[{self.group_id}] 获取tokens时出错: {e}")
            return None, None
        
    def generate_email(self):
        """生成随机邮箱地址"""
        timestamp = int(time.time())
        # 添加2个随机数字，避免多线程时邮箱号重复
        random_suffix = random.randint(10, 99)
        return f"a965586934{timestamp}{random_suffix}@2925.com"

    def write_failed_email(self, email_data, reason):
        """将失败的邮箱账号写入文件"""
        try:
            with open("2925_fail.txt", "a", encoding="utf-8") as f:
                timestamp = datetime.now().isoformat()
                f.write(f"{timestamp} | {email_data} | {reason}\n")
            logger.info(f"[{self.group_id}] 已记录失败邮箱: {email_data}")
        except Exception as e:
            logger.error(f"[{self.group_id}] 写入失败邮箱文件时出错: {e}")

    def get_new_proxy(self, max_duration_minutes=5):
        """从API获取新的代理IP，支持重试机制和备用API"""
        logger.info(f"[{self.group_id}] 正在获取新代理...")

        # 首先尝试主要API（重试5次）
        if self._try_get_proxy_from_main_api():
            return True

        # 主要API失败后，尝试备用API（查询正在使用的代理）
        logger.warning(f"[{self.group_id}] 主要API获取代理失败，尝试备用API...")
        if self._try_get_proxy_from_backup_api():
            return True

        # 如果备用API也失败，使用原来的循环获取逻辑作为最后手段
        logger.warning(f"[{self.group_id}] 备用API也失败，使用循环获取作为最后手段...")
        return self._try_get_proxy_with_loop(max_duration_minutes)

    def _try_get_proxy_from_main_api(self):
        """尝试从主要API获取代理，重试5次"""
        for attempt in range(1, PROXY_RETRY_TIMES + 1):
            try:
                logger.info(f"[{self.group_id}] 主要API第 {attempt}/{PROXY_RETRY_TIMES} 次尝试...")
                response = requests.get(PROXY_API_URL, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                        proxy_info = data["data"][0]
                        server = proxy_info.get("server", "")
                        if ":" in server:
                            self.proxy_host, self.proxy_port = server.split(":")
                            self.proxy_start_time = time.time()
                            logger.info(f"[{self.group_id}] 主要API成功获取代理: {self.proxy_host}:{self.proxy_port}")
                            return True
                        else:
                            logger.error(f"[{self.group_id}] 代理服务器格式错误: {server}")
                    else:
                        logger.error(f"[{self.group_id}] 主要API返回失败: {data}")
                else:
                    logger.error(f"[{self.group_id}] 主要API请求失败: {response.status_code}")

            except Exception as e:
                logger.error(f"[{self.group_id}] 主要API请求异常: {e}")

            # 如果不是最后一次尝试，等待3秒
            if attempt < PROXY_RETRY_TIMES:
                logger.info(f"[{self.group_id}] {PROXY_RETRY_INTERVAL}秒后重试主要API...")
                time.sleep(PROXY_RETRY_INTERVAL)

        return False

    def _try_get_proxy_from_backup_api(self):
        """尝试从备用API获取正在使用的代理"""
        try:
            logger.info(f"[{self.group_id}] 尝试备用API获取正在使用的代理...")
            response = requests.get(BACKUP_PROXY_API_URL, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                    # 选择第一个可用的代理
                    proxy_info = data["data"][0]
                    server = proxy_info.get("server", "")
                    if ":" in server:
                        self.proxy_host, self.proxy_port = server.split(":")
                        self.proxy_start_time = time.time()
                        deadline = proxy_info.get("deadline", "未知")
                        logger.info(f"[{self.group_id}] 备用API成功获取代理: {self.proxy_host}:{self.proxy_port} (到期时间: {deadline})")
                        return True
                    else:
                        logger.error(f"[{self.group_id}] 备用API代理格式错误: {server}")
                else:
                    logger.error(f"[{self.group_id}] 备用API返回失败: {data}")
            else:
                logger.error(f"[{self.group_id}] 备用API请求失败: {response.status_code}")

        except Exception as e:
            logger.error(f"[{self.group_id}] 备用API请求异常: {e}")

        return False

    def _try_get_proxy_with_loop(self, max_duration_minutes=5):
        """循环获取代理作为最后手段"""
        logger.info(f"[{self.group_id}] 开始循环获取代理（最后手段）...")

        start_time = time.time()
        max_duration_seconds = max_duration_minutes * 60
        attempt = 0

        while True:
            attempt += 1
            elapsed_time = time.time() - start_time

            # 检查是否超过最大持续时间
            if elapsed_time >= max_duration_seconds:
                logger.error(f"[{self.group_id}] 循环获取代理超时，已尝试 {max_duration_minutes} 分钟，共 {attempt-1} 次尝试")
                return False

            try:
                logger.info(f"[{self.group_id}] 循环第 {attempt} 次尝试获取代理...")
                response = requests.get(PROXY_API_URL, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                        proxy_info = data["data"][0]
                        server = proxy_info.get("server", "")
                        if ":" in server:
                            self.proxy_host, self.proxy_port = server.split(":")
                            self.proxy_start_time = time.time()
                            logger.info(f"[{self.group_id}] 循环获取成功: {self.proxy_host}:{self.proxy_port} (尝试 {attempt} 次)")
                            return True
                        else:
                            logger.error(f"[{self.group_id}] 循环获取代理格式错误: {server}")
                    else:
                        logger.error(f"[{self.group_id}] 循环获取失败: {data}")
                else:
                    logger.error(f"[{self.group_id}] 循环获取请求失败: {response.status_code}")
            except Exception as e:
                logger.error(f"[{self.group_id}] 循环获取异常: {e}")

            # 如果还没超时，等待3秒后重试
            remaining_time = max_duration_seconds - elapsed_time
            if remaining_time > 3:
                logger.info(f"[{self.group_id}] 3秒后继续循环重试... (剩余时间: {remaining_time:.1f}秒)")
                time.sleep(3)
            else:
                logger.error(f"[{self.group_id}] 剩余时间不足3秒，停止循环重试")
                return False

    def test_proxy(self, max_test_attempts=10):
        """测试当前代理是否可用，如果不可用则重新获取，直到可用或达到最大尝试次数"""
        # 首先检查代理是否过期
        if self.is_proxy_expired():
            logger.info(f"[{self.group_id}] 当前代理已过期，尝试获取新代理")
            if not self.get_new_proxy():
                logger.error(f"[{self.group_id}] 获取新代理失败")
                return False
            logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")

        for attempt in range(1, max_test_attempts + 1):
            try:
                test_url = "https://www.google.com"
                test_proxies = {
                    "http": f"http://{self.proxy_host}:{self.proxy_port}",
                    "https": f"http://{self.proxy_host}:{self.proxy_port}"
                }
                response = requests.get(test_url, proxies=test_proxies, timeout=5)
                if response.status_code == 200:
                    logger.info(f"[{self.group_id}] 代理测试成功")
                    return True
                else:
                    logger.error(f"[{self.group_id}] 代理测试失败: {response.status_code}")
            except Exception as e:
                logger.error(f"[{self.group_id}] 代理测试异常: {e}")

            # 如果测试失败且未达到最大尝试次数，则重新获取代理
            if attempt < max_test_attempts:
                logger.info(f"[{self.group_id}] 尝试获取新代理 (测试尝试 {attempt}/{max_test_attempts})")
                if not self.get_new_proxy():
                    logger.error(f"[{self.group_id}] 获取新代理失败，停止测试")
                    return False
                logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")

        logger.error(f"[{self.group_id}] 多次尝试后未能获取可用代理")
        return False

    def verify_proxy_effectiveness(self):
        """验证代理是否真正生效，检查IP泄露风险"""
        if not USE_PROXY or not self.page:
            return True

        try:
            logger.info(f"[{self.group_id}] 开始验证代理有效性...")

            # 简化验证：直接访问目标网站测试连通性
            test_url = "https://www.google.com"
            self.page.get(test_url, timeout=15)
            time.sleep(2)

            # 检查页面是否正常加载
            if "google" in self.page.title.lower() or self.page.url.startswith("https://www.google"):
                logger.info(f"[{self.group_id}] [OK] 代理连接正常，可以访问外网")
                return True
            else:
                logger.warning(f"[{self.group_id}] [WARN] 代理连接可能有问题，但继续执行")
                return True  # 继续执行，不阻断流程

        except Exception as e:
            logger.warning(f"[{self.group_id}] 代理验证失败: {e}")
            logger.info(f"[{self.group_id}] 跳过代理验证，继续执行注册流程")
            return True  # 验证失败不影响继续执行

        return True

    def check_webrtc_leak(self):
        """检查WebRTC是否可能泄露真实IP"""
        if not USE_PROXY or not self.page:
            return True

        try:
            logger.info(f"[{self.group_id}] 检查WebRTC泄露风险...")

            # 注入JavaScript检测WebRTC
            webrtc_check_script = """
            return new Promise((resolve) => {
                if (!window.RTCPeerConnection && !window.webkitRTCPeerConnection && !window.mozRTCPeerConnection) {
                    resolve({webrtc_disabled: true, ips: []});
                    return;
                }

                const RTCPeerConnection = window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection;
                const ips = [];

                try {
                    const pc = new RTCPeerConnection({iceServers: [{urls: "stun:stun.l.google.com:19302"}]});

                    pc.createDataChannel("");
                    pc.createOffer().then(offer => pc.setLocalDescription(offer));

                    pc.onicecandidate = (event) => {
                        if (event.candidate) {
                            const candidate = event.candidate.candidate;
                            const ip = candidate.match(/([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/);
                            if (ip) ips.push(ip[1]);
                        }
                    };

                    setTimeout(() => {
                        pc.close();
                        resolve({webrtc_disabled: false, ips: [...new Set(ips)]});
                    }, 3000);
                } catch (e) {
                    resolve({webrtc_disabled: true, ips: [], error: e.message});
                }
            });
            """

            result = self.page.run_js(webrtc_check_script)

            if result:
                if result.get("webrtc_disabled"):
                    logger.info(f"[{self.group_id}] [OK] WebRTC已禁用，无泄露风险")
                    return True
                else:
                    detected_ips = result.get("ips", [])
                    if detected_ips:
                        logger.warning(f"[{self.group_id}] [WARN] WebRTC检测到IP: {detected_ips}")
                        # 这里可以进一步检查这些IP是否是本地IP
                        local_ips = [ip for ip in detected_ips if ip.startswith(('192.168.', '10.', '172.')) or ip == '127.0.0.1']
                        if local_ips:
                            logger.warning(f"[{self.group_id}] [WARN] 检测到本地IP: {local_ips}")
                        return True
                    else:
                        logger.info(f"[{self.group_id}] [OK] WebRTC未检测到IP泄露")
                        return True

        except Exception as e:
            logger.warning(f"[{self.group_id}] WebRTC检测失败: {e}")

        return True
    def setup_browser(self, use_proxy=False):
        """设置浏览器，优化启动速度"""
        logger.info(f"[{self.group_id}] 开始设置浏览器...")

        # 彻底清理浏览器配置文件夹，确保每次都是全新环境
        try:
            if os.path.exists(self.profile_path):
                import shutil
                # 多次尝试删除，确保删除成功
                for attempt in range(3):
                    try:
                        shutil.rmtree(self.profile_path)
                        logger.info(f"[{self.group_id}] 已删除旧的浏览器配置文件夹: {self.profile_path}")
                        break
                    except Exception as e:
                        if attempt < 2:
                            logger.warning(f"[{self.group_id}] 删除配置文件夹失败，第 {attempt+1} 次重试...")
                            time.sleep(1)
                        else:
                            logger.warning(f"[{self.group_id}] 删除旧的浏览器配置文件夹时出错: {e}")
        except Exception as e:
            logger.warning(f"[{self.group_id}] 清理浏览器配置时出错: {e}")

        # 创建新的目录
        os.makedirs(self.profile_path, exist_ok=True)
        logger.info(f"[{self.group_id}] 已创建全新的浏览器配置目录")

        try:
            # 记录启动前的浏览器进程
            self.before_pids = self.get_browser_processes()

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 设置用户数据目录
            co.set_user_data_path(self.profile_path)

            # 如果是代理模式，应用防指纹设置
            if use_proxy and USE_PROXY:
                self.apply_anti_fingerprint_settings(co)

            # 优化浏览器启动参数 - 提高性能，减少资源使用
            co.set_argument("--incognito")  # 隐身模式
            co.set_argument("--disable-extensions")  # 禁用扩展
            co.set_argument("--disable-gpu")  # 禁用GPU加速
            co.set_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
            co.set_argument("--disable-infobars")  # 禁用信息栏
            co.set_argument("--disable-browser-side-navigation")  # 禁用浏览器侧导航
            co.set_argument("--disable-features=TranslateUI,BlinkGenPropertyTrees")  # 禁用翻译UI等
            co.set_argument("--disable-notifications")  # 禁用通知
            co.set_argument("--disable-popup-blocking")  # 禁用弹窗阻止
            co.set_argument("--disable-background-timer-throttling")  # 禁用后台计时器限制
            co.set_argument("--disable-backgrounding-occluded-windows")  # 禁用背景窗口
            co.set_argument("--disable-breakpad")  # 禁用崩溃报告
            co.set_argument("--disable-client-side-phishing-detection")  # 禁用钓鱼检测
            co.set_argument("--disable-default-apps")  # 禁用默认应用
            co.set_argument("--disable-hang-monitor")  # 禁用挂起监视器
            co.set_argument("--disable-prompt-on-repost")  # 禁用重新发布提示
            co.set_argument("--disable-sync")  # 禁用同步
            co.set_argument("--no-first-run")  # 禁止首次运行
            co.set_argument("--no-default-browser-check")  # 禁止默认浏览器检查
            co.set_argument("--disable-webrtc")  # 禁用WebRTC
            co.set_argument("--enforce-webrtc-ip-permission-check")  # 强制WebRTC IP权限检查

            # 设置代理（如果启用）
            if use_proxy and USE_PROXY and self.proxy_host and self.proxy_port:
                co.set_proxy(f"{self.proxy_host}:{self.proxy_port}")
                logger.info(f"[{self.group_id}] 已设置浏览器代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.info(f"[{self.group_id}] 当前不使用代理")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动浏览器
            start_time = time.time()
            logger.info(f"[{self.group_id}] 启动浏览器")
            self.page = ChromiumPage(co)

            # 等待浏览器准备就绪
            time.sleep(1)

            # 显示启动用时
            elapsed = time.time() - start_time
            logger.info(f"[{self.group_id}] 浏览器启动完成，用时 {elapsed:.2f} 秒")

            # 记录新增的浏览器进程
            self.track_browser_processes()

            # 如果是代理模式，应用额外的防指纹措施
            if use_proxy and USE_PROXY:
                try:
                    self.apply_proxy_anti_fingerprint_measures()
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 应用防指纹措施出错: {e}")

            return True

        except Exception as e:
            logger.exception(f"[{self.group_id}] 设置浏览器时出现异常: {e}")
            return False

    def get_default_browser_path(self):
        """获取默认浏览器路径"""
        # 简化示例，实际使用时可以依据不同操作系统获取更精确的路径
        if sys.platform == "win32":
            return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        elif sys.platform == "darwin":
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            return "/usr/bin/google-chrome"

    def get_browser_processes(self):
        """获取当前浏览器进程ID列表"""
        try:
            browser_process_names = ['chrome', 'chromium', 'msedge', 'edge', 'brave']
            return [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in browser_process_names)]
        except:
            return []

    def track_browser_processes(self):
        """跟踪新启动的浏览器进程"""
        global _chrome_process_ids
        try:
            after_pids = self.get_browser_processes()
            new_pids = [pid for pid in after_pids if pid not in self.before_pids]
            _chrome_process_ids.extend(new_pids)

            if new_pids:
                logger.info(f"[{self.group_id}] 跟踪到 {len(new_pids)} 个新浏览器进程")
            else:
                logger.warning(f"[{self.group_id}] 未检测到新的浏览器进程")
        except Exception as e:
            logger.error(f"[{self.group_id}] 跟踪进程失败: {e}")

    def cleanup(self):
        """清理资源，更彻底地清理浏览器"""
        try:
            # 关闭浏览器
            if self.page:
                # 先清除浏览器缓存
                try:
                    self.page.run_cdp('Network.clearBrowserCache')
                    self.page.run_cdp('Network.clearBrowserCookies')
                    self.page.run_cdp('Storage.clearDataForOrigin', {'origin': '*', 'storageTypes': 'all'})
                    logger.info(f"[{self.group_id}] 已清除浏览器缓存和Cookie")
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 清除缓存失败: {e}")

                # 关闭浏览器
                self.page.quit()
                time.sleep(1)

            # 强制结束可能残留的进程
            try:
                for pid in self.get_browser_processes():
                    if pid in self.before_pids:
                        continue  # 跳过启动前已存在的进程
                    if sys.platform == "win32":
                        os.system(f'taskkill /F /PID {pid} /T >nul 2>&1')
                    else:
                        try:
                            os.kill(pid, signal.SIGKILL)
                        except:
                            pass
                logger.info(f"[{self.group_id}] 已尝试强制结束残留进程")
            except Exception as e:
                logger.warning(f"[{self.group_id}] 结束残留进程出错: {e}")

            # 删除配置文件目录
            try:
                import shutil
                if os.path.exists(self.profile_path):
                    shutil.rmtree(self.profile_path)
                    logger.info(f"[{self.group_id}] 已删除浏览器配置目录")
                os.makedirs(self.profile_path, exist_ok=True)
            except Exception as e:
                logger.error(f"[{self.group_id}] 删除配置目录时出错: {e}")

            logger.info(f"[{self.group_id}] 资源清理完成")
        except Exception as e:
            logger.error(f"[{self.group_id}] 清理资源时出错: {e}")

    def handle_turnstile(self):
        """处理 Turnstile 验证"""
        logger.info(f"[{self.group_id}] 开始处理 Turnstile 人机验证...")

        # 等待turnstile元素出现
        turnstile = self.page.ele("@id=cf-turnstile", timeout=5)
        if not turnstile:
            logger.error(f"[{self.group_id}] 未找到 cf-turnstile 元素")
            return False

        try:
            # 将turnstile元素滚动到视图中并点击以获取焦点
            self.page.run_js("arguments[0].scrollIntoView(true);", turnstile)
            turnstile.click()
            logger.info(f"[{self.group_id}] 已点击Turnstile容器获取焦点")
            time.sleep(1)

            # 发送Tab键聚焦到复选框，然后发送空格键进行点击
            self.page.actions.key_down('TAB')
            self.page.actions.key_up('TAB')
            time.sleep(1)
            self.page.actions.key_down('space')
            self.page.actions.key_up('space')
            logger.info(f"[{self.group_id}] 已发送Tab和Space键")
            time.sleep(2)

            # 检查验证结果
            if self.check_verification_success():
                logger.info(f"[{self.group_id}] Turnstile 验证通过")
                return True

            logger.warning(f"[{self.group_id}] Turnstile 验证未通过")
            return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 处理验证时出错: {e}")
            return False

    def check_verification_success(self):
        """检查验证是否成功"""
        try:
            # 检查是否存在后续表单元素
            if (self.page.ele("@name=password", timeout=0.5) or
                self.page.ele("@name=email", timeout=0.5) or
                self.page.ele("@data-index=0", timeout=0.5) or
                self.page.ele("Account Settings", timeout=0.5)):
                return True

            # 检查是否有错误信息
            error_xpaths = [
                'xpath://div[contains(text(), "Can\'t verify the user is human")]',
                'xpath://div[contains(text(), "Error: 600010")]',
                'xpath://div[contains(text(), "Please try again")]'
            ]

            for xpath in error_xpaths:
                if self.page.ele(xpath):
                    return False

            return False
        except:
            return False

    def input_verification_code(self, code):
        """输入验证码"""
        if not code or len(code) != 6:
            logger.error(f"[{self.group_id}] 无效的验证码: {code}")
            return False

        try:
            # 查找验证码输入框
            for i, digit in enumerate(code):
                input_ele = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='" + str(i) + "']")
                if input_ele:
                    input_ele.input(digit)
                    time.sleep(0.2)
                else:
                    logger.error(f"[{self.group_id}] 未找到第{i}个验证码输入框")
                    return False

            logger.info(f"[{self.group_id}] 验证码输入成功")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 输入验证码出错: {e}")
            return False

    def get_email_code(self, email):
        """从邮件中获取验证码"""
        try:
            # 准备请求参数
            request_kwargs = {
                "params": {"email": email},
                "timeout": 10
            }

            # 添加代理配置（如果启用）
            # if USE_PROXY:
            #     request_kwargs["proxies"] = {
            #         "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
            #         "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
            #     }

            response = requests.get(f"{API_BASE_URL}{EMAIL_CODE_ENDPOINT}", **request_kwargs)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and data.get("data"):
                    logger.info(f"[{self.group_id}] 获取到邮箱验证码: {data.get('data')}")
                    return data.get("data")
                else:
                    logger.error(f"[{self.group_id}] 获取邮箱"+email+"验证码失败: {data.get('message')}")
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取邮箱验证码时出错: {e}")
            return None

    def get_token(self):
        """从cookies中提取WorkosCursorSessionToken"""
        try:
            cookies = self.page.cookies()
            for cookie in cookies:
                if cookie.get('name') == 'WorkosCursorSessionToken':
                    token = cookie.get('value')
                    # if token and '::' in token:
                    #     return token.split('::')[1]
                    return token
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取token出错: {e}")
            return None

    def submit_token(self, email, token, access_token=None, refresh_token=None):
        """将token提交到API（异步方式）"""
        # 创建异步线程执行提交操作
        submit_thread = threading.Thread(
            target=self._async_submit_token,
            args=(email, token, access_token, refresh_token),
            daemon=True
        )
        submit_thread.start()
        # 立即返回，不等待提交完成
        return True

    def _async_submit_token(self, email, token, access_token=None, refresh_token=None):
        """异步执行token提交和日志记录"""
        try:
            # 准备请求参数
            submit_data = {
                "email": email,
                "token": token
            }

            # 添加accessToken和refreshToken（如果有）
            if access_token:
                submit_data["accessToken"] = access_token
            if refresh_token:
                submit_data["refreshToken"] = refresh_token

            request_kwargs = {
                "json": submit_data,
                "timeout": 20
            }

            # 添加代理配置（如果启用）
            # if USE_PROXY:
            #     request_kwargs["proxies"] = {
            #         "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
            #         "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
            #     }

            response = requests.post(f"{API_BASE_URL}{TOKEN_SUBMIT_ENDPOINT}", **request_kwargs)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info(f"[{self.group_id}] 成功提交{email}的token和tokens (异步)")
                    if self.success_callback:
                        self.success_callback(self.group_id, email, token)
                    return

            logger.error(f"[{self.group_id}] 提交token失败: {response.status_code} - {response.text}")
            if self.error_callback:
                self.error_callback(self.group_id, email, "提交token失败")
        except Exception as e:
            logger.error(f"[{self.group_id}] 提交token时出错: {e}")
            if self.error_callback:
                self.error_callback(self.group_id, email, f"提交token异常: {str(e)}")

    def process_registration(self):
        """处理注册流程"""
        if self.current_attempt >= self.max_attempts:
            logger.warning(f"[{self.group_id}] 达到最大尝试次数 {self.max_attempts}，终止当前注册")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, f"达到最大尝试次数 {self.max_attempts}")
            # 重置浏览器以便下次注册
            try:
                if self.page:
                    self.page.quit()
                    time.sleep(2)

                # 根据代理配置决定如何重置浏览器
                if USE_PROXY and USE_PROXY_FULL_PROCESS:
                    # 全程代理模式：检查代理是否过期
                    if self.is_proxy_expired():
                        logger.info(f"[{self.group_id}] 代理已过期，获取新代理...")
                        if not self.get_new_proxy():
                            logger.error(f"[{self.group_id}] 获取新代理失败")
                            return False
                    self.setup_browser(use_proxy=True)
                    logger.info(f"[{self.group_id}] 尝试失败后已重置浏览器（全程代理模式）")
                else:
                    # 其他模式：无代理重置
                    self.setup_browser(use_proxy=False)
                    if USE_PROXY and not USE_PROXY_FULL_PROCESS:
                        logger.info(f"[{self.group_id}] 尝试失败后已重置浏览器（部分代理模式）")
                    else:
                        logger.info(f"[{self.group_id}] 尝试失败后已重置浏览器（无代理模式）")
            except Exception as e:
                logger.error(f"[{self.group_id}] 重置浏览器失败: {e}")
            return False

        self.current_attempt += 1
        logger.info(f"[{self.group_id}] 执行注册尝试 {self.current_attempt}/{self.max_attempts}")

        # 等待页面加载
        time.sleep(3)
        has_overlay = bool(self.page.ele('xpath://div[@inert and @aria-hidden="true" and not(@class)]',timeout=1))
        # 1. 查找并点击magic-code按钮

        if not has_overlay:
            magic_code_button = self.page.ele("xpath://button[@name='intent' and @type='submit' and @value='magic-code' and not(@data-disabled='true')]",timeout=1)
            if bool(magic_code_button):
                logger.info(f"[{self.group_id}] 找到magic-code按钮，点击")
                try:
                    magic_code_button.click()
                except ElementLostError:
                    logger.info(f"[{self.group_id}] 点击按钮后元素失效，属正常跳转，已忽略")
                time.sleep(3)

        # 2. 检查是否存在人机验证
        turnstile = self.page.ele("@id=cf-turnstile", timeout=1)
        if has_overlay :
            if bool(turnstile):
                logger.info(f"[{self.group_id}] 检测到人机验证")
                self.handle_turnstile()
                time.sleep(3)

        # 3. 检查是否存在验证码输入框
        code_input = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='0']", timeout=1)
        if bool(code_input):
            logger.info(f"[{self.group_id}] 检测到验证码输入框，尝试获取验证码")
            self.process_verification_code()
            return True

        # 等待一段时间后继续尝试
        time.sleep(2)
        return self.process_registration()

    def process_verification_code(self, attempt=0):
        """处理验证码获取和输入"""
        if attempt >= 30:
            logger.error(f"[{self.group_id}] 验证码获取失败，达到最大尝试次数")
            # 记录失败的邮箱账号
            if self.email:
                self.write_failed_email(self.email, "验证码获取失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "验证码获取失败")
            return

        code = self.get_email_code(self.email)

        if not code:
            logger.info(f"[{self.group_id}] 尚未获取到验证码，3秒后重试 ({attempt + 1}/30)")
            time.sleep(3)
            self.process_verification_code(attempt + 1)
            return

        logger.info(f"[{self.group_id}] 获取到验证码: {code}")

        # 根据代理配置决定处理方式
        if (USE_PROXY and USE_PROXY_FULL_PROCESS) or not USE_PROXY:
            # 全程使用代理模式或无代理模式：直接输入验证码，不重启浏览器
            if USE_PROXY and USE_PROXY_FULL_PROCESS:
                logger.info(f"[{self.group_id}] 全程代理模式，直接输入验证码，不重启浏览器")
            else:
                logger.info(f"[{self.group_id}] 无代理模式，直接输入验证码，不重启浏览器")

            # 直接输入验证码
            if not self.input_verification_code(code):
                logger.error(f"[{self.group_id}] 验证码输入失败")
                # 记录失败的邮箱账号
                if self.email:
                    self.write_failed_email(self.email, "验证码输入失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "验证码输入失败")
                return

            # 等待页面加载完成
            time.sleep(5)

            # 检查是否跳转到登录确认页面
            current_url = self.page.url
            if "loginDeepControl" in current_url:
                logger.info(f"[{self.group_id}] 检测到登录确认页面，查找登录按钮")
                # 查找并点击登录按钮
                login_button = self.page.ele("xpath://button[.//span[contains(text(), 'Log In')]]", timeout=10)
                if login_button:
                    logger.info(f"[{self.group_id}] 找到登录按钮，点击确认")
                    login_button.click()
                    time.sleep(5)
                else:
                    logger.warning(f"[{self.group_id}] 未找到登录按钮")

            # 获取并提交token
            self.get_and_submit_token()

        else:
            # 部分代理模式：重启浏览器并启用代理（原有逻辑）
            # 仅当USE_PROXY=True且USE_PROXY_FULL_PROCESS=False时
            logger.info(f"[{self.group_id}] 部分代理模式，准备重启浏览器并启用代理")
            use_proxy_for_restart = True

            # 保存当前URL和验证码
            current_url = self.page.url

            # 获取新代理（部分代理模式必须获取代理）
            # 检查当前代理是否过期
            if self.is_proxy_expired():
                logger.info(f"[{self.group_id}] 当前代理已过期，正在获取新的代理IP...")
                self.get_new_proxy()
                logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.info(f"[{self.group_id}] 当前代理仍在有效期内，继续使用当前代理")

            # 关闭浏览器
            if self.page:
                self.page.quit()
                time.sleep(2)

            # 重启浏览器
            if not self.setup_browser(use_proxy=use_proxy_for_restart):
                logger.error(f"[{self.group_id}] 重启浏览器失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "重启浏览器失败")
                return

            # 重新访问页面
            self.page.get(current_url)
            time.sleep(3)

            # 输入验证码
            proxy_status = "启用代理后" if use_proxy_for_restart else "无代理模式下"
            logger.info(f"[{self.group_id}] {proxy_status}，开始输入验证码")
            if not self.input_verification_code(code):
                logger.error(f"[{self.group_id}] 验证码输入失败")
                # 记录失败的邮箱账号
                if self.email:
                    self.write_failed_email(self.email, "验证码输入失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "验证码输入失败")
                return

            # 等待页面加载完成
            time.sleep(5)

            # 检查是否跳转到登录确认页面
            current_url = self.page.url
            if "loginDeepControl" in current_url:
                logger.info(f"[{self.group_id}] 检测到登录确认页面，查找登录按钮")
                # 查找并点击登录按钮
                login_button = self.page.ele("xpath://button[.//span[contains(text(), 'Log In')]]", timeout=10)
                if login_button:
                    logger.info(f"[{self.group_id}] 找到登录按钮，点击确认")
                    login_button.click()
                    time.sleep(5)
                else:
                    logger.warning(f"[{self.group_id}] 未找到登录按钮")

            # 获取并提交token
            self.get_and_submit_token()

    def get_and_submit_token(self):
        """获取并提交token"""
        try:
            # 检查是否跳转到cursor.com
            current_url = self.page.url

            # 先处理信用卡绑定
            logger.info(f"[{self.group_id}] 开始处理信用卡绑定流程")
            if not self.handle_credit_card_binding():
                logger.error(f"[{self.group_id}] 信用卡绑定失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "信用卡绑定失败")
                return

            # 获取token
            time.sleep(5)
            token = self.get_token()
            if not token:
                logger.error(f"[{self.group_id}] 获取token失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "获取token失败")
                return

            # 获取accessToken和refreshToken
            access_token, refresh_token = self.get_access_token_and_refresh_token(token)

            if access_token and refresh_token:
                logger.info(f"[{self.group_id}] 成功获取accessToken和refreshToken")
                # 提交token和tokens到API (异步)
                self.submit_token(self.email, token, access_token, refresh_token)
                logger.info(f"[{self.group_id}] 注册流程完成: {self.email} (token和tokens提交已触发异步处理)")
            else:
                logger.warning(f"[{self.group_id}] 未能获取accessToken和refreshToken，仅提交原token")
                # 仅提交原token到API (异步)
                self.submit_token(self.email, token)
                logger.info(f"[{self.group_id}] 注册流程完成: {self.email} (仅token提交已触发异步处理)")

        except Exception as e:
            logger.error(f"[{self.group_id}] token处理过程中出错: {e}")
        finally:
            # 清理资源并重启为无代理模式，为下一轮注册做准备
            try:
                logger.info(f"[{self.group_id}] 当前注册流程已完成，清理资源并准备下一轮")
                
                # 使用cleanup彻底清理当前浏览器
                self.cleanup()
                
                # 重启浏览器（根据代理配置）
                time.sleep(1)  # 等待资源释放

                # 根据代理配置决定如何重启浏览器
                if USE_PROXY and USE_PROXY_FULL_PROCESS:
                    # 全程代理模式：检查代理是否过期
                    if self.is_proxy_expired():
                        logger.info(f"[{self.group_id}] 代理已过期，获取新代理...")
                        if not self.get_new_proxy():
                            logger.error(f"[{self.group_id}] 获取新代理失败")

                    if self.setup_browser(use_proxy=True):
                        logger.info(f"[{self.group_id}] 已重启浏览器为全程代理模式，准备下一轮注册")
                    else:
                        logger.error(f"[{self.group_id}] 重启浏览器失败")
                else:
                    # 其他模式：无代理重启
                    if self.setup_browser(use_proxy=False):
                        if USE_PROXY and not USE_PROXY_FULL_PROCESS:
                            logger.info(f"[{self.group_id}] 已重启浏览器为部分代理模式，准备下一轮注册")
                        else:
                            logger.info(f"[{self.group_id}] 已重启浏览器为无代理模式，准备下一轮注册")
                    else:
                        logger.error(f"[{self.group_id}] 重启浏览器失败")
            except Exception as e:
                logger.error(f"[{self.group_id}] 重置浏览器过程中出错: {e}")
                # 即使出错也尝试再次启动
                try:
                    time.sleep(2)  # 多等待一会儿
                    if USE_PROXY and USE_PROXY_FULL_PROCESS:
                        self.setup_browser(use_proxy=True)
                        logger.info(f"[{self.group_id}] 二次尝试重启浏览器成功（全程代理模式）")
                    else:
                        self.setup_browser(use_proxy=False)
                        if USE_PROXY and not USE_PROXY_FULL_PROCESS:
                            logger.info(f"[{self.group_id}] 二次尝试重启浏览器成功（部分代理模式）")
                        else:
                            logger.info(f"[{self.group_id}] 二次尝试重启浏览器成功（无代理模式）")
                except:
                    logger.error(f"[{self.group_id}] 二次尝试重启浏览器也失败，将在下一轮注册时重试")
        
    def run(self):
        """运行注册流程"""
        # 根据代理配置初始化浏览器
        if USE_PROXY and USE_PROXY_FULL_PROCESS:
            # 全程代理模式：先获取代理，然后启用代理初始化浏览器
            logger.info(f"[{self.group_id}] 全程代理模式，正在获取代理...")
            if not self.get_new_proxy():
                logger.error(f"[{self.group_id}] 获取代理失败，线程终止")
                if self.error_callback:
                    self.error_callback(self.group_id, "None", "获取代理失败")
                return

            if not self.setup_browser(use_proxy=True):
                logger.error(f"[{self.group_id}] 浏览器设置失败，线程终止")
                if self.error_callback:
                    self.error_callback(self.group_id, "None", "浏览器初始化失败")
                return
            logger.info(f"[{self.group_id}] 浏览器初始化成功（全程代理模式），准备开始注册流程")
        else:
            # 其他模式：无代理初始化浏览器
            # 包括：USE_PROXY=False 或 USE_PROXY=True但USE_PROXY_FULL_PROCESS=False
            if not self.setup_browser(use_proxy=False):
                logger.error(f"[{self.group_id}] 浏览器设置失败，线程终止")
                if self.error_callback:
                    self.error_callback(self.group_id, "None", "浏览器初始化失败")
                return

            if USE_PROXY and not USE_PROXY_FULL_PROCESS:
                logger.info(f"[{self.group_id}] 浏览器初始化成功（部分代理模式），准备开始注册流程")
            else:
                logger.info(f"[{self.group_id}] 浏览器初始化成功（无代理模式），准备开始注册流程")

        # 运行指定次数的注册流程
        registration_count = 0

        try:
            while registration_count < DEFAULT_REGISTRATION_COUNT:
                registration_count += 1
                # 记录开始时间
                registration_start_time = time.time()
                logger.info(f"[{self.group_id}] 开始第 {registration_count}/{DEFAULT_REGISTRATION_COUNT} 次注册流程")

                # 每次注册都重新初始化浏览器（确保环境干净）
                if registration_count > 1:
                    logger.info(f"[{self.group_id}] 第 {registration_count} 次注册，重新初始化浏览器以确保环境干净")
                    # 关闭之前的浏览器
                    if hasattr(self, 'page') and self.page:
                        try:
                            self.page.quit()
                            time.sleep(2)  # 等待浏览器完全关闭
                        except:
                            pass

                    # 根据代理配置重新初始化浏览器
                    if USE_PROXY and USE_PROXY_FULL_PROCESS:
                        # 全程代理模式：检查代理是否过期，如果过期则获取新代理
                        if self.is_proxy_expired():
                            logger.info(f"[{self.group_id}] 代理已过期，获取新代理...")
                            if not self.get_new_proxy():
                                logger.error(f"[{self.group_id}] 获取新代理失败，跳过本次注册")
                                continue

                        if not self.setup_browser(use_proxy=True):
                            logger.error(f"[{self.group_id}] 浏览器设置失败，跳过本次注册")
                            continue
                    else:
                        # 其他模式：无代理初始化
                        if not self.setup_browser(use_proxy=False):
                            logger.error(f"[{self.group_id}] 浏览器设置失败，跳过本次注册")
                            continue

                # 重置当前尝试次数
                self.current_attempt = 0

                # 生成新邮箱
                self.email = self.generate_email()
                if not self.email:
                    logger.error(f"[{self.group_id}] 获取邮箱失败，跳过本次注册")
                    continue

                logger.info(f"[{self.group_id}] 准备注册新账号: {self.email}")

                # 生成OAuth参数
                if not self.generate_oauth_params():
                    logger.error(f"[{self.group_id}] 生成OAuth参数失败，跳过本次注册")
                    continue

                # 构建state参数
                state_data = {
                    "returnTo": f"https://cursor.com/cn/loginDeepControl?challenge={self.challenge}&uuid={self.uuid}&mode=login"
                }
                state_json = json.dumps(state_data)
                encoded_state = quote(state_json)

                # 加载注册页面（无代理模式）
                encoded_email = quote(self.email)
                url = SIGNUP_URL.format(encoded_email, encoded_state)
                logger.info(f"[{self.group_id}] 访问注册URL: {url}")
                self.page.get(url)

                # 记录当前代理状态（此时应该是无代理模式）
                logger.info(f"[{self.group_id}] 开始注册 - 当前无代理模式（用于通过人机验证）")

                # 开始处理注册
                time.sleep(2)
                self.process_registration()
                
                # 记录结束时间和耗时
                registration_end_time = time.time()
                elapsed_time = registration_end_time - registration_start_time
                logger.info(f"[{self.group_id}] 完成注册流程: {self.email} - 耗时: {elapsed_time:.2f} 秒")

                # 每次注册完成后进行清理，为下次注册准备干净环境
                if registration_count < DEFAULT_REGISTRATION_COUNT:  # 如果不是最后一次注册
                    logger.info(f"[{self.group_id}] 注册完成，准备清理环境为下次注册做准备")
                    try:
                        # 清理浏览器存储数据
                        if hasattr(self, 'page') and self.page:
                            try:
                                # 清理所有存储数据
                                self.page.run_js("localStorage.clear();")
                                self.page.run_js("sessionStorage.clear();")
                                self.page.run_js("indexedDB.databases().then(dbs => dbs.forEach(db => indexedDB.deleteDatabase(db.name)));")
                                logger.info(f"[{self.group_id}] 已清理浏览器存储数据")
                            except Exception as e:
                                logger.warning(f"[{self.group_id}] 清理浏览器存储数据失败: {e}")
                    except Exception as e:
                        logger.warning(f"[{self.group_id}] 清理环境时出错: {e}")

                # 根据注册耗时动态调整休息时间
                # if elapsed_time >= 90:
                #     rest_time = 2
                # else:
                #     rest_time = 92 - elapsed_time

                # logger.info(f"[{self.group_id}] 注册耗时 {elapsed_time:.2f} 秒，休息 {rest_time:.2f} 秒")
                time.sleep(2)
            
            logger.info(f"[{self.group_id}] 完成全部 {DEFAULT_REGISTRATION_COUNT} 次注册流程")
        finally:
            # 所有注册完成后，清理资源
            self.cleanup()
            logger.info(f"[{self.group_id}] 线程资源已清理")

    def is_proxy_expired(self):
        """检查当前代理是否已过期"""
        if not self.proxy_start_time:
            return True  # 如果没有开始时间，视为已过期
            
        current_time = time.time()
        elapsed_minutes = (current_time - self.proxy_start_time) / 60
        
        if elapsed_minutes >= PROXY_LIFETIME_MINUTES:
            logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，超过设定的 {PROXY_LIFETIME_MINUTES} 分钟有效期")
            return True
            
        logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，还在有效期内 ({PROXY_LIFETIME_MINUTES} 分钟)")
        return False

    def log_proxy_status(self, stage=""):
        """记录当前代理状态"""
        if USE_PROXY and self.proxy_host and self.proxy_port:
            logger.info(f"[{self.group_id}] {stage} - 当前使用代理: {self.proxy_host}:{self.proxy_port}")
        else:
            logger.info(f"[{self.group_id}] {stage} - 当前无代理模式")

    def format_expiry_date(self, expiry_str):
        """格式化有效期，确保年份只有两位数"""
        try:
            if not expiry_str:
                return expiry_str

            # 如果包含斜杠，分割处理
            if '/' in expiry_str:
                parts = expiry_str.split('/')
                if len(parts) == 2:
                    month = parts[0].strip()
                    year = parts[1].strip()

                    # 确保月份是两位数
                    if len(month) == 1:
                        month = '0' + month

                    # 确保年份是两位数
                    if len(year) == 4:
                        year = year[-2:]  # 取后两位
                    elif len(year) == 1:
                        year = '0' + year

                    formatted = f"{month}/{year}"
                    logger.info(f"[{self.group_id}] 有效期格式化: {expiry_str} -> {formatted}")
                    return formatted

            # 如果不包含斜杠，直接返回
            return expiry_str

        except Exception as e:
            logger.warning(f"[{self.group_id}] 格式化有效期时出错: {e}，使用原始值: {expiry_str}")
            return expiry_str

    def handle_credit_card_binding(self):
        """处理信用卡绑定流程，使用无代理浏览器"""
        try:
            logger.info(f"[{self.group_id}] 开始处理信用卡绑定流程")

            # 检查是否有Continue按钮
            continue_button = self.page.ele("xpath://button[text()='Continue']", timeout=5)
            if not continue_button:
                logger.info(f"[{self.group_id}] 未找到Continue按钮，可能已跳过")
                return False

            logger.info(f"[{self.group_id}] 发现需要绑定信用卡，准备切换到无代理浏览器")

            # 保存当前状态
            current_url = self.page.url
            session_token = self.get_session_token()

            if not session_token:
                logger.error(f"[{self.group_id}] 无法获取WorkosCursorSessionToken")
                return False

            logger.info(f"[{self.group_id}] 已保存URL: {current_url}")
            logger.info(f"[{self.group_id}] 已保存SessionToken: {session_token[:20]}...")

            # 关闭当前浏览器
            logger.info(f"[{self.group_id}] 关闭当前代理浏览器")
            self.page.quit()
            time.sleep(2)

            # 创建无代理浏览器进行绑卡
            binding_result = self.bind_card_with_no_proxy_browser(current_url, session_token)

            if binding_result:
                logger.info(f"[{self.group_id}] 信用卡绑定成功")
                return True
            else:
                logger.error(f"[{self.group_id}] 信用卡绑定失败")
                return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 信用卡绑定流程异常: {e}")
            return False

    def get_session_token(self):
        """获取WorkosCursorSessionToken"""
        try:
            # 获取所有cookies
            cookies = self.page.cookies()

            for cookie in cookies:
                if cookie.get('name') == 'WorkosCursorSessionToken':
                    logger.info(f"[{self.group_id}] 找到WorkosCursorSessionToken")
                    return cookie.get('value')

            logger.warning(f"[{self.group_id}] 未找到WorkosCursorSessionToken")
            return None

        except Exception as e:
            logger.error(f"[{self.group_id}] 获取SessionToken时出错: {e}")
            return None

    def bind_card_with_no_proxy_browser(self, url, session_token):
        """使用无代理浏览器进行信用卡绑定"""
        no_proxy_browser = None
        max_retry_attempts = 2  # 最多重试2次

        try:
            logger.info(f"[{self.group_id}] 创建无代理浏览器")

            # 创建无代理浏览器
            no_proxy_browser = self.create_no_proxy_browser()
            if not no_proxy_browser:
                logger.error(f"[{self.group_id}] 创建无代理浏览器失败")
                return False

            # 先访问cursor.com主页设置cookie
            logger.info(f"[{self.group_id}] 先访问cursor.com主页设置cookie")
            no_proxy_browser.get("https://cursor.com")
            time.sleep(3)

            # 设置SessionToken cookie到cursor.com域名下
            logger.info(f"[{self.group_id}] 设置WorkosCursorSessionToken到cursor.com域名")
            no_proxy_browser.set.cookies([{
                'name': 'WorkosCursorSessionToken',
                'value': session_token,
                'domain': 'cursor.com',
                'path': '/'
            }])

            # 现在访问保存的URL
            logger.info(f"[{self.group_id}] 访问保存的URL: {url}")
            no_proxy_browser.get(url)
            time.sleep(5)

            # 等待页面加载
            self.wait_for_page_load_in_browser(no_proxy_browser)

            # 点击Continue按钮
            continue_button = no_proxy_browser.ele("xpath://button[text()='Continue']", timeout=10)
            if not continue_button:
                logger.error(f"[{self.group_id}] 在无代理浏览器中未找到Continue按钮")
                return False

            logger.info(f"[{self.group_id}] 在无代理浏览器中点击Continue按钮")
            continue_button.click()
            time.sleep(5)

            # 等待页面加载
            self.wait_for_page_load_in_browser(no_proxy_browser)

            # 查找信用卡选项按钮
            card_button = no_proxy_browser.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
            if not card_button:
                logger.error(f"[{self.group_id}] 在无代理浏览器中未找到信用卡选项按钮")
                return False

            logger.info(f"[{self.group_id}] 在无代理浏览器中找到信用卡选项按钮，点击")
            card_button.click()
            time.sleep(3)

            # 等待信用卡表单加载
            self.wait_for_credit_card_form_in_browser(no_proxy_browser)

            # 重试绑定信用卡
            for attempt in range(max_retry_attempts):
                logger.info(f"[{self.group_id}] 无代理浏览器信用卡绑定尝试 {attempt + 1}/{max_retry_attempts}")

                # 读取信用卡信息
                card_info = self.load_and_remove_card_info()
                if not card_info:
                    logger.error(f"[{self.group_id}] 无法获取信用卡信息")
                    return False

                # 根据是否是第一次尝试选择填充方式
                if attempt == 0:
                    # 第一次尝试：填充完整的信用卡表单
                    if not self.fill_credit_card_form_in_browser(no_proxy_browser, card_info):
                        logger.error(f"[{self.group_id}] 在无代理浏览器中填充信用卡信息失败")
                        if attempt < max_retry_attempts - 1:
                            logger.info(f"[{self.group_id}] 准备重试...")
                            continue
                        return False
                else:
                    # 重试时：只更新核心信息
                    if not self.update_credit_card_core_info_in_browser(no_proxy_browser, card_info):
                        logger.error(f"[{self.group_id}] 在无代理浏览器中更新信用卡核心信息失败")
                        if attempt < max_retry_attempts - 1:
                            logger.info(f"[{self.group_id}] 准备重试...")
                            continue
                        return False

                # 提交表单并检查结果
                binding_result = self.submit_credit_card_form_in_browser(no_proxy_browser)

                if binding_result:
                    logger.info(f"[{self.group_id}] 无代理浏览器信用卡绑定成功")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 无代理浏览器信用卡绑定失败 (尝试 {attempt + 1}/{max_retry_attempts})")
                    if attempt < max_retry_attempts - 1:
                        logger.info(f"[{self.group_id}] 准备使用新的信用卡信息重试...")
                        time.sleep(2)
                        continue
                    else:
                        logger.error(f"[{self.group_id}] 无代理浏览器信用卡绑定失败，已达到最大重试次数")
                        return False

            return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 无代理浏览器绑卡异常: {e}")
            return False
        finally:
            # 确保关闭无代理浏览器
            if no_proxy_browser:
                try:
                    logger.info(f"[{self.group_id}] 关闭无代理浏览器")
                    no_proxy_browser.quit()
                    time.sleep(2)
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 关闭无代理浏览器时出错: {e}")

    def extract_domain(self, url):
        """从URL中提取域名"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            domain = parsed.netloc
            # 如果域名以www开头，去掉www
            if domain.startswith('www.'):
                domain = domain[4:]
            # 如果域名为空，返回默认值
            if not domain:
                domain = "cursor.com"
            logger.info(f"[{self.group_id}] 提取域名: {domain}")
            return domain
        except Exception as e:
            logger.error(f"[{self.group_id}] 提取域名失败: {e}")
            return "cursor.com"  # 默认域名

    def create_no_proxy_browser(self):
        """创建无代理浏览器"""
        try:
            logger.info(f"[{self.group_id}] 创建无代理浏览器")

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 使用临时用户数据目录
            no_proxy_profile_path = f"chrome-data-no-proxy/group_{self.group_id}"
            os.makedirs(no_proxy_profile_path, exist_ok=True)
            co.set_user_data_path(no_proxy_profile_path)

            # 设置无痕模式
            co.set_argument("--incognito")
            logger.info(f"[{self.group_id}] 已启用无代理浏览器无痕模式")

            # 基本浏览器参数（无代理）
            co.set_argument("--disable-extensions")
            co.set_argument("--disable-gpu")
            co.set_argument("--disable-dev-shm-usage")
            co.set_argument("--disable-infobars")
            co.set_argument("--disable-notifications")
            co.set_argument("--no-first-run")
            co.set_argument("--no-default-browser-check")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动无代理浏览器
            no_proxy_browser = ChromiumPage(co)
            time.sleep(2)

            logger.info(f"[{self.group_id}] 无代理浏览器创建成功")
            return no_proxy_browser

        except Exception as e:
            logger.error(f"[{self.group_id}] 创建无代理浏览器失败: {e}")
            return None

    def wait_for_page_load_in_browser(self, browser):
        """在指定浏览器中等待页面完全加载"""
        try:
            logger.info(f"[{self.group_id}] 等待页面完全加载...")

            # 等待页面加载完成
            time.sleep(3)

            # 检查页面是否加载完成
            for i in range(10):  # 最多等待10秒
                try:
                    # 检查页面状态
                    ready_state = browser.run_js("return document.readyState")
                    if ready_state == "complete":
                        logger.info(f"[{self.group_id}] 页面加载完成")
                        break
                except:
                    pass
                time.sleep(1)

            # 额外等待确保所有元素都加载完成
            time.sleep(2)

        except Exception as e:
            logger.warning(f"[{self.group_id}] 等待页面加载时出错: {e}")

    def wait_for_credit_card_form_in_browser(self, browser):
        """在指定浏览器中等待信用卡表单完全加载"""
        try:
            logger.info(f"[{self.group_id}] 等待信用卡表单加载...")

            # 等待卡号输入框出现
            for i in range(15):  # 最多等待15秒
                card_number_input = browser.ele("xpath://input[@id='cardNumber']", timeout=1)
                if card_number_input:
                    logger.info(f"[{self.group_id}] 信用卡表单加载完成")
                    break
                time.sleep(1)
            else:
                logger.warning(f"[{self.group_id}] 信用卡表单加载超时")

            # 额外等待确保表单完全可用
            time.sleep(2)

        except Exception as e:
            logger.warning(f"[{self.group_id}] 等待信用卡表单加载时出错: {e}")

    def wait_for_page_load(self):
        """等待页面完全加载"""
        try:
            logger.info(f"[{self.group_id}] 等待页面完全加载...")

            # 等待页面加载完成
            time.sleep(3)

            # 检查页面是否加载完成
            for i in range(10):  # 最多等待10秒
                try:
                    # 检查页面状态
                    ready_state = self.page.run_js("return document.readyState")
                    if ready_state == "complete":
                        logger.info(f"[{self.group_id}] 页面加载完成")
                        break
                except:
                    pass
                time.sleep(1)

            # 额外等待确保所有元素都加载完成
            time.sleep(2)

        except Exception as e:
            logger.warning(f"[{self.group_id}] 等待页面加载时出错: {e}")

    def wait_for_credit_card_form(self):
        """等待信用卡表单完全加载"""
        try:
            logger.info(f"[{self.group_id}] 等待信用卡表单加载...")

            # 等待卡号输入框出现
            for i in range(15):  # 最多等待15秒
                card_number_input = self.page.ele("xpath://input[@id='cardNumber']", timeout=1)
                if card_number_input:
                    logger.info(f"[{self.group_id}] 信用卡表单加载完成")
                    break
                time.sleep(1)
            else:
                logger.warning(f"[{self.group_id}] 信用卡表单加载超时")

            # 额外等待确保表单完全可用
            time.sleep(2)

        except Exception as e:
            logger.warning(f"[{self.group_id}] 等待信用卡表单加载时出错: {e}")

    def fill_credit_card_form(self, card_info):
        """填充信用卡表单（使用直接input方式）"""
        try:
            logger.info(f"[{self.group_id}] 开始填充信用卡表单")

            # 1. 填充卡号
            card_number_input = self.page.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                card_number_input.clear()
                card_number_input.input(str(card_info.get('CardNumber', '')))
                logger.info(f"[{self.group_id}] 已填充卡号")
            else:
                logger.error(f"[{self.group_id}] 未找到卡号输入框")
                return False

            time.sleep(1)

            # 2. 填充有效期
            card_expiry_input = self.page.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                card_expiry_input.clear()
                card_expiry_input.input(str(formatted_expiry))
                logger.info(f"[{self.group_id}] 已填充有效期")
            else:
                logger.error(f"[{self.group_id}] 未找到有效期输入框")
                return False

            time.sleep(1)

            # 3. 填充CVV
            card_cvc_input = self.page.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                card_cvc_input.clear()
                card_cvc_input.input(str(card_info.get('CVV', '')))
                logger.info(f"[{self.group_id}] 已填充CVV")
            else:
                logger.error(f"[{self.group_id}] 未找到CVV输入框")
                return False

            time.sleep(1)

            # 4. 填充姓名
            billing_name_input = self.page.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                billing_name_input.clear()
                billing_name_input.input(str(card_info.get('Name', '')))
                logger.info(f"[{self.group_id}] 已填充姓名")
            else:
                logger.error(f"[{self.group_id}] 未找到姓名输入框")
                return False

            time.sleep(1)

            # 继续填充其他字段
            return self.fill_remaining_fields(card_info)

        except Exception as e:
            logger.error(f"[{self.group_id}] 填充信用卡表单异常: {e}")
            return False

    def fill_remaining_fields(self, card_info):
        """填充剩余的地址等字段"""
        try:
            # 5. 选择国家 CN
            billing_country_select = self.page.ele("@id=billingCountry", timeout=10)
            if billing_country_select:
                try:
                    billing_country_select.select.by_value('CN')
                    logger.info(f"[{self.group_id}] 已选择国家 CN")
                    self.page.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                except:
                    try:
                        billing_country_select.select.by_text('China')
                        logger.info(f"[{self.group_id}] 已选择国家 China")
                        self.page.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                    except:
                        logger.warning(f"[{self.group_id}] 选择国家失败，继续其他字段")

            time.sleep(3)  # 等待省份下拉框加载

            # 6. 填充地址
            billing_address_input = self.page.ele("xpath://input[@id='billingAddressLine1']", timeout=10)
            if billing_address_input:
                billing_address_input.clear()
                billing_address_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充地址")

            time.sleep(1)

            # 7. 填充邮政编码
            billing_postal_input = self.page.ele("xpath://input[@id='billingPostalCode']", timeout=10)
            if billing_postal_input:
                billing_postal_input.clear()
                billing_postal_input.input("353000")
                logger.info(f"[{self.group_id}] 已填充邮政编码")

            time.sleep(1)

            # 8. 填充城市
            billing_locality_input = self.page.ele("xpath://input[@id='billingLocality']", timeout=10)
            if billing_locality_input:
                billing_locality_input.clear()
                billing_locality_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充城市")

            time.sleep(1)

            # 9. 选择省份
            billing_admin_select = self.page.ele("@id=billingAdministrativeArea", timeout=10)
            if billing_admin_select:
                try:
                    options = billing_admin_select.eles("tag:option")
                    for option in options:
                        if '福建' in option.text:
                            option.click()
                            logger.info(f"[{self.group_id}] 已选择省份: {option.text}")
                            break
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 选择省份时出错: {e}")

            time.sleep(1)

            # 10. 填充区域
            billing_dependent_input = self.page.ele("xpath://input[@id='billingDependentLocality']", timeout=10)
            if billing_dependent_input:
                billing_dependent_input.clear()
                billing_dependent_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充区域")

            time.sleep(3)
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 填充剩余字段异常: {e}")
            return False

    def update_credit_card_core_info(self, card_info):
        """只更新信用卡核心信息（卡号、有效期、CVV、姓名）"""
        try:
            logger.info(f"[{self.group_id}] 开始更新信用卡核心信息")

            # 1. 更新卡号
            card_number_input = self.page.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                card_number_input.clear()
                card_number_input.input(str(card_info.get('CardNumber', '')))
                logger.info(f"[{self.group_id}] 已更新卡号")
            else:
                logger.error(f"[{self.group_id}] 未找到卡号输入框")
                return False

            time.sleep(1)

            # 2. 更新有效期
            card_expiry_input = self.page.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                card_expiry_input.clear()
                card_expiry_input.input(str(formatted_expiry))
                logger.info(f"[{self.group_id}] 已更新有效期")
            else:
                logger.error(f"[{self.group_id}] 未找到有效期输入框")
                return False

            time.sleep(1)

            # 3. 更新CVV
            card_cvc_input = self.page.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                card_cvc_input.clear()
                card_cvc_input.input(str(card_info.get('CVV', '')))
                logger.info(f"[{self.group_id}] 已更新CVV")
            else:
                logger.error(f"[{self.group_id}] 未找到CVV输入框")
                return False

            time.sleep(1)

            # 4. 更新姓名
            billing_name_input = self.page.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                billing_name_input.clear()
                billing_name_input.input(str(card_info.get('Name', '')))
                logger.info(f"[{self.group_id}] 已更新姓名")
            else:
                logger.error(f"[{self.group_id}] 未找到姓名输入框")
                return False

            time.sleep(2)
            logger.info(f"[{self.group_id}] 信用卡核心信息更新完成")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 更新信用卡核心信息异常: {e}")
            return False

    def submit_credit_card_form_with_timeout(self):
        """提交信用卡表单并处理人机验证"""
        try:
            logger.info(f"[{self.group_id}] 开始提交信用卡表单")

            # 点击提交按钮
            submit_button = self.page.ele("xpath://button[@data-testid='hosted-payment-submit-button']", timeout=10)
            if not submit_button:
                logger.error(f"[{self.group_id}] 未找到提交按钮")
                return False

            logger.info(f"[{self.group_id}] 找到提交按钮，点击")
            submit_button.click()
            time.sleep(5)

            # 检查是否出现人机验证 - 修复识别问题
            hcaptcha_elements = [
                "xpath://div[@id='h-captcha']",
                "xpath://iframe[contains(@src,'hcaptcha')]",
                "xpath://div[contains(@class,'h-captcha')]",
                "xpath://div[contains(@class,'hcaptcha')]"
            ]

            hcaptcha_found = False
            hcaptcha_root = None
            for selector in hcaptcha_elements:
                hcaptcha_root = self.page.ele(selector, timeout=3)
                if hcaptcha_root:
                    logger.info(f"[{self.group_id}] 检测到人机验证: {selector}")
                    hcaptcha_found = True
                    break

            if hcaptcha_found:
                logger.info(f"[{self.group_id}] 检测到人机验证，尝试处理")
                try:
                    # 根据检测到的元素类型进行不同处理
                    if hcaptcha_root:
                        # 检查是否是iframe
                        if hcaptcha_root.tag == 'iframe':
                            logger.info(f"[{self.group_id}] 检测到hCaptcha iframe，尝试切换到iframe内部")
                            try:
                                # 切换到iframe
                                self.page.set.window.to_frame(hcaptcha_root)
                                time.sleep(2)

                                # 在iframe内查找可点击的元素
                                captcha_checkbox = self.page.ele("xpath://div[@class='check']", timeout=5)
                                if not captcha_checkbox:
                                    captcha_checkbox = self.page.ele("xpath://div[contains(@class,'checkbox')]", timeout=5)
                                if not captcha_checkbox:
                                    captcha_checkbox = self.page.ele("xpath://div[contains(@class,'captcha')]", timeout=5)

                                if captcha_checkbox:
                                    logger.info(f"[{self.group_id}] 在iframe中找到验证框，点击")
                                    captcha_checkbox.click()
                                    time.sleep(2)
                                else:
                                    logger.warning(f"[{self.group_id}] 在iframe中未找到可点击的验证元素")

                                # 切换回主页面
                                self.page.set.window.to_main()
                                time.sleep(1)

                            except Exception as iframe_e:
                                logger.warning(f"[{self.group_id}] iframe处理失败: {iframe_e}")
                                # 切换回主页面
                                try:
                                    self.page.set.window.to_main()
                                except:
                                    pass
                        else:
                            # 不是iframe，直接点击
                            logger.info(f"[{self.group_id}] 检测到hCaptcha div，直接点击")
                            hcaptcha_root.click()
                            time.sleep(1)

                    # 使用键盘操作作为备选方案
                    logger.info(f"[{self.group_id}] 尝试键盘操作")
                    self.page.actions.key_down('TAB')
                    self.page.actions.key_up('TAB')
                    time.sleep(1)
                    self.page.actions.key_down('space')
                    self.page.actions.key_up('space')
                    time.sleep(2)

                    # 等待人机验证完成
                    logger.info(f"[{self.group_id}] 等待人机验证完成...")
                    for i in range(60):
                        time.sleep(1)
                        current_url = self.page.url
                        if "cursor.com" in current_url:
                            logger.info(f"[{self.group_id}] 人机验证完成，已跳转到cursor.com")
                            return True

                        # 检查是否还有验证框存在
                        if i % 10 == 0:  # 每10秒检查一次
                            still_has_captcha = False
                            for selector in hcaptcha_elements:
                                if self.page.ele(selector, timeout=1):
                                    still_has_captcha = True
                                    break
                            if not still_has_captcha:
                                logger.info(f"[{self.group_id}] 验证框已消失，检查页面状态")
                                time.sleep(2)
                                current_url = self.page.url
                                if "cursor.com" in current_url:
                                    logger.info(f"[{self.group_id}] 验证完成，已跳转到cursor.com")
                                    return True

                    logger.warning(f"[{self.group_id}] 人机验证超时")
                    return False

                except Exception as e:
                    logger.error(f"[{self.group_id}] 处理人机验证时出错: {e}")
                    # 确保切换回主页面
                    try:
                        self.page.set.window.to_main()
                    except:
                        pass
                    return False
            else:
                logger.info(f"[{self.group_id}] 未检测到人机验证，检查是否绑定成功")
                time.sleep(3)

                # 检查是否绑定成功
                current_url = self.page.url
                if "cursor.com" in current_url:
                    logger.info(f"[{self.group_id}] 信用卡绑定成功，已跳转到cursor.com")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 信用卡绑定可能失败，当前URL: {current_url}")
                    return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 提交信用卡表单异常: {e}")
            return False

    def fill_credit_card_form_in_browser(self, browser, card_info):
        """在指定浏览器中填充信用卡表单"""
        try:
            logger.info(f"[{self.group_id}] 开始在无代理浏览器中填充信用卡表单")

            # 1. 填充卡号
            card_number_input = browser.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                card_number_input.clear()
                card_number_input.input(str(card_info.get('CardNumber', '')))
                logger.info(f"[{self.group_id}] 已填充卡号")
            else:
                logger.error(f"[{self.group_id}] 未找到卡号输入框")
                return False

            time.sleep(1)

            # 2. 填充有效期
            card_expiry_input = browser.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                card_expiry_input.clear()
                card_expiry_input.input(str(formatted_expiry))
                logger.info(f"[{self.group_id}] 已填充有效期")
            else:
                logger.error(f"[{self.group_id}] 未找到有效期输入框")
                return False

            time.sleep(1)

            # 3. 填充CVV
            card_cvc_input = browser.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                card_cvc_input.clear()
                card_cvc_input.input(str(card_info.get('CVV', '')))
                logger.info(f"[{self.group_id}] 已填充CVV")
            else:
                logger.error(f"[{self.group_id}] 未找到CVV输入框")
                return False

            time.sleep(1)

            # 4. 填充姓名
            billing_name_input = browser.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                billing_name_input.clear()
                billing_name_input.input(str(card_info.get('Name', '')))
                logger.info(f"[{self.group_id}] 已填充姓名")
            else:
                logger.error(f"[{self.group_id}] 未找到姓名输入框")
                return False

            time.sleep(1)

            # 继续填充其他字段
            return self.fill_remaining_fields_in_browser(browser, card_info)

        except Exception as e:
            logger.error(f"[{self.group_id}] 在无代理浏览器中填充信用卡表单异常: {e}")
            return False

    def fill_remaining_fields_in_browser(self, browser, card_info):
        """在指定浏览器中填充剩余的地址等字段"""
        try:
            # 5. 选择国家 CN
            billing_country_select = browser.ele("@id=billingCountry", timeout=10)
            if billing_country_select:
                try:
                    billing_country_select.select.by_value('CN')
                    logger.info(f"[{self.group_id}] 已选择国家 CN")
                    browser.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                except:
                    try:
                        billing_country_select.select.by_text('China')
                        logger.info(f"[{self.group_id}] 已选择国家 China")
                        browser.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                    except:
                        logger.warning(f"[{self.group_id}] 选择国家失败，继续其他字段")

            time.sleep(3)  # 等待省份下拉框加载

            # 6. 填充地址
            billing_address_input = browser.ele("xpath://input[@id='billingAddressLine1']", timeout=10)
            if billing_address_input:
                billing_address_input.clear()
                billing_address_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充地址")

            time.sleep(1)

            # 7. 填充邮政编码
            billing_postal_input = browser.ele("xpath://input[@id='billingPostalCode']", timeout=10)
            if billing_postal_input:
                billing_postal_input.clear()
                billing_postal_input.input("353000")
                logger.info(f"[{self.group_id}] 已填充邮政编码")

            time.sleep(1)

            # 8. 填充城市
            billing_locality_input = browser.ele("xpath://input[@id='billingLocality']", timeout=10)
            if billing_locality_input:
                billing_locality_input.clear()
                billing_locality_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充城市")

            time.sleep(1)

            # 9. 选择省份
            billing_admin_select = browser.ele("@id=billingAdministrativeArea", timeout=10)
            if billing_admin_select:
                try:
                    options = billing_admin_select.eles("tag:option")
                    for option in options:
                        if '福建' in option.text:
                            option.click()
                            logger.info(f"[{self.group_id}] 已选择省份: {option.text}")
                            break
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 选择省份时出错: {e}")

            time.sleep(1)

            # 10. 填充区域
            billing_dependent_input = browser.ele("xpath://input[@id='billingDependentLocality']", timeout=10)
            if billing_dependent_input:
                billing_dependent_input.clear()
                billing_dependent_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充区域")

            time.sleep(3)
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 在无代理浏览器中填充剩余字段异常: {e}")
            return False

    def update_credit_card_core_info_in_browser(self, browser, card_info):
        """在指定浏览器中只更新信用卡核心信息"""
        try:
            logger.info(f"[{self.group_id}] 开始在无代理浏览器中更新信用卡核心信息")

            # 1. 更新卡号
            card_number_input = browser.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                card_number_input.clear()
                card_number_input.input(str(card_info.get('CardNumber', '')))
                logger.info(f"[{self.group_id}] 已更新卡号")
            else:
                logger.error(f"[{self.group_id}] 未找到卡号输入框")
                return False

            time.sleep(1)

            # 2. 更新有效期
            card_expiry_input = browser.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                card_expiry_input.clear()
                card_expiry_input.input(str(formatted_expiry))
                logger.info(f"[{self.group_id}] 已更新有效期")
            else:
                logger.error(f"[{self.group_id}] 未找到有效期输入框")
                return False

            time.sleep(1)

            # 3. 更新CVV
            card_cvc_input = browser.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                card_cvc_input.clear()
                card_cvc_input.input(str(card_info.get('CVV', '')))
                logger.info(f"[{self.group_id}] 已更新CVV")
            else:
                logger.error(f"[{self.group_id}] 未找到CVV输入框")
                return False

            time.sleep(1)

            # 4. 更新姓名
            billing_name_input = browser.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                billing_name_input.clear()
                billing_name_input.input(str(card_info.get('Name', '')))
                logger.info(f"[{self.group_id}] 已更新姓名")
            else:
                logger.error(f"[{self.group_id}] 未找到姓名输入框")
                return False

            time.sleep(2)
            logger.info(f"[{self.group_id}] 无代理浏览器信用卡核心信息更新完成")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 在无代理浏览器中更新信用卡核心信息异常: {e}")
            return False

    def submit_credit_card_form_in_browser(self, browser):
        """在指定浏览器中提交信用卡表单"""
        try:
            logger.info(f"[{self.group_id}] 开始在无代理浏览器中提交信用卡表单")

            # 点击提交按钮
            submit_button = browser.ele("xpath://button[@data-testid='hosted-payment-submit-button']", timeout=10)
            if not submit_button:
                logger.error(f"[{self.group_id}] 在无代理浏览器中未找到提交按钮")
                return False

            logger.info(f"[{self.group_id}] 在无代理浏览器中找到提交按钮，点击")
            submit_button.click()
            time.sleep(5)

            # 检查是否出现人机验证 - 使用改进的检测方式
            hcaptcha_elements = [
                "xpath://div[@id='h-captcha']",
                "xpath://iframe[contains(@src,'hcaptcha')]",
                "xpath://div[contains(@class,'h-captcha')]",
                "xpath://div[contains(@class,'hcaptcha')]"
            ]

            hcaptcha_found = False
            hcaptcha_root = None
            for selector in hcaptcha_elements:
                hcaptcha_root = browser.ele(selector, timeout=3)
                if hcaptcha_root:
                    logger.info(f"[{self.group_id}] 在无代理浏览器中检测到人机验证: {selector}")
                    hcaptcha_found = True
                    break

            if hcaptcha_found:
                logger.info(f"[{self.group_id}] 在无代理浏览器中检测到人机验证，尝试处理")
                try:
                    # 根据检测到的元素类型进行不同处理
                    if hcaptcha_root:
                        # 检查是否是iframe
                        if hcaptcha_root.tag == 'iframe':
                            logger.info(f"[{self.group_id}] 检测到hCaptcha iframe，尝试切换到iframe内部")
                            try:
                                # 切换到iframe
                                browser.set.window.to_frame(hcaptcha_root)
                                time.sleep(2)

                                # 在iframe内查找可点击的元素
                                captcha_checkbox = browser.ele("xpath://div[@class='check']", timeout=5)
                                if not captcha_checkbox:
                                    captcha_checkbox = browser.ele("xpath://div[contains(@class,'checkbox')]", timeout=5)
                                if not captcha_checkbox:
                                    captcha_checkbox = browser.ele("xpath://div[contains(@class,'captcha')]", timeout=5)

                                if captcha_checkbox:
                                    logger.info(f"[{self.group_id}] 在iframe中找到验证框，点击")
                                    captcha_checkbox.click()
                                    time.sleep(2)
                                else:
                                    logger.warning(f"[{self.group_id}] 在iframe中未找到可点击的验证元素")

                                # 切换回主页面
                                browser.set.window.to_main()
                                time.sleep(1)

                            except Exception as iframe_e:
                                logger.warning(f"[{self.group_id}] iframe处理失败: {iframe_e}")
                                # 切换回主页面
                                try:
                                    browser.set.window.to_main()
                                except:
                                    pass
                        else:
                            # 不是iframe，直接点击
                            logger.info(f"[{self.group_id}] 检测到hCaptcha div，直接点击")
                            hcaptcha_root.click()
                            time.sleep(1)

                    # 使用键盘操作作为备选方案
                    logger.info(f"[{self.group_id}] 尝试键盘操作")
                    browser.actions.key_down('TAB')
                    browser.actions.key_up('TAB')
                    time.sleep(1)
                    browser.actions.key_down('space')
                    browser.actions.key_up('space')
                    time.sleep(2)

                    # 等待人机验证完成
                    logger.info(f"[{self.group_id}] 等待人机验证完成...")
                    for i in range(60):
                        time.sleep(1)
                        current_url = browser.url
                        if "cursor.com" in current_url:
                            logger.info(f"[{self.group_id}] 无代理浏览器人机验证完成，已跳转到cursor.com")
                            return True

                        # 检查是否还有验证框存在
                        if i % 10 == 0:  # 每10秒检查一次
                            still_has_captcha = False
                            for selector in hcaptcha_elements:
                                if browser.ele(selector, timeout=1):
                                    still_has_captcha = True
                                    break
                            if not still_has_captcha:
                                logger.info(f"[{self.group_id}] 验证框已消失，检查页面状态")
                                time.sleep(2)
                                current_url = browser.url
                                if "cursor.com" in current_url:
                                    logger.info(f"[{self.group_id}] 验证完成，已跳转到cursor.com")
                                    return True

                    logger.warning(f"[{self.group_id}] 无代理浏览器人机验证超时")
                    return False

                except Exception as e:
                    logger.error(f"[{self.group_id}] 在无代理浏览器中处理人机验证时出错: {e}")
                    # 确保切换回主页面
                    try:
                        browser.set.window.to_main()
                    except:
                        pass
                    return False
            else:
                logger.info(f"[{self.group_id}] 在无代理浏览器中未检测到人机验证，检查是否绑定成功")
                time.sleep(3)

                # 检查是否绑定成功
                current_url = browser.url
                if "cursor.com" in current_url:
                    logger.info(f"[{self.group_id}] 无代理浏览器信用卡绑定成功，已跳转到cursor.com")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 无代理浏览器信用卡绑定可能失败，当前URL: {current_url}")
                    return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 在无代理浏览器中提交信用卡表单异常: {e}")
            return False

    def load_and_remove_card_info(self):
        """从JSON文件加载信用卡信息，根据USE_FIXED_FIRST_CARD控制是否删除已使用的记录"""
        try:
            import json

            # 读取JSON文件
            with open("ChinaUnionPay.json", "r", encoding="utf-8") as f:
                card_list = json.load(f)

            if not card_list:
                logger.error(f"[{self.group_id}] ChinaUnionPay.json文件为空")
                return None

            # 取第一个信用卡信息
            card_info = card_list[0]
            logger.info(f"[{self.group_id}] 获取信用卡信息: {card_info.get('Name', 'Unknown')}")

            # 根据USE_FIXED_FIRST_CARD控制是否删除记录
            if USE_FIXED_FIRST_CARD:
                logger.info(f"[{self.group_id}] 固定读取模式：使用第一条信用卡数据，不删除文件中的记录")
            else:
                # 删除已使用的记录
                card_list.pop(0)

                # 写回文件
                with open("ChinaUnionPay.json", "w", encoding="utf-8") as f:
                    json.dump(card_list, f, indent=4, ensure_ascii=False)

                logger.info(f"[{self.group_id}] 已从文件中删除使用的信用卡信息，剩余 {len(card_list)} 张卡")

            return card_info

        except Exception as e:
            logger.error(f"[{self.group_id}] 读取信用卡信息失败: {e}")
            return None


class RegistrationManager:
    def __init__(self, num_threads=DEFAULT_THREADS, headless=False):
        self.num_threads = min(num_threads, MAX_THREADS)
        self.headless = headless
        self.registers = []
        self.successful = []
        self.failed = []
        
    def on_registration_finished(self, group_id, email, token):
        self.successful.append({
            "group_id": group_id,
            "email": email,
            "token": token,
            "timestamp": datetime.now().isoformat()
        })
        logger.info(f"注册成功: {email} (组: {group_id})")
            
#         # 将成功信息保存到文件
#         with open("successful_registrations.txt", "a", encoding="utf-8") as f:
#             f.write(f"{datetime.now().isoformat()} | {email} | {token}\n")
            
    def on_registration_error(self, group_id, email, error):
        self.failed.append({
            "group_id": group_id,
            "email": email,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        logger.error(f"注册失败: {email} (组: {group_id}): {error}")
        
    def start(self):
        """开始注册进程"""
        logger.info(f"启动 {self.num_threads} 个注册线程")
        
        # 清理浏览器进程
        cleanup_chrome_processes()
        
        for i in range(self.num_threads):
            group_id = f"{i}"  # 简化为数字索引
            register = DrissionCursorRegister(group_id, self.headless)
            
            # 设置回调
            register.set_callbacks(self.on_registration_finished, self.on_registration_error)
            
            # 创建线程运行注册实例
            thread = threading.Thread(target=register.run, daemon=True)
            
            # 保存实例
            self.registers.append(register)
            
            # 启动线程
            thread.start()
            
        logger.info("所有注册线程已启动")


def cleanup_chrome_processes():
    """清理Chrome进程"""
    global _chrome_process_ids
    
    if not _chrome_process_ids:
        logger.info("没有需要清理的Chrome进程...")
        return
        
    logger.info(f"清理 {len(_chrome_process_ids)} 个Chrome进程...")
    try:
        if os.name == 'nt':
            for pid in _chrome_process_ids:
                try:
                    os.system(f'taskkill /F /PID {pid} /T 2>nul')
                except:
                    pass
        else:
            for pid in _chrome_process_ids:
                try:
                    os.kill(pid, signal.SIGTERM)
                except:
                    pass
        _chrome_process_ids = []
    except Exception as e:
        logger.error(f"清理进程时出错: {e}")


def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info("接收到退出信号，正在清理资源...")
    cleanup_chrome_processes()
    sys.exit(0)


def main():
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 全局变量声明
    global USE_PROXY
    global DEFAULT_REGISTRATION_COUNT
    global PROXY_LIFETIME_MINUTES
    
    # 命令行参数
    parser = argparse.ArgumentParser(description="Cursor自动注册工具")
    parser.add_argument("-t", "--threads", type=int, default=DEFAULT_THREADS,
                        help=f"并行注册线程数量 (最大 {MAX_THREADS})")
    parser.add_argument("--headless", action="store_true", help="使用无头模式运行")
    parser.add_argument("--proxy", action="store_true", help="启用代理")
    parser.add_argument("-n", "--count", type=int, default=DEFAULT_REGISTRATION_COUNT,
                        help="每个线程的注册次数")
    parser.add_argument("-p", "--proxy-lifetime", type=int, default=PROXY_LIFETIME_MINUTES,
                        help="每个代理的有效时间（分钟）")
    args = parser.parse_args()
    
    # 设置代理参数
    USE_PROXY = True
    
    # 设置每个代理的有效时间
    if args.proxy_lifetime and args.proxy_lifetime > 0:
        PROXY_LIFETIME_MINUTES = args.proxy_lifetime
    logger.info(f"已设置每个代理最多使用 {PROXY_LIFETIME_MINUTES} 分钟")
        
    # 设置注册次数
    if args.count and args.count > 0:
        DEFAULT_REGISTRATION_COUNT = args.count
        logger.info(f"每个线程的注册次数已设置为: {DEFAULT_REGISTRATION_COUNT}")
        
    # 启动管理器
    manager = RegistrationManager(args.threads, args.headless)
    manager.start()
    
    # 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到用户中断，正在退出...")
        cleanup_chrome_processes()


if __name__ == "__main__":
    main() 
