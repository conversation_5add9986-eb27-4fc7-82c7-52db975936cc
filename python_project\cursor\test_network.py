#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网络连接
"""

import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_network():
    """测试网络连接"""
    try:
        print("测试网络连接...")
        
        # 测试基本网络连接
        response = requests.get("https://www.baidu.com", timeout=10, verify=False)
        print(f"百度连接测试: {response.status_code}")
        
        # 测试humkt网站连接
        print("测试humkt网站连接...")
        response = requests.get("https://www.humkt.com", timeout=10, verify=False)
        print(f"humkt网站连接测试: {response.status_code}")
        
        # 测试API接口
        print("测试humkt API接口...")
        buy_url = "https://www.humkt.com/api/buy?id=1514&qty=1&token=qUunKMEIzCeAXLB6YOfAfdyyRBgesJ1Q"
        response = requests.get(buy_url, timeout=10, verify=False)
        print(f"API接口测试: {response.status_code}")
        print(f"API响应内容: {response.text}")
        
    except Exception as e:
        print(f"网络测试失败: {e}")

if __name__ == "__main__":
    test_network()
