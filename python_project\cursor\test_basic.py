#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic():
    """基础测试"""
    try:
        logger.info("开始基础测试...")
        
        # 测试导入DrissionPage
        logger.info("测试导入DrissionPage...")
        from DrissionPage import ChromiumPage, ChromiumOptions
        logger.info("DrissionPage导入成功")
        
        # 测试创建浏览器选项
        logger.info("创建浏览器选项...")
        co = ChromiumOptions()
        co.headless(False)
        logger.info("浏览器选项创建成功")
        
        # 测试创建浏览器页面
        logger.info("创建浏览器页面...")
        page = ChromiumPage(co)
        logger.info("浏览器页面创建成功")
        
        # 测试访问网页
        logger.info("访问百度...")
        page.get("https://www.baidu.com")
        time.sleep(3)
        
        logger.info(f"当前页面URL: {page.url}")
        logger.info(f"页面标题: {page.title}")
        
        # 关闭浏览器
        logger.info("关闭浏览器...")
        page.quit()
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic()
