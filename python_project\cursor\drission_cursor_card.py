from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import signal
import random
import logging
import json
import sys
import requests
from datetime import datetime
import threading
import psutil
from colorama import Fore, Style
import argparse

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("vcc_generator.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
VCC_GENERATOR_URL = "https://www.vccgenerator.org/"
JSON_FILE_PATH = "ChinaUnionPay.json"
MAX_CARDS = 1000  # 最大获取卡片数量

# 虚拟卡获取接口URL（基于提供的curl示例）
VCC_API_URL = "https://www.vccgenerator.org/fetchdata/generate-home-credit-card/"

# 全局变量，用于存储浏览器进程ID
_chrome_process_ids = []

class VCCGenerator:
    def __init__(self, headless=False):
        self.headless = headless
        self.page = None
        self.profile_path = "chrome-data-vcc"
        self.before_pids = []
        self.cards_data = []
        self.session_cookies = None
        self.session_headers = None

    def load_existing_data(self):
        """加载已存在的JSON数据"""
        try:
            if os.path.exists(JSON_FILE_PATH):
                with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
                    self.cards_data = json.load(f)
                logger.info(f"已加载 {len(self.cards_data)} 条现有数据")
            else:
                self.cards_data = []
                logger.info("创建新的数据文件")
        except Exception as e:
            logger.error(f"加载数据文件失败: {e}")
            self.cards_data = []

    def save_data(self):
        """保存数据到JSON文件"""
        try:
            with open(JSON_FILE_PATH, 'w', encoding='utf-8') as f:
                json.dump(self.cards_data, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存 {len(self.cards_data)} 条数据到 {JSON_FILE_PATH}")
        except Exception as e:
            logger.error(f"保存数据失败: {e}")

    def setup_browser(self):
        """设置浏览器"""
        logger.info("开始设置浏览器...")

        # 先尝试删除已存在的目录
        try:
            if os.path.exists(self.profile_path):
                import shutil
                shutil.rmtree(self.profile_path)
                logger.info(f"已删除旧的浏览器配置文件夹: {self.profile_path}")
        except Exception as e:
            logger.warning(f"删除旧的浏览器配置文件夹时出错: {e}")

        # 创建新的目录
        os.makedirs(self.profile_path, exist_ok=True)

        try:
            # 记录启动前的浏览器进程
            self.before_pids = self.get_browser_processes()

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 设置用户数据目录
            co.set_user_data_path(self.profile_path)

            # 使用隐身模式
            co.set_argument("--incognito")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动浏览器
            logger.info("启动浏览器")
            self.page = ChromiumPage(co)

            # 等待一段时间确保浏览器完全启动
            time.sleep(2)

            # 记录新增的浏览器进程
            self.track_browser_processes()

            logger.info("浏览器设置完成")
            return True

        except Exception as e:
            logger.exception(f"设置浏览器时出现异常: {e}")
            return False

    def get_default_browser_path(self):
        """获取默认浏览器路径"""
        if sys.platform == "win32":
            return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        elif sys.platform == "darwin":
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            return "/usr/bin/google-chrome"

    def get_browser_processes(self):
        """获取当前浏览器进程ID列表"""
        try:
            browser_process_names = ['chrome', 'chromium', 'msedge', 'edge', 'brave']
            return [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in browser_process_names)]
        except:
            return []

    def track_browser_processes(self):
        """跟踪新启动的浏览器进程"""
        global _chrome_process_ids
        try:
            after_pids = self.get_browser_processes()
            new_pids = [pid for pid in after_pids if pid not in self.before_pids]
            _chrome_process_ids.extend(new_pids)

            if new_pids:
                logger.info(f"跟踪到 {len(new_pids)} 个新浏览器进程")
            else:
                logger.warning("未检测到新的浏览器进程")
        except Exception as e:
            logger.error(f"跟踪进程失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            # 关闭浏览器
            if self.page:
                self.page.quit()

            # 删除配置文件目录
            import shutil
            if os.path.exists(self.profile_path):
                shutil.rmtree(self.profile_path)

            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")

    def open_vcc_website(self):
        """打开VCC生成器网站"""
        try:
            logger.info(f"正在访问 {VCC_GENERATOR_URL}")
            self.page.get(VCC_GENERATOR_URL)
            time.sleep(3)

            # 检查是否有Cloudflare验证
            if self.page.ele('xpath://title[contains(text(), "Just a moment")]', timeout=2):
                logger.info("检测到Cloudflare验证，等待通过...")
                # 等待Cloudflare验证完成
                for i in range(30):  # 最多等待30秒
                    time.sleep(1)
                    if not self.page.ele('xpath://title[contains(text(), "Just a moment")]', timeout=1):
                        logger.info("Cloudflare验证已通过")
                        break
                    if i == 29:
                        logger.error("Cloudflare验证超时")
                        return False

            # 等待页面加载完成
            logger.info("等待页面加载完成...")
            time.sleep(5)

            # 检查并关闭广告弹窗
            self.close_ad_popup()

            # 检查页面是否正常加载
            if self.page.ele('xpath://title[contains(text(), "VCC Generator")]', timeout=5):
                logger.info("页面加载成功")
                return True
            else:
                logger.warning("页面可能未正常加载，但继续尝试")
                return True

        except Exception as e:
            logger.error(f"访问VCC网站失败: {e}")
            return False

    def close_ad_popup(self):
        """关闭广告弹窗"""
        try:
            # 查找广告关闭按钮
            dismiss_button = self.page.ele('@id=dismiss-button', timeout=3)
            if dismiss_button:
                logger.info("发现广告弹窗，正在关闭...")
                dismiss_button.click()
                time.sleep(1)
                logger.info("广告弹窗已关闭")
            else:
                logger.info("未发现广告弹窗")
        except Exception as e:
            logger.warning(f"关闭广告弹窗时出错: {e}")

    def extract_session_info(self):
        """提取浏览器的session信息（cookies和headers）"""
        try:
            # 获取cookies
            cookies = self.page.cookies()
            self.session_cookies = {}
            for cookie in cookies:
                self.session_cookies[cookie['name']] = cookie['value']

            # 获取CSRF token（从cookies或页面中）
            csrf_token = self.session_cookies.get('csrftoken', '')
            if not csrf_token:
                # 尝试从页面中获取CSRF token
                csrf_element = self.page.ele('xpath://input[@name="csrfmiddlewaretoken"]', timeout=2)
                if csrf_element:
                    csrf_token = csrf_element.attr('value')

            # 构建请求头（基于curl示例）
            self.session_headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'origin': 'https://www.vccgenerator.org',
                'priority': 'u=1, i',
                'referer': 'https://www.vccgenerator.org/',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                'x-csrftoken': csrf_token,
                'x-requested-with': 'XMLHttpRequest'
            }

            logger.info(f"已提取 {len(self.session_cookies)} 个cookies")
            logger.info(f"CSRF Token: {csrf_token[:20]}..." if csrf_token else "未找到CSRF Token")
            return True
        except Exception as e:
            logger.error(f"提取session信息失败: {e}")
            return False

    def fetch_vcc_data_via_browser(self):
        """通过浏览器操作获取虚拟卡数据"""
        try:
            logger.info("开始通过浏览器操作获取数据...")

            # 等待页面完全加载
            time.sleep(3)

            # 设置网络监听
            self.setup_network_listener()

            # 尝试找到并设置表单字段
            try:
                # 1. 设置品牌为中国银联 (id=cc-brand)
                brand_select = self.page.ele('@id=cc-brand', timeout=5)
                if brand_select:
                    brand_select.select.by_text('CHINA UNION PAY')
                    logger.info("已设置品牌为 CHINA UNION PAY")
                else:
                    logger.error("未找到品牌选择框 (id=cc-brand)")

                # 2. 设置国家为中国 (id=cc-country)
                country_select = self.page.ele('@id=cc-country', timeout=2)
                if country_select:
                    country_select.select.by_text('CHINA')
                    logger.info("已设置国家为 CHINA")
                else:
                    logger.error("未找到国家选择框 (id=cc-country)")

                # 3. 设置银行 - 尝试多种选择器
                bank_select = None
                bank_selectors = [
                    '@id=cc-bank',
                    '@id=form-control',
                    'xpath://select[contains(@class, "form-control")]',
                    'xpath://select[@name="bank"]'
                ]

                for selector in bank_selectors:
                    bank_select = self.page.ele(selector, timeout=2)
                    if bank_select:
                        logger.info(f"找到银行选择框: {selector}")
                        break

                if bank_select:
                    bank_select.select.by_text('AGRICULTURAL BANK OF CHINA')
                    logger.info("已设置银行为 AGRICULTURAL BANK OF CHINA")
                else:
                    logger.warning("未找到银行选择框，跳过银行设置")

                # 4. 设置数量 (id=cc-amount)
                amount_select = self.page.ele('@id=cc-amount', timeout=2)
                if amount_select:
                    amount_select.select.by_text('20')
                    logger.info("已设置数量为 20")
                else:
                    logger.error("未找到数量选择框 (id=cc-amount)")

                time.sleep(1)

                # 5. 点击生成按钮 (id=first-generate)
                generate_button = self.page.ele('@id=first-generate', timeout=5)
                if generate_button:
                    logger.info("找到生成按钮 (id=first-generate)")
                    logger.info("点击生成按钮...")
                    generate_button.click()

                    # 等待API请求完成并获取响应数据
                    result_data = self.wait_for_api_response()
                    if result_data:
                        return result_data
                    else:
                        logger.error("未能获取到API响应数据")
                        return None
                else:
                    logger.error("未找到生成按钮 (id=first-generate)")
                    return None

            except Exception as e:
                logger.error(f"表单操作失败: {e}")
                return None

        except Exception as e:
            logger.error(f"浏览器操作获取数据失败: {e}")
            return None

    def setup_network_listener(self):
        """设置网络监听"""
        try:
            # 注入JavaScript代码来监听XHR请求
            js_code = """
            // 保存原始的XMLHttpRequest
            const originalXHR = window.XMLHttpRequest;
            window.capturedResponses = [];

            // 重写XMLHttpRequest
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;

                xhr.open = function(method, url, ...args) {
                    this._url = url;
                    this._method = method;
                    return originalOpen.apply(this, [method, url, ...args]);
                };

                xhr.send = function(data) {
                    this.addEventListener('readystatechange', function() {
                        if (this.readyState === 4 && this._url && this._url.includes('fetchdata/generate-home-credit-card')) {
                            try {
                                const responseData = JSON.parse(this.responseText);
                                window.capturedResponses.push({
                                    url: this._url,
                                    method: this._method,
                                    status: this.status,
                                    response: responseData,
                                    timestamp: new Date().toISOString()
                                });
                                console.log('Captured API response:', responseData);
                            } catch (e) {
                                console.error('Failed to parse response:', e);
                            }
                        }
                    });
                    return originalSend.apply(this, arguments);
                };

                return xhr;
            };

            // 保持原型链
            window.XMLHttpRequest.prototype = originalXHR.prototype;
            """

            self.page.run_js(js_code)
            logger.info("网络监听JavaScript已注入")
        except Exception as e:
            logger.error(f"设置网络监听失败: {e}")

    def wait_for_api_response(self):
        """等待并获取API响应数据"""
        try:
            logger.info("等待API响应...")

            # 等待API请求完成
            max_wait_time = 15  # 最多等待15秒
            check_interval = 0.5  # 每0.5秒检查一次

            for i in range(int(max_wait_time / check_interval)):
                try:
                    # 检查是否有捕获到的响应
                    js_code = """
                    if (window.capturedResponses && window.capturedResponses.length > 0) {
                        const latestResponse = window.capturedResponses[window.capturedResponses.length - 1];
                        return latestResponse.response;
                    }
                    return null;
                    """

                    response_data = self.page.run_js(js_code)
                    if response_data:
                        logger.info("成功捕获到API响应数据")
                        logger.info(f"响应数据结构: {response_data}")

                        # 处理响应数据 - 新格式: { "success": true, "creditCard": [...] }
                        if isinstance(response_data, dict):
                            # 检查是否有 creditCard 字段
                            if "creditCard" in response_data and isinstance(response_data["creditCard"], list):
                                credit_cards = response_data["creditCard"]
                                if len(credit_cards) > 0:
                                    # 处理所有卡片数据
                                    all_cards = []
                                    for card_info in credit_cards:
                                        card_data = {
                                            "IssuingNetwork": card_info.get("IssuingNetwork", ""),
                                            "CardNumber": card_info.get("CardNumber", ""),
                                            "Bank": card_info.get("Bank", ""),
                                            "Name": card_info.get("Name", ""),
                                            "Address": card_info.get("Address", ""),
                                            "Country": card_info.get("Country", ""),
                                            "MoneyRange": card_info.get("MoneyRange", ""),
                                            "CVV": card_info.get("CVV", ""),
                                            "Expiry": card_info.get("Expiry", ""),
                                            "Pin": card_info.get("Pin", ""),
                                            "timestamp": datetime.now().isoformat(),
                                            "source": "vccgenerator.org"
                                        }
                                        all_cards.append(card_data)

                                    logger.info(f"成功解析API响应数据 (creditCard格式)，共 {len(all_cards)} 张卡片")
                                    return all_cards
                                else:
                                    logger.error("creditCard数组为空")
                            else:
                                # 兼容旧格式 - 直接是卡片对象
                                card_data = {
                                    "IssuingNetwork": response_data.get("IssuingNetwork", ""),
                                    "CardNumber": response_data.get("CardNumber", ""),
                                    "Bank": response_data.get("Bank", ""),
                                    "Name": response_data.get("Name", ""),
                                    "Address": response_data.get("Address", ""),
                                    "Country": response_data.get("Country", ""),
                                    "MoneyRange": response_data.get("MoneyRange", ""),
                                    "CVV": response_data.get("CVV", ""),
                                    "Expiry": response_data.get("Expiry", ""),
                                    "Pin": response_data.get("Pin", ""),
                                    "timestamp": datetime.now().isoformat(),
                                    "source": "vccgenerator.org"
                                }
                                logger.info("成功解析API响应数据 (直接格式)")
                                return card_data
                        elif isinstance(response_data, list) and len(response_data) > 0:
                            # 兼容数组格式
                            card_info = response_data[0]
                            card_data = {
                                "IssuingNetwork": card_info.get("IssuingNetwork", ""),
                                "CardNumber": card_info.get("CardNumber", ""),
                                "Bank": card_info.get("Bank", ""),
                                "Name": card_info.get("Name", ""),
                                "Address": card_info.get("Address", ""),
                                "Country": card_info.get("Country", ""),
                                "MoneyRange": card_info.get("MoneyRange", ""),
                                "CVV": card_info.get("CVV", ""),
                                "Expiry": card_info.get("Expiry", ""),
                                "Pin": card_info.get("Pin", ""),
                                "timestamp": datetime.now().isoformat(),
                                "source": "vccgenerator.org"
                            }
                            logger.info("成功解析API响应数据 (数组格式)")
                            return card_data

                    time.sleep(check_interval)

                except Exception as e:
                    logger.warning(f"检查响应时出错: {e}")
                    time.sleep(check_interval)

            logger.error("等待API响应超时")
            return None

        except Exception as e:
            logger.error(f"等待API响应时出错: {e}")
            return None



    def run_collection(self):
        """运行虚拟卡数据收集"""
        logger.info("开始虚拟卡数据收集流程")

        # 加载现有数据
        self.load_existing_data()

        # 设置浏览器
        if not self.setup_browser():
            logger.error("浏览器设置失败")
            return False

        try:
            # 打开VCC网站
            if not self.open_vcc_website():
                logger.error("无法访问VCC网站")
                return False

            # 提取session信息
            if not self.extract_session_info():
                logger.error("无法提取session信息")
                return False

            # 开始循环获取数据
            collected_count = 0
            max_attempts = MAX_CARDS - len(self.cards_data)

            logger.info(f"当前已有 {len(self.cards_data)} 条数据，还需收集 {max_attempts} 条")

            while len(self.cards_data) < MAX_CARDS and collected_count < max_attempts:
                logger.info(f"正在获取第 {collected_count + 1} 条数据...")

                # 如果不是第一次，需要重新设置浏览器
                if collected_count > 0:
                    logger.info("关闭当前浏览器，准备重新启动...")
                    self.cleanup()
                    time.sleep(2)

                    # 重新设置浏览器
                    if not self.setup_browser():
                        logger.error("重新设置浏览器失败")
                        break

                    # 重新打开网站
                    if not self.open_vcc_website():
                        logger.error("重新访问网站失败")
                        break

                # 获取虚拟卡数据（使用浏览器操作）
                card_data = self.fetch_vcc_data_via_browser()

                if card_data:
                    # 检查返回的是单张卡片还是多张卡片
                    if isinstance(card_data, list):
                        # 多张卡片
                        for card in card_data:
                            if len(self.cards_data) < MAX_CARDS:
                                self.cards_data.append(card)
                        collected_count += len(card_data)
                        logger.info(f"成功获取 {len(card_data)} 张卡片数据，当前总数: {len(self.cards_data)}")
                    else:
                        # 单张卡片
                        self.cards_data.append(card_data)
                        collected_count += 1
                        logger.info(f"成功获取1张卡片数据，当前总数: {len(self.cards_data)}")

                    # 每获取数据后保存一次
                    self.save_data()

                    # 如果已经达到目标数量，退出循环
                    if len(self.cards_data) >= MAX_CARDS:
                        logger.info(f"已达到目标数量 {MAX_CARDS}，停止收集")
                        break

                else:
                    logger.warning("获取数据失败，等待后重试...")

                # 随机等待时间，避免请求过于频繁
                wait_time = random.uniform(2, 5)
                time.sleep(wait_time)

            # 最终保存数据
            self.save_data()
            logger.info(f"数据收集完成，共收集 {len(self.cards_data)} 条数据")

            return True

        except Exception as e:
            logger.error(f"数据收集过程中出错: {e}")
            return False
        finally:
            self.cleanup()






def cleanup_chrome_processes():
    """清理Chrome进程"""
    global _chrome_process_ids

    if not _chrome_process_ids:
        logger.info("没有需要清理的Chrome进程...")
        return

    logger.info(f"清理 {len(_chrome_process_ids)} 个Chrome进程...")
    try:
        if os.name == 'nt':
            for pid in _chrome_process_ids:
                try:
                    os.system(f'taskkill /F /PID {pid} /T 2>nul')
                except:
                    pass
        else:
            for pid in _chrome_process_ids:
                try:
                    os.kill(pid, signal.SIGTERM)
                except:
                    pass
        _chrome_process_ids = []
    except Exception as e:
        logger.error(f"清理进程时出错: {e}")


def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info("接收到退出信号，正在清理资源...")
    cleanup_chrome_processes()
    sys.exit(0)


def main():
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 命令行参数
    parser = argparse.ArgumentParser(description="VCC虚拟卡数据收集工具")
    parser.add_argument("--headless", action="store_true", help="使用无头模式运行")
    args = parser.parse_args()

    # 创建VCC生成器实例
    vcc_generator = VCCGenerator(headless=args.headless)

    try:
        # 运行数据收集
        success = vcc_generator.run_collection()

        if success:
            logger.info("虚拟卡数据收集完成")
        else:
            logger.error("虚拟卡数据收集失败")

    except KeyboardInterrupt:
        logger.info("接收到用户中断，正在退出...")
        vcc_generator.cleanup()
        cleanup_chrome_processes()
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        vcc_generator.cleanup()
        cleanup_chrome_processes()


if __name__ == "__main__":
    main()