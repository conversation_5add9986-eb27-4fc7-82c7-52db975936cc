from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import signal
import random
import logging
import json
import sys
from urllib.parse import quote
import requests
from datetime import datetime
import threading
import psutil
from colorama import Fore, Style
import argparse
from DrissionPage.errors import ElementLostError
import uuid
import base64
import hashlib
import secrets
import email
import imaplib
import re
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

"""
Cursor 自动注册工具 - Outlook版本（从文件读取邮箱）

主要功能：
1. 从outlookemail.txt文件读取Outlook邮箱信息
2. 使用OAuth2 IMAP方式获取验证码
3. 每个邮箱只使用一次（不管注册成功还是失败）
4. 支持多线程并发注册

邮箱文件格式（outlookemail.txt）：
每行一个账号信息，格式为：邮箱----密码----客户端ID----令牌
示例：
<EMAIL>----password123----client-id----refresh-token

参考Java代码实现了以下功能：
- 使用refresh_token获取access_token
- 使用OAuth2连接Outlook IMAP服务器
- 搜索并解析Cursor验证码邮件
- 线程安全的邮箱分配机制
"""

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cursor_registration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
SIGNUP_URL = "https://authenticator.cursor.sh/password?email={}&state={}&redirect_uri=https://cursor.com/api/auth/callback"
API_BASE_URL = "http://119.29.20.123:8080/admin-api"  # 修改为你的实际API端点
# EMAIL_CODE_ENDPOINT = "/cqp/cursoremailpool/getEmailCode?token=ed97fe98dc91428096289b9ffa1f9d61"  # 不再使用，改为从文件读取
TOKEN_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/saveRecord?token=ed97fe98dc91428096289b9ffa1f9d61"

# 代理配置
USE_PROXY = True  # 是否使用代理
# 主要代理API
PROXY_API_URL = "https://overseas.proxy.qg.net/get?key=EB914784&num=1&keep_alive=5&area=990400"
# 备用代理API（查询正在使用的代理）
BACKUP_PROXY_API_URL = "https://overseas.proxy.qg.net/query?key=EB914784"
PROXY_LIFETIME_MINUTES = 5  # 每个代理的有效时间（分钟）
PROXY_RETRY_TIMES = 5  # 代理获取重试次数
PROXY_RETRY_INTERVAL = 3  # 代理获取重试间隔（秒）

# 控制注册线程数量的常量
DEFAULT_THREADS = 1  # 默认并发线程数
MAX_THREADS = 20  # 最大并发线程数
DEFAULT_REGISTRATION_COUNT = 1000  # 每个线程默认注册次数

# 全局变量，用于存储浏览器进程ID
_chrome_process_ids = []

# 全局变量，用于跟踪已使用的邮箱（所有线程共享）
_used_emails = set()
# 线程锁，用于保护全局变量
_email_lock = threading.Lock()

class DrissionCursorRegister:
    def __init__(self, group_id, headless=False):
        self.group_id = group_id
        self.headless = headless
        self.email = None
        self.password = None  # 邮箱密码
        self.client_id = None  # Outlook OAuth2 客户端ID
        self.refresh_token = None  # Outlook OAuth2 刷新令牌
        self.access_token = None  # Outlook OAuth2 访问令牌
        self.page = None
        self.token = None
        self.code = None
        self.profile_path = f"chrome-data-cursor/group_{group_id}"  # 简化命名为固定格式
        self.max_attempts = 10  # 最大尝试次数
        self.current_attempt = 0
        self.before_pids = []
        self.success_callback = None
        self.error_callback = None
        self.proxy_host = None
        self.proxy_port = None
        self.proxy_start_time = None  # 记录代理获取的时间
        # OAuth相关参数
        self.uuid = None
        self.challenge = None
        self.verifier = None
        
    def set_callbacks(self, success_callback, error_callback):
        """设置回调函数"""
        self.success_callback = success_callback
        self.error_callback = error_callback

    def generate_oauth_params(self):
        """生成OAuth相关参数：uuid、challenge、verifier"""
        try:
            # 生成UUID
            self.uuid = str(uuid.uuid4())
            logger.info(f"[{self.group_id}] 生成UUID: {self.uuid}")

            # 生成code_verifier (32字节随机数，Base64编码)
            random_bytes = secrets.token_bytes(32)
            self.verifier = base64.urlsafe_b64encode(random_bytes).decode('utf-8').rstrip('=')
            logger.info(f"[{self.group_id}] 生成verifier: {self.verifier}")

            # 生成code_challenge (SHA-256哈希 + Base64编码)
            sha256_hash = hashlib.sha256(self.verifier.encode('utf-8')).digest()
            self.challenge = base64.urlsafe_b64encode(sha256_hash).decode('utf-8').rstrip('=')
            logger.info(f"[{self.group_id}] 生成challenge: {self.challenge}")

            return True
        except Exception as e:
            logger.error(f"[{self.group_id}] 生成OAuth参数失败: {e}")
            return False

    def get_new_proxy(self, max_duration_minutes=5):
        """从API获取新的代理IP，支持重试机制和备用API"""
        logger.info(f"[{self.group_id}] 正在获取新代理...")

        # 首先尝试主要API（重试5次）
        if self._try_get_proxy_from_main_api():
            return True

        # 主要API失败后，尝试备用API（查询正在使用的代理）
        logger.warning(f"[{self.group_id}] 主要API获取代理失败，尝试备用API...")
        if self._try_get_proxy_from_backup_api():
            return True

        # 如果备用API也失败，使用原来的循环获取逻辑作为最后手段
        logger.warning(f"[{self.group_id}] 备用API也失败，使用循环获取作为最后手段...")
        return self._try_get_proxy_with_loop(max_duration_minutes)

    def _try_get_proxy_from_main_api(self):
        """尝试从主要API获取代理，重试5次"""
        for attempt in range(1, PROXY_RETRY_TIMES + 1):
            try:
                logger.info(f"[{self.group_id}] 主要API第 {attempt}/{PROXY_RETRY_TIMES} 次尝试...")
                response = requests.get(PROXY_API_URL, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                        proxy_info = data["data"][0]
                        server = proxy_info.get("server", "")
                        if ":" in server:
                            self.proxy_host, self.proxy_port = server.split(":")
                            self.proxy_start_time = time.time()
                            logger.info(f"[{self.group_id}] 主要API成功获取代理: {self.proxy_host}:{self.proxy_port}")
                            return True
                        else:
                            logger.error(f"[{self.group_id}] 代理服务器格式错误: {server}")
                    else:
                        logger.error(f"[{self.group_id}] 主要API返回失败: {data}")
                else:
                    logger.error(f"[{self.group_id}] 主要API请求失败: {response.status_code}")

            except Exception as e:
                logger.error(f"[{self.group_id}] 主要API请求异常: {e}")

            # 如果不是最后一次尝试，等待3秒
            if attempt < PROXY_RETRY_TIMES:
                logger.info(f"[{self.group_id}] {PROXY_RETRY_INTERVAL}秒后重试主要API...")
                time.sleep(PROXY_RETRY_INTERVAL)

        return False

    def _try_get_proxy_from_backup_api(self):
        """尝试从备用API获取正在使用的代理"""
        try:
            logger.info(f"[{self.group_id}] 尝试备用API获取正在使用的代理...")
            response = requests.get(BACKUP_PROXY_API_URL, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                    # 选择第一个可用的代理
                    proxy_info = data["data"][0]
                    server = proxy_info.get("server", "")
                    if ":" in server:
                        self.proxy_host, self.proxy_port = server.split(":")
                        self.proxy_start_time = time.time()
                        deadline = proxy_info.get("deadline", "未知")
                        logger.info(f"[{self.group_id}] 备用API成功获取代理: {self.proxy_host}:{self.proxy_port} (到期时间: {deadline})")
                        return True
                    else:
                        logger.error(f"[{self.group_id}] 备用API代理格式错误: {server}")
                else:
                    logger.error(f"[{self.group_id}] 备用API返回失败: {data}")
            else:
                logger.error(f"[{self.group_id}] 备用API请求失败: {response.status_code}")

        except Exception as e:
            logger.error(f"[{self.group_id}] 备用API请求异常: {e}")

        return False

    def _try_get_proxy_with_loop(self, max_duration_minutes=5):
        """循环获取代理作为最后手段"""
        logger.info(f"[{self.group_id}] 开始循环获取代理（最后手段）...")

        start_time = time.time()
        max_duration_seconds = max_duration_minutes * 60
        attempt = 0

        while True:
            attempt += 1
            elapsed_time = time.time() - start_time

            # 检查是否超过最大持续时间
            if elapsed_time >= max_duration_seconds:
                logger.error(f"[{self.group_id}] 循环获取代理超时，已尝试 {max_duration_minutes} 分钟，共 {attempt-1} 次尝试")
                return False

            try:
                logger.info(f"[{self.group_id}] 循环第 {attempt} 次尝试获取代理...")
                response = requests.get(PROXY_API_URL, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                        proxy_info = data["data"][0]
                        server = proxy_info.get("server", "")
                        if ":" in server:
                            self.proxy_host, self.proxy_port = server.split(":")
                            self.proxy_start_time = time.time()
                            logger.info(f"[{self.group_id}] 循环获取成功: {self.proxy_host}:{self.proxy_port} (尝试 {attempt} 次)")
                            return True
                        else:
                            logger.error(f"[{self.group_id}] 循环获取代理格式错误: {server}")
                    else:
                        logger.error(f"[{self.group_id}] 循环获取失败: {data}")
                else:
                    logger.error(f"[{self.group_id}] 循环获取请求失败: {response.status_code}")
            except Exception as e:
                logger.error(f"[{self.group_id}] 循环获取异常: {e}")

            # 如果还没超时，等待3秒后重试
            remaining_time = max_duration_seconds - elapsed_time
            if remaining_time > 3:
                logger.info(f"[{self.group_id}] 3秒后继续循环重试... (剩余时间: {remaining_time:.1f}秒)")
                time.sleep(3)
            else:
                logger.error(f"[{self.group_id}] 剩余时间不足3秒，停止循环重试")
                return False

    def test_proxy(self, max_test_attempts=10):
        """测试当前代理是否可用，如果不可用则重新获取，直到可用或达到最大尝试次数"""
        # 首先检查代理是否过期
        if self.is_proxy_expired():
            logger.info(f"[{self.group_id}] 当前代理已过期，尝试获取新代理")
            if not self.get_new_proxy():
                logger.error(f"[{self.group_id}] 获取新代理失败")
                return False
            logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")

        for attempt in range(1, max_test_attempts + 1):
            try:
                test_url = "https://www.google.com"
                test_proxies = {
                    "http": f"http://{self.proxy_host}:{self.proxy_port}",
                    "https": f"http://{self.proxy_host}:{self.proxy_port}"
                }
                response = requests.get(test_url, proxies=test_proxies, timeout=5)
                if response.status_code == 200:
                    logger.info(f"[{self.group_id}] 代理测试成功")
                    return True
                else:
                    logger.error(f"[{self.group_id}] 代理测试失败: {response.status_code}")
            except Exception as e:
                logger.error(f"[{self.group_id}] 代理测试异常: {e}")

            # 如果测试失败且未达到最大尝试次数，则重新获取代理
            if attempt < max_test_attempts:
                logger.info(f"[{self.group_id}] 尝试获取新代理 (测试尝试 {attempt}/{max_test_attempts})")
                if not self.get_new_proxy():
                    logger.error(f"[{self.group_id}] 获取新代理失败，停止测试")
                    return False
                logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")

        logger.error(f"[{self.group_id}] 多次尝试后未能获取可用代理")
        return False

    def is_proxy_expired(self):
        """检查当前代理是否已过期"""
        if not self.proxy_start_time:
            return True  # 如果没有开始时间，视为已过期

        current_time = time.time()
        elapsed_minutes = (current_time - self.proxy_start_time) / 60

        if elapsed_minutes >= PROXY_LIFETIME_MINUTES:
            logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，超过设定的 {PROXY_LIFETIME_MINUTES} 分钟有效期")
            return True

        logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，还在有效期内 ({PROXY_LIFETIME_MINUTES} 分钟)")
        return False

    def generate_random_browser_profile(self):
        """生成随机的浏览器特征以避免指纹识别"""
        try:
            # 随机User-Agent列表（Windows Chrome）
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36"
            ]

            # 随机屏幕分辨率
            resolutions = [
                "1920,1080", "1366,768", "1536,864", "1440,900", "1600,900",
                "1280,720", "1920,1200", "2560,1440", "1680,1050"
            ]

            # 随机时区
            timezones = [
                "America/New_York", "America/Los_Angeles", "America/Chicago",
                "Europe/London", "Europe/Berlin", "Asia/Tokyo", "Asia/Shanghai",
                "Australia/Sydney", "America/Toronto"
            ]

            # 随机语言
            languages = [
                "en-US,en;q=0.9", "en-GB,en;q=0.9", "zh-CN,zh;q=0.9,en;q=0.8",
                "ja-JP,ja;q=0.9,en;q=0.8", "de-DE,de;q=0.9,en;q=0.8"
            ]

            profile = {
                "user_agent": random.choice(user_agents),
                "resolution": random.choice(resolutions),
                "timezone": random.choice(timezones),
                "language": random.choice(languages),
                "platform": random.choice(["Win32", "Win64"]),
                "memory": random.choice([4, 8, 16, 32]),  # GB
                "cpu_cores": random.choice([4, 6, 8, 12, 16])
            }

            logger.info(f"[{self.group_id}] 生成随机浏览器特征: UA={profile['user_agent'][:50]}...")
            return profile

        except Exception as e:
            logger.error(f"[{self.group_id}] 生成随机浏览器特征失败: {e}")
            return None

    def apply_anti_fingerprint_settings(self, co):
        """应用防指纹设置（仅在代理模式下使用）"""
        try:
            logger.info(f"[{self.group_id}] 应用防指纹浏览器设置...")

            # 生成随机浏览器特征
            browser_profile = self.generate_random_browser_profile()
            if browser_profile:
                co.set_user_agent(browser_profile["user_agent"])
                co.set_argument(f"--window-size={browser_profile['resolution'].replace(',', 'x')}")

                # 随机窗口位置
                random_x = random.randint(0, 200)
                random_y = random.randint(0, 200)
                co.set_argument(f"--window-position={random_x},{random_y}")

                logger.info(f"[{self.group_id}] 已应用随机浏览器特征")

            # 防指纹Chrome参数
            co.set_argument("--disable-canvas-aa")  # 禁用Canvas抗锯齿
            co.set_argument("--disable-2d-canvas-clip-aa")  # 禁用2D Canvas裁剪抗锯齿
            co.set_argument("--disable-gl-drawing-for-tests")  # 禁用GL绘制测试
            co.set_argument("--disable-gpu-sandbox")  # 禁用GPU沙盒
            co.set_argument("--disable-font-subpixel-positioning")  # 禁用字体子像素定位
            co.set_argument("--disable-lcd-text")  # 禁用LCD文本渲染
            co.set_argument("--disable-sensors")  # 禁用传感器
            co.set_argument("--disable-device-discovery-notifications")  # 禁用设备发现通知
            co.set_argument("--disable-geolocation")  # 禁用地理位置
            co.set_argument("--disable-plugins-discovery")  # 禁用插件发现
            co.set_argument("--disable-speech-api")  # 禁用语音API

            # 设置代理相关偏好
            co.set_pref("webrtc.ip_handling_policy", "disable_non_proxied_udp")
            co.set_pref("webrtc.multiple_routes_enabled", False)
            co.set_pref("webrtc.nonproxied_udp_enabled", False)
            co.set_pref("media.peerconnection.enabled", False)
            co.set_pref("media.peerconnection.ice.default_address_only", True)
            co.set_pref("media.peerconnection.ice.no_host", True)

            logger.info(f"[{self.group_id}] 防指纹设置应用完成")

        except Exception as e:
            logger.warning(f"[{self.group_id}] 应用防指纹设置失败: {e}")

    def inject_anti_fingerprint_script(self):
        """注入反指纹脚本"""
        if not self.page:
            return

        try:
            # 反指纹JavaScript代码
            anti_fingerprint_script = """
            // 随机化Canvas指纹
            const originalGetContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(type, ...args) {
                const context = originalGetContext.apply(this, [type, ...args]);
                if (type === '2d') {
                    const originalFillText = context.fillText;
                    context.fillText = function(text, x, y, maxWidth) {
                        // 添加微小的随机偏移
                        const randomOffset = Math.random() * 0.1 - 0.05;
                        return originalFillText.apply(this, [text, x + randomOffset, y + randomOffset, maxWidth]);
                    };
                }
                return context;
            };

            // 随机化WebGL指纹
            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === this.RENDERER || parameter === this.VENDOR) {
                    return 'Generic Renderer';
                }
                return originalGetParameter.apply(this, arguments);
            };

            // 随机化屏幕信息
            Object.defineProperty(screen, 'width', {
                get: function() { return 1920 + Math.floor(Math.random() * 100); }
            });
            Object.defineProperty(screen, 'height', {
                get: function() { return 1080 + Math.floor(Math.random() * 100); }
            });

            // 随机化时区
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {
                return -480 + Math.floor(Math.random() * 60); // 随机时区偏移
            };

            // 隐藏自动化特征
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });

            // 随机化语言
            Object.defineProperty(navigator, 'language', {
                get: function() {
                    const languages = ['en-US', 'en-GB', 'zh-CN', 'ja-JP', 'de-DE'];
                    return languages[Math.floor(Math.random() * languages.length)];
                }
            });

            console.log('Anti-fingerprint script injected');
            """

            self.page.run_js(anti_fingerprint_script)
            logger.info(f"[{self.group_id}] 已注入反指纹脚本")

        except Exception as e:
            logger.warning(f"[{self.group_id}] 注入反指纹脚本失败: {e}")

    def apply_proxy_anti_fingerprint_measures(self):
        """在代理模式下应用额外的防指纹措施"""
        try:
            logger.info(f"[{self.group_id}] 应用代理模式防指纹措施...")

            # 注入反指纹脚本
            self.inject_anti_fingerprint_script()

            logger.info(f"[{self.group_id}] 代理模式防指纹措施应用完成")

        except Exception as e:
            logger.warning(f"[{self.group_id}] 应用代理防指纹措施失败: {e}")

    def get_access_token_and_refresh_token(self, token):
        """根据token获取accessToken和refreshToken"""
        try:
            logger.info(f"[{self.group_id}] 开始获取accessToken和refreshToken")

            # 处理token格式
            if not "user_01" in token:
                # 这里简化处理，实际可能需要JWT解析
                # 假设token格式需要调整
                processed_token = f"user_01::{token}"
            else:
                processed_token = token

            # URL编码处理
            encoded_token = quote(processed_token, safe='')

            # 调用API获取tokens
            api_url = f"https://api2.cursor.sh/auth/poll?uuid={self.uuid}&verifier={self.verifier}"
            logger.info(f"[{self.group_id}] 调用API: {api_url}")

            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"[{self.group_id}] API响应: {result}")

                # 提取accessToken和refreshToken
                access_token = result.get('accessToken')
                refresh_token = result.get('refreshToken')

                if access_token and refresh_token:
                    logger.info(f"[{self.group_id}] 成功获取tokens")
                    return access_token, refresh_token
                else:
                    logger.error(f"[{self.group_id}] API响应中未找到tokens")
                    return None, None
            else:
                logger.error(f"[{self.group_id}] API请求失败: {response.status_code} - {response.text}")
                return None, None

        except Exception as e:
            logger.error(f"[{self.group_id}] 获取tokens时出错: {e}")
            return None, None
        
    def generate_email(self):
        """从outlookemail.txt文件读取Outlook邮箱地址和OAuth2令牌"""
        global _used_emails, _email_lock

        try:
            logger.info(f"[{self.group_id}] 正在从outlookemail.txt文件读取邮箱信息")

            # 检查文件是否存在
            if not os.path.exists("outlookemail.txt"):
                logger.error(f"[{self.group_id}] outlookemail.txt文件不存在")
                return None

            # 使用线程锁保护文件读取和邮箱选择过程
            with _email_lock:
                # 读取文件内容
                with open("outlookemail.txt", "r", encoding="utf-8") as f:
                    lines = f.readlines()

                if not lines:
                    logger.error(f"[{self.group_id}] outlookemail.txt文件为空")
                    return None

                # 查找未使用的邮箱
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith("#"):  # 跳过空行和注释行
                        continue

                    # 解析邮箱信息：邮箱----密码----客户端ID----令牌
                    if "----" in line:
                        parts = line.split("----")
                        if len(parts) >= 4:
                            email = parts[0].strip()
                            password = parts[1].strip()
                            client_id = parts[2].strip()
                            refresh_token = parts[3].strip()

                            # 检查是否已使用过这个邮箱
                            if email not in _used_emails:
                                # 标记为已使用
                                _used_emails.add(email)

                                # 设置实例变量
                                self.password = password
                                self.client_id = client_id
                                self.refresh_token = refresh_token

                                logger.info(f"[{self.group_id}] 成功获取Outlook邮箱: {email}")
                                logger.info(f"[{self.group_id}] Client ID: {client_id}")
                                logger.info(f"[{self.group_id}] Refresh Token长度: {len(refresh_token)}")
                                logger.info(f"[{self.group_id}] Refresh Token前50字符: {refresh_token[:50]}...")

                                # 验证邮箱配置
                                if self.validate_email_config(email, client_id, refresh_token):
                                    logger.info(f"[{self.group_id}] 邮箱配置验证通过")
                                else:
                                    logger.warning(f"[{self.group_id}] 邮箱配置验证失败，但继续使用")

                                return email
                        else:
                            logger.warning(f"[{self.group_id}] 邮箱数据格式错误，部分数量不足: {len(parts)} - {line}")
                    else:
                        logger.warning(f"[{self.group_id}] 邮箱数据格式错误，无分隔符: {line}")

                logger.error(f"[{self.group_id}] 没有找到可用的邮箱（可能都已使用过）")
                return None

        except Exception as e:
            logger.error(f"[{self.group_id}] 读取outlookemail.txt文件异常: {e}")
            return None

    def validate_email_config(self, email, client_id, refresh_token):
        """验证邮箱配置是否正确"""
        try:
            logger.info(f"[{self.group_id}] 开始验证邮箱配置: {email}")

            # 检查基本格式
            if not email or '@' not in email:
                logger.error(f"[{self.group_id}] 邮箱格式无效: {email}")
                return False

            if not client_id or len(client_id) < 10:
                logger.error(f"[{self.group_id}] Client ID格式无效: {client_id}")
                return False

            if not refresh_token or len(refresh_token) < 50:
                logger.error(f"[{self.group_id}] Refresh Token格式无效，长度: {len(refresh_token)}")
                return False

            # 检查邮箱域名
            domain = email.split('@')[1].lower()
            valid_domains = ['outlook.com', 'hotmail.com', 'live.com', 'msn.com']
            if domain not in valid_domains:
                logger.warning(f"[{self.group_id}] 邮箱域名可能不支持: {domain}")
                logger.warning(f"[{self.group_id}] 支持的域名: {valid_domains}")

            # 尝试获取访问令牌来验证配置
            test_token = self.get_oauth_token_by_outlook(client_id, refresh_token)
            if test_token:
                logger.info(f"[{self.group_id}] 邮箱配置验证成功，可以获取访问令牌")
                # 缓存访问令牌
                self.access_token = test_token
                return True
            else:
                logger.error(f"[{self.group_id}] 邮箱配置验证失败，无法获取访问令牌")
                return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 验证邮箱配置时出错: {e}")
            return False

    def get_oauth_token_by_outlook(self, client_id, refresh_token):
        """根据client_id和refresh_token获取OAuth2访问令牌 - 改进版本"""
        try:
            logger.info(f"[{self.group_id}] 开始获取OAuth2访问令牌")
            logger.info(f"[{self.group_id}] Client ID: {client_id}")
            logger.info(f"[{self.group_id}] Refresh Token长度: {len(refresh_token)}")

            # 构建请求参数 - 使用form格式，与Java版本一致
            form_data = {
                "client_id": client_id,
                "grant_type": "refresh_token",
                "refresh_token": refresh_token
            }

            # 设置请求头
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            logger.info(f"[{self.group_id}] 发送OAuth2请求到: https://login.live.com/oauth20_token.srf")

            # 发送POST请求获取访问令牌
            response = requests.post(
                "https://login.live.com/oauth20_token.srf",
                data=form_data,  # 使用form数据格式，自动编码为x-www-form-urlencoded
                headers=headers,
                timeout=30
            )

            logger.info(f"[{self.group_id}] OAuth2响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"[{self.group_id}] OAuth2响应解析成功")

                    access_token = result.get("access_token")
                    if access_token:
                        logger.info(f"[{self.group_id}] 成功获取OAuth2访问令牌，长度: {len(access_token)}")
                        logger.info(f"[{self.group_id}] Access Token前50字符: {access_token[:50]}...")

                        # 检查令牌类型和过期时间
                        token_type = result.get("token_type", "Bearer")
                        expires_in = result.get("expires_in", "未知")
                        scope = result.get("scope", "未知")
                        logger.info(f"[{self.group_id}] Token类型: {token_type}, 过期时间: {expires_in}秒, 作用域: {scope}")

                        return access_token
                    else:
                        logger.error(f"[{self.group_id}] 响应中未找到access_token")
                        logger.error(f"[{self.group_id}] 完整响应: {result}")
                        return None

                except ValueError as json_e:
                    logger.error(f"[{self.group_id}] OAuth2响应不是有效的JSON: {json_e}")
                    logger.error(f"[{self.group_id}] 响应内容: {response.text}")
                    return None
            else:
                logger.error(f"[{self.group_id}] OAuth2请求失败: {response.status_code}")
                logger.error(f"[{self.group_id}] 错误响应: {response.text}")

                # 尝试解析错误信息
                try:
                    error_data = response.json()
                    error_code = error_data.get("error", "未知错误")
                    error_description = error_data.get("error_description", "无描述")
                    logger.error(f"[{self.group_id}] 错误代码: {error_code}, 描述: {error_description}")
                except:
                    pass

                return None

        except requests.exceptions.Timeout:
            logger.error(f"[{self.group_id}] OAuth2请求超时")
            return None
        except requests.exceptions.ConnectionError:
            logger.error(f"[{self.group_id}] OAuth2请求连接错误")
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取OAuth2访问令牌异常: {e}")
            import traceback
            logger.error(f"[{self.group_id}] 异常详情: {traceback.format_exc()}")
            return None

    def generate_oauth2_string(self, email, access_token):
        """生成OAuth2认证字符串"""
        try:
            # 构建OAuth2字符串
            auth_string = f"user={email}\x01auth=Bearer {access_token}\x01\x01"
            logger.info(f"[{self.group_id}] 原始OAuth2字符串长度: {len(auth_string)}")

            # Base64编码
            encoded_string = base64.b64encode(auth_string.encode('utf-8')).decode('utf-8')
            logger.info(f"[{self.group_id}] 编码后OAuth2字符串长度: {len(encoded_string)}")
            logger.info(f"[{self.group_id}] OAuth2字符串前100字符: {encoded_string[:100]}...")

            return encoded_string
        except Exception as e:
            logger.error(f"[{self.group_id}] 生成OAuth2字符串失败: {e}")
            import traceback
            logger.error(f"[{self.group_id}] 异常详情: {traceback.format_exc()}")
            return None

    def extract_code_from_email(self, email_content):
        """从邮件内容中提取验证码"""
        try:
            # 查找6位数字验证码的正则表达式
            code_patterns = [
                # 匹配空格分隔的验证码，如 "3 3 4 9 3 9"
                r'Your one-time code is:\s*\n\s*(\d\s+\d\s+\d\s+\d\s+\d\s+\d)',
                r'code is:\s*\n\s*(\d\s+\d\s+\d\s+\d\s+\d\s+\d)',
                # 匹配连续的6位数字
                r'Your one-time code is[:\s]*(\d{6})',
                r'verification code is[:\s]*(\d{6})',
                r'code[:\s]*(\d{6})',
                # 匹配任何6位数字
                r'(\d{6})',
                # 匹配空格分隔的数字（更通用的模式）
                r'(\d\s+\d\s+\d\s+\d\s+\d\s+\d)',
            ]

            for pattern in code_patterns:
                match = re.search(pattern, email_content, re.IGNORECASE | re.MULTILINE)
                if match:
                    code_raw = match.group(1)
                    # 移除所有空格和非数字字符，只保留数字
                    code = re.sub(r'[^\d]', '', code_raw)
                    if len(code) == 6 and code.isdigit():
                        logger.info(f"[{self.group_id}] 成功提取验证码: {code} (原始格式: {code_raw})")
                        return code

            logger.warning(f"[{self.group_id}] 未能从邮件内容中提取验证码")
            return None

        except Exception as e:
            logger.error(f"[{self.group_id}] 提取验证码时出错: {e}")
            return None

    def get_new_proxy(self):
        """从API获取新的代理IP"""
        logger.info(f"[{self.group_id}] 正在获取新代理...")
        try:
            response = requests.get(PROXY_API_URL, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                    proxy_info = data["data"][0]
                    server = proxy_info.get("server", "")
                    if ":" in server:
                        self.proxy_host, self.proxy_port = server.split(":")
                        # 设置代理获取时间
                        self.proxy_start_time = time.time()
                        logger.info(f"[{self.group_id}] 成功获取新代理: {self.proxy_host}:{self.proxy_port}")
                        return True
                    else:
                        logger.error(f"[{self.group_id}] 代理服务器格式错误: {server}")
                else:
                    logger.error(f"[{self.group_id}] 获取代理失败: {data}")
            else:
                logger.error(f"[{self.group_id}] 获取代理请求失败: {response.status_code}")
            return False
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取代理时发生异常: {e}")
            return False

    def test_proxy(self, max_attempts=60):
        """测试当前代理是否可用，如果不可用则重新获取，直到可用或达到最大尝试次数"""
        # 首先检查代理是否过期
        if self.is_proxy_expired():
            logger.info(f"[{self.group_id}] 当前代理已过期，尝试获取新代理")
            if self.get_new_proxy():
                logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.error(f"[{self.group_id}] 获取新代理失败")
                return False

        for attempt in range(1, max_attempts + 1):
            try:
                test_url = "https://www.google.com"
                test_proxies = {
                    "http": f"http://{self.proxy_host}:{self.proxy_port}",
                    "https": f"http://{self.proxy_host}:{self.proxy_port}"
                }
                response = requests.get(test_url, proxies=test_proxies, timeout=5)
                if response.status_code == 200:
                    logger.info(f"[{self.group_id}] 代理测试成功")
                    return True
                else:
                    logger.error(f"[{self.group_id}] 代理测试失败: {response.status_code}")
            except Exception as e:
                logger.error(f"[{self.group_id}] 代理测试异常: {e}")

            # 如果测试失败且未达到最大尝试次数，则重新获取代理
            if attempt < max_attempts:
                logger.info(f"[{self.group_id}] 尝试获取新代理 (尝试 {attempt}/{max_attempts})")
                if self.get_new_proxy():
                    logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
                    continue
                else:
                    logger.error(f"[{self.group_id}] 获取新代理失败")

        logger.error(f"[{self.group_id}] 多次尝试后未能获取可用代理")
        return False
    def setup_browser(self, use_proxy=False):
        """设置浏览器，优化启动速度"""
        logger.info(f"[{self.group_id}] 开始设置浏览器...")

        # 先尝试删除已存在的目录
        try:
            if os.path.exists(self.profile_path):
                import shutil
                shutil.rmtree(self.profile_path)
                logger.info(f"[{self.group_id}] 已删除旧的浏览器配置文件夹: {self.profile_path}")
        except Exception as e:
            logger.warning(f"[{self.group_id}] 删除旧的浏览器配置文件夹时出错: {e}")

        # 创建新的目录
        os.makedirs(self.profile_path, exist_ok=True)

        try:
            # 记录启动前的浏览器进程
            self.before_pids = self.get_browser_processes()

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 设置用户数据目录
            co.set_user_data_path(self.profile_path)

            # 如果是代理模式，应用防指纹设置
            if use_proxy and USE_PROXY:
                self.apply_anti_fingerprint_settings(co)

            # 优化浏览器启动参数 - 提高性能，减少资源使用
            co.set_argument("--incognito")  # 隐身模式
            co.set_argument("--disable-extensions")  # 禁用扩展
            co.set_argument("--disable-gpu")  # 禁用GPU加速
            co.set_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
            co.set_argument("--disable-infobars")  # 禁用信息栏
            co.set_argument("--disable-browser-side-navigation")  # 禁用浏览器侧导航
            co.set_argument("--disable-features=TranslateUI,BlinkGenPropertyTrees")  # 禁用翻译UI等
            co.set_argument("--disable-notifications")  # 禁用通知
            co.set_argument("--disable-popup-blocking")  # 禁用弹窗阻止
            co.set_argument("--disable-background-timer-throttling")  # 禁用后台计时器限制
            co.set_argument("--disable-backgrounding-occluded-windows")  # 禁用背景窗口
            co.set_argument("--disable-breakpad")  # 禁用崩溃报告
            co.set_argument("--disable-client-side-phishing-detection")  # 禁用钓鱼检测
            co.set_argument("--disable-default-apps")  # 禁用默认应用
            co.set_argument("--disable-hang-monitor")  # 禁用挂起监视器
            co.set_argument("--disable-prompt-on-repost")  # 禁用重新发布提示
            co.set_argument("--disable-sync")  # 禁用同步
            co.set_argument("--no-first-run")  # 禁止首次运行
            co.set_argument("--no-default-browser-check")  # 禁止默认浏览器检查
            co.set_argument("--disable-webrtc")  # 禁用WebRTC
            co.set_argument("--enforce-webrtc-ip-permission-check")  # 强制WebRTC IP权限检查

            # 设置代理（如果启用）
            if use_proxy and USE_PROXY and self.proxy_host and self.proxy_port:
                co.set_proxy(f"{self.proxy_host}:{self.proxy_port}")
                logger.info(f"[{self.group_id}] 已设置浏览器代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.info(f"[{self.group_id}] 当前不使用代理")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动浏览器
            start_time = time.time()
            logger.info(f"[{self.group_id}] 启动浏览器")
            self.page = ChromiumPage(co)

            # 等待浏览器准备就绪
            time.sleep(1)

            # 显示启动用时
            elapsed = time.time() - start_time
            logger.info(f"[{self.group_id}] 浏览器启动完成，用时 {elapsed:.2f} 秒")

            # 记录新增的浏览器进程
            self.track_browser_processes()

            # 如果是代理模式，应用额外的防指纹措施
            if use_proxy and USE_PROXY:
                try:
                    self.apply_proxy_anti_fingerprint_measures()
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 应用防指纹措施出错: {e}")

            return True

        except Exception as e:
            logger.exception(f"[{self.group_id}] 设置浏览器时出现异常: {e}")
            return False

    def get_default_browser_path(self):
        """获取默认浏览器路径"""
        # 简化示例，实际使用时可以依据不同操作系统获取更精确的路径
        if sys.platform == "win32":
            return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        elif sys.platform == "darwin":
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            return "/usr/bin/google-chrome"

    def get_browser_processes(self):
        """获取当前浏览器进程ID列表"""
        try:
            browser_process_names = ['chrome', 'chromium', 'msedge', 'edge', 'brave']
            return [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in browser_process_names)]
        except:
            return []

    def track_browser_processes(self):
        """跟踪新启动的浏览器进程"""
        global _chrome_process_ids
        try:
            after_pids = self.get_browser_processes()
            new_pids = [pid for pid in after_pids if pid not in self.before_pids]
            _chrome_process_ids.extend(new_pids)

            if new_pids:
                logger.info(f"[{self.group_id}] 跟踪到 {len(new_pids)} 个新浏览器进程")
            else:
                logger.warning(f"[{self.group_id}] 未检测到新的浏览器进程")
        except Exception as e:
            logger.error(f"[{self.group_id}] 跟踪进程失败: {e}")

    def cleanup(self):
        """清理资源，更彻底地清理浏览器"""
        try:
            # 关闭浏览器
            if self.page:
                # 先清除浏览器缓存
                try:
                    self.page.run_cdp('Network.clearBrowserCache')
                    self.page.run_cdp('Network.clearBrowserCookies')
                    self.page.run_cdp('Storage.clearDataForOrigin', {'origin': '*', 'storageTypes': 'all'})
                    logger.info(f"[{self.group_id}] 已清除浏览器缓存和Cookie")
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 清除缓存失败: {e}")

                # 关闭浏览器
                self.page.quit()
                time.sleep(1)

            # 强制结束可能残留的进程
            try:
                for pid in self.get_browser_processes():
                    if pid in self.before_pids:
                        continue  # 跳过启动前已存在的进程
                    if sys.platform == "win32":
                        os.system(f'taskkill /F /PID {pid} /T >nul 2>&1')
                    else:
                        try:
                            os.kill(pid, signal.SIGKILL)
                        except:
                            pass
                logger.info(f"[{self.group_id}] 已尝试强制结束残留进程")
            except Exception as e:
                logger.warning(f"[{self.group_id}] 结束残留进程出错: {e}")

            # 删除配置文件目录
            try:
                import shutil
                if os.path.exists(self.profile_path):
                    shutil.rmtree(self.profile_path)
                    logger.info(f"[{self.group_id}] 已删除浏览器配置目录")
                os.makedirs(self.profile_path, exist_ok=True)
            except Exception as e:
                logger.error(f"[{self.group_id}] 删除配置目录时出错: {e}")

            logger.info(f"[{self.group_id}] 资源清理完成")
        except Exception as e:
            logger.error(f"[{self.group_id}] 清理资源时出错: {e}")

    def handle_turnstile(self):
        """处理 Turnstile 验证"""
        logger.info(f"[{self.group_id}] 开始处理 Turnstile 人机验证...")

        # 等待turnstile元素出现
        turnstile = self.page.ele("@id=cf-turnstile", timeout=5)
        if not turnstile:
            logger.error(f"[{self.group_id}] 未找到 cf-turnstile 元素")
            return False

        try:
            # 将turnstile元素滚动到视图中并点击以获取焦点
            self.page.run_js("arguments[0].scrollIntoView(true);", turnstile)
            turnstile.click()
            logger.info(f"[{self.group_id}] 已点击Turnstile容器获取焦点")
            time.sleep(1)

            # 发送Tab键聚焦到复选框，然后发送空格键进行点击
            self.page.actions.key_down('TAB')
            self.page.actions.key_up('TAB')
            time.sleep(1)
            self.page.actions.key_down('space')
            self.page.actions.key_up('space')
            logger.info(f"[{self.group_id}] 已发送Tab和Space键")
            time.sleep(2)

            # 检查验证结果
            if self.check_verification_success():
                logger.info(f"[{self.group_id}] Turnstile 验证通过")
                return True

            logger.warning(f"[{self.group_id}] Turnstile 验证未通过")
            return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 处理验证时出错: {e}")
            return False

    def check_verification_success(self):
        """检查验证是否成功"""
        try:
            # 检查是否存在后续表单元素
            if (self.page.ele("@name=password", timeout=0.5) or
                self.page.ele("@name=email", timeout=0.5) or
                self.page.ele("@data-index=0", timeout=0.5) or
                self.page.ele("Account Settings", timeout=0.5)):
                return True

            # 检查是否有错误信息
            error_xpaths = [
                'xpath://div[contains(text(), "Can\'t verify the user is human")]',
                'xpath://div[contains(text(), "Error: 600010")]',
                'xpath://div[contains(text(), "Please try again")]'
            ]

            for xpath in error_xpaths:
                if self.page.ele(xpath):
                    return False

            return False
        except:
            return False

    def input_verification_code(self, code):
        """输入验证码"""
        if not code or len(code) != 6:
            logger.error(f"[{self.group_id}] 无效的验证码: {code}")
            return False

        try:
            # 查找验证码输入框
            for i, digit in enumerate(code):
                input_ele = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='" + str(i) + "']")
                if input_ele:
                    input_ele.input(digit)
                    time.sleep(0.2)
                else:
                    logger.error(f"[{self.group_id}] 未找到第{i}个验证码输入框")
                    return False

            logger.info(f"[{self.group_id}] 验证码输入成功")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 输入验证码出错: {e}")
            return False

    def get_email_code(self, email):
        """使用OAuth2读取Outlook邮件并提取验证码 - 改进版本"""
        try:
            logger.info(f"[{self.group_id}] 开始使用OAuth2读取Outlook邮件获取验证码")

            # 首先获取访问令牌
            if not self.access_token:
                self.access_token = self.get_oauth_token_by_outlook(self.client_id, self.refresh_token)
                if not self.access_token:
                    logger.error(f"[{self.group_id}] 获取OAuth2访问令牌失败")
                    return None

            # 尝试使用标准的imaplib.IMAP4_SSL连接
            logger.info(f"[{self.group_id}] 连接到Outlook IMAP服务器")

            # 方法1：尝试使用标准OAuth2认证
            try:
                return self._try_standard_oauth2_auth(email)
            except Exception as e1:
                logger.warning(f"[{self.group_id}] 标准OAuth2认证失败: {e1}")

            # 方法2：尝试使用手动SASL认证
            try:
                return self._try_manual_sasl_auth(email)
            except Exception as e2:
                logger.warning(f"[{self.group_id}] 手动SASL认证失败: {e2}")

            # 方法3：尝试使用简化的认证方式
            try:
                return self._try_simple_auth(email)
            except Exception as e3:
                logger.error(f"[{self.group_id}] 所有认证方式都失败了")
                logger.error(f"[{self.group_id}] 标准OAuth2: {e1}")
                logger.error(f"[{self.group_id}] 手动SASL: {e2}")
                logger.error(f"[{self.group_id}] 简化认证: {e3}")

            return None

        except Exception as e:
            logger.error(f"[{self.group_id}] 获取Outlook验证码时出错: {e}")
            import traceback
            logger.error(f"[{self.group_id}] 异常详情: {traceback.format_exc()}")
            return None

    def _try_standard_oauth2_auth(self, email):
        """尝试使用标准OAuth2认证方式"""
        logger.info(f"[{self.group_id}] 尝试标准OAuth2认证...")

        imap = imaplib.IMAP4_SSL("outlook.office365.com", 993)
        try:
            # 使用标准的OAuth2认证
            auth_string = f"user={email}\001auth=Bearer {self.access_token}\001\001"
            imap.authenticate('XOAUTH2', lambda x: auth_string.encode('utf-8'))
            logger.info(f"[{self.group_id}] 标准OAuth2认证成功")

            return self._read_emails_from_imap(imap)

        finally:
            try:
                imap.logout()
            except:
                pass

    def _try_manual_sasl_auth(self, email):
        """尝试使用手动SASL认证方式"""
        logger.info(f"[{self.group_id}] 尝试手动SASL认证...")

        imap = imaplib.IMAP4_SSL("outlook.office365.com", 993)
        try:
            # 构建认证字符串
            auth_string = f"user={email}\001auth=Bearer {self.access_token}\001\001"
            auth_b64 = base64.b64encode(auth_string.encode('utf-8')).decode('ascii')

            # 手动SASL XOAUTH2协议
            tag = 'A001'
            imap.send(f'{tag} AUTHENTICATE XOAUTH2\r\n'.encode('ascii'))
            response1 = imap.readline()
            logger.info(f"[{self.group_id}] 服务器响应1: {response1}")

            if b'+' in response1:
                imap.send(auth_b64.encode('ascii') + b'\r\n')
                response2 = imap.readline()
                logger.info(f"[{self.group_id}] 服务器响应2: {response2}")

                if b'OK' in response2 and tag.encode() in response2:
                    logger.info(f"[{self.group_id}] 手动SASL认证成功")
                    imap.state = 'AUTH'
                    time.sleep(0.5)  # 等待连接稳定

                    return self._read_emails_from_imap(imap)
                else:
                    raise Exception(f"SASL认证失败: {response2}")
            else:
                raise Exception(f"SASL初始化失败: {response1}")

        finally:
            try:
                imap.logout()
            except:
                pass

    def _try_simple_auth(self, email):
        """尝试使用简化的认证方式"""
        logger.info(f"[{self.group_id}] 尝试简化认证...")

        imap = imaplib.IMAP4_SSL("outlook.office365.com", 993)
        try:
            # 尝试直接使用access_token作为密码
            imap.login(email, self.access_token)
            logger.info(f"[{self.group_id}] 简化认证成功")

            return self._read_emails_from_imap(imap)

        finally:
            try:
                imap.logout()
            except:
                pass

    def _read_emails_from_imap(self, imap):
        """从IMAP连接中读取邮件"""
        logger.info(f"[{self.group_id}] 开始读取邮件...")

        # 选择收件箱
        select_result = imap.select('INBOX')
        if select_result[0] != 'OK':
            raise Exception(f"选择收件箱失败: {select_result}")

        logger.info(f"[{self.group_id}] 已选择收件箱")

        # 搜索Cursor相关的邮件
        search_criteria = '(OR (SUBJECT "Sign in to Cursor") (SUBJECT "Sign up for Cursor"))'
        logger.info(f"[{self.group_id}] 搜索邮件，条件: {search_criteria}")

        try:
            typ, message_numbers = imap.search(None, search_criteria)
            logger.info(f"[{self.group_id}] 搜索结果: {typ}, {message_numbers}")
        except Exception as search_e:
            logger.error(f"[{self.group_id}] 搜索邮件失败: {search_e}")
            # 尝试搜索所有邮件
            try:
                typ, message_numbers = imap.search(None, 'ALL')
                logger.info(f"[{self.group_id}] 改为搜索所有邮件: {typ}, {message_numbers}")
            except Exception as search_all_e:
                logger.error(f"[{self.group_id}] 搜索所有邮件也失败: {search_all_e}")
                raise Exception("无法搜索邮件")

        if typ != 'OK' or not message_numbers[0]:
            logger.warning(f"[{self.group_id}] 未找到符合条件的邮件")
            # 尝试搜索所有邮件看看邮箱是否为空
            try:
                typ_all, message_numbers_all = imap.search(None, 'ALL')
                if typ_all == 'OK' and message_numbers_all[0]:
                    all_count = len(message_numbers_all[0].split())
                    logger.info(f"[{self.group_id}] 邮箱中共有 {all_count} 封邮件，但没有Cursor相关邮件")
                else:
                    logger.warning(f"[{self.group_id}] 邮箱中没有任何邮件")
            except Exception as search_e:
                logger.error(f"[{self.group_id}] 搜索所有邮件失败: {search_e}")
            return None

        # 获取邮件列表
        message_ids = message_numbers[0].split()
        if not message_ids:
            logger.warning(f"[{self.group_id}] 没有找到符合条件的邮件")
            return None

        logger.info(f"[{self.group_id}] 找到 {len(message_ids)} 封Cursor相关邮件")

        # 处理最新的邮件（IMAP中邮件ID通常是按时间顺序的，最大的ID是最新的）
        latest_msg_id = message_ids[-1]  # 最后一个是最新的
        logger.info(f"[{self.group_id}] 获取最新邮件ID: {latest_msg_id}")

        try:
            typ, msg_data = imap.fetch(latest_msg_id, '(RFC822)')
            if typ != 'OK':
                logger.error(f"[{self.group_id}] 获取邮件内容失败: {typ}")
                return None
        except Exception as fetch_e:
            logger.error(f"[{self.group_id}] 获取邮件内容异常: {fetch_e}")
            return None

        # 解析邮件
        try:
            import email as email_module
            email_message = email_module.message_from_bytes(msg_data[0][1])
            subject = email_message.get('Subject', '')
            from_addr = email_message.get('From', '')
            date = email_message.get('Date', '')
            logger.info(f"[{self.group_id}] 处理邮件 - 主题: {subject}, 发件人: {from_addr}, 日期: {date}")

            # 提取邮件内容
            email_content = ""
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain":
                        try:
                            content = part.get_payload(decode=True)
                            if content:
                                email_content = content.decode('utf-8', errors='ignore')
                                break
                        except Exception as decode_e:
                            logger.warning(f"[{self.group_id}] 解码邮件内容失败: {decode_e}")
                            continue
            else:
                try:
                    content = email_message.get_payload(decode=True)
                    if content:
                        email_content = content.decode('utf-8', errors='ignore')
                except Exception as decode_e:
                    logger.warning(f"[{self.group_id}] 解码邮件内容失败: {decode_e}")

            if not email_content:
                logger.error(f"[{self.group_id}] 无法获取邮件内容")
                return None

            # 从邮件内容中提取验证码
            logger.info(f"[{self.group_id}] 邮件内容长度: {len(email_content)}")
            logger.info(f"[{self.group_id}] 邮件内容前500字符: {email_content[:500]}")

            verification_code = self.extract_code_from_email(email_content)
            if verification_code:
                logger.info(f"[{self.group_id}] 成功获取验证码: {verification_code}")
                return verification_code
            else:
                logger.error(f"[{self.group_id}] 未能从邮件中提取验证码")
                logger.error(f"[{self.group_id}] 完整邮件内容: {email_content}")
                return None

        except Exception as parse_e:
            logger.error(f"[{self.group_id}] 解析邮件异常: {parse_e}")
            import traceback
            logger.error(f"[{self.group_id}] 解析异常详情: {traceback.format_exc()}")
            return None



    def get_token(self):
        """从cookies中提取WorkosCursorSessionToken"""
        try:
            cookies = self.page.cookies()
            for cookie in cookies:
                if cookie.get('name') == 'WorkosCursorSessionToken':
                    token = cookie.get('value')
                    # if token and '::' in token:
                    #     return token.split('::')[1]
                    return token
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取token出错: {e}")
            return None

    def submit_token(self, email, token, access_token=None, refresh_token=None):
        """将token提交到API（异步方式）"""
        # 创建异步线程执行提交操作
        submit_thread = threading.Thread(
            target=self._async_submit_token,
            args=(email, token, access_token, refresh_token),
            daemon=True
        )
        submit_thread.start()
        # 立即返回，不等待提交完成
        return True

    def _async_submit_token(self, email, token, access_token=None, refresh_token=None):
        """异步执行token提交和日志记录"""
        try:
            # 准备请求参数
            submit_data = {
                "email": email,
                "token": token
            }

            # 添加accessToken和refreshToken（如果有）
            if access_token:
                submit_data["accessToken"] = access_token
            if refresh_token:
                submit_data["refreshToken"] = refresh_token

            request_kwargs = {
                "json": submit_data,
                "timeout": 20
            }

            # 添加代理配置（如果启用）
            # if USE_PROXY:
            #     request_kwargs["proxies"] = {
            #         "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
            #         "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
            #     }

            response = requests.post(f"{API_BASE_URL}{TOKEN_SUBMIT_ENDPOINT}", **request_kwargs)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info(f"[{self.group_id}] 成功提交{email}的token和tokens (异步)")
                    if self.success_callback:
                        self.success_callback(self.group_id, email, token)
                    return

            logger.error(f"[{self.group_id}] 提交token失败: {response.status_code} - {response.text}")
            if self.error_callback:
                self.error_callback(self.group_id, email, "提交token失败")
        except Exception as e:
            logger.error(f"[{self.group_id}] 提交token时出错: {e}")
            if self.error_callback:
                self.error_callback(self.group_id, email, f"提交token异常: {str(e)}")

    def process_registration(self):
        """处理注册流程"""
        if self.current_attempt >= self.max_attempts:
            logger.warning(f"[{self.group_id}] 达到最大尝试次数 {self.max_attempts}，终止当前注册")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, f"达到最大尝试次数 {self.max_attempts}")
            # 重置为无代理模式以便下次注册
            try:
                if self.page:
                    self.page.quit()
                    time.sleep(2)
                self.setup_browser(use_proxy=False)
                logger.info(f"[{self.group_id}] 尝试失败后已重置为无代理模式")
            except Exception as e:
                logger.error(f"[{self.group_id}] 重置为无代理模式失败: {e}")
            return False

        self.current_attempt += 1
        logger.info(f"[{self.group_id}] 执行注册尝试 {self.current_attempt}/{self.max_attempts}")

        # 等待页面加载
        time.sleep(3)
        has_overlay = bool(self.page.ele('xpath://div[@inert and @aria-hidden="true" and not(@class)]',timeout=1))
        # 1. 查找并点击magic-code按钮

        if not has_overlay:
            magic_code_button = self.page.ele("xpath://button[@name='intent' and @type='submit' and @value='magic-code' and not(@data-disabled='true')]",timeout=1)
            if bool(magic_code_button):
                logger.info(f"[{self.group_id}] 找到magic-code按钮，点击")
                try:
                    magic_code_button.click()
                except ElementLostError:
                    logger.info(f"[{self.group_id}] 点击按钮后元素失效，属正常跳转，已忽略")
                time.sleep(3)

        # 2. 检查是否存在人机验证
        turnstile = self.page.ele("@id=cf-turnstile", timeout=1)
        if has_overlay :
            if bool(turnstile):
                logger.info(f"[{self.group_id}] 检测到人机验证")
                self.handle_turnstile()
                time.sleep(3)

        # 3. 检查是否存在验证码输入框
        code_input = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='0']", timeout=1)
        if bool(code_input):
            logger.info(f"[{self.group_id}] 检测到验证码输入框，尝试获取验证码")
            self.process_verification_code()
            return True

        # 等待一段时间后继续尝试
        time.sleep(2)
        return self.process_registration()

    def process_verification_code(self, attempt=0):
        """处理验证码获取和输入"""
        if attempt >= 30:
            logger.error(f"[{self.group_id}] 验证码获取失败，达到最大尝试次数")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "验证码获取失败")
            return

        code = self.get_email_code(self.email)

        if not code:
            logger.info(f"[{self.group_id}] 尚未获取到验证码，3秒后重试 ({attempt + 1}/30)")
            time.sleep(3)
            self.process_verification_code(attempt + 1)
            return

        logger.info(f"[{self.group_id}] 获取到验证码，准备获取代理并切换到代理模式")

        # 保存当前URL和验证码
        current_url = self.page.url

        # 获取代理（如果启用代理）
        if USE_PROXY:
            logger.info(f"[{self.group_id}] 开始获取代理...")
            if not self.get_new_proxy():
                logger.error(f"[{self.group_id}] 获取代理失败，已尝试5分钟")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "获取代理失败")
                return

            logger.info(f"[{self.group_id}] 代理获取成功: {self.proxy_host}:{self.proxy_port}")

            # 测试代理可用性
            if not self.test_proxy():
                logger.error(f"[{self.group_id}] 代理测试失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "代理测试失败")
                return

            logger.info(f"[{self.group_id}] 代理测试通过，准备切换浏览器")

        # 关闭浏览器
        if self.page:
            self.page.quit()
            time.sleep(2)

        # 重启浏览器并启用代理
        logger.info(f"[{self.group_id}] 重启浏览器并切换到代理模式...")
        if not self.setup_browser(use_proxy=USE_PROXY):
            logger.error(f"[{self.group_id}] 重启浏览器失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "重启浏览器失败")
            return

        # 重新访问页面
        self.page.get(current_url)
        time.sleep(3)

        # 输入验证码
        proxy_status = "代理模式" if USE_PROXY else "无代理模式"
        logger.info(f"[{self.group_id}] 已切换到{proxy_status}，开始输入验证码")
        if not self.input_verification_code(code):
            logger.error(f"[{self.group_id}] 验证码输入失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "验证码输入失败")
            return

        # 等待页面加载完成
        time.sleep(5)

        # 检查是否跳转到登录确认页面
        current_url = self.page.url
        if "loginDeepControl" in current_url:
            logger.info(f"[{self.group_id}] 检测到登录确认页面，查找登录按钮")
            # 查找并点击登录按钮
            login_button = self.page.ele("xpath://button[.//span[contains(text(), 'Log In')]]", timeout=10)
            if login_button:
                logger.info(f"[{self.group_id}] 找到登录按钮，点击确认")
                login_button.click()
                time.sleep(5)
            else:
                logger.warning(f"[{self.group_id}] 未找到登录按钮")

        # 获取并提交token
        self.get_and_submit_token()

    def get_and_submit_token(self):
        """获取并提交token"""
        try:
            # 检查是否跳转到cursor.com
            current_url = self.page.url

            # 获取token
            time.sleep(5)
            token = self.get_token()
            if not token:
                logger.error(f"[{self.group_id}] 获取token失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "获取token失败")
                return

            # 获取accessToken和refreshToken
            access_token, refresh_token = self.get_access_token_and_refresh_token(token)

            if access_token and refresh_token:
                logger.info(f"[{self.group_id}] 成功获取accessToken和refreshToken")
                # 提交token和tokens到API (异步)
                self.submit_token(self.email, token, access_token, refresh_token)
                logger.info(f"[{self.group_id}] 注册流程完成: {self.email} (token和tokens提交已触发异步处理)")
            else:
                logger.warning(f"[{self.group_id}] 未能获取accessToken和refreshToken，仅提交原token")
                # 仅提交原token到API (异步)
                self.submit_token(self.email, token)
                logger.info(f"[{self.group_id}] 注册流程完成: {self.email} (仅token提交已触发异步处理)")

        except Exception as e:
            logger.error(f"[{self.group_id}] token处理过程中出错: {e}")
        finally:
            # 清理资源并重启为无代理模式，为下一轮注册做准备
            try:
                logger.info(f"[{self.group_id}] 当前注册流程已完成，清理资源并准备下一轮")
                
                # 使用cleanup彻底清理当前浏览器
                self.cleanup()
                
                # 重启无代理浏览器
                time.sleep(1)  # 等待资源释放
                if self.setup_browser(use_proxy=False):
                    logger.info(f"[{self.group_id}] 已重启浏览器为无代理模式，准备下一轮注册")
                else:
                    logger.error(f"[{self.group_id}] 重启无代理浏览器失败")
            except Exception as e:
                logger.error(f"[{self.group_id}] 重置浏览器过程中出错: {e}")
                # 即使出错也尝试再次启动
                try:
                    time.sleep(2)  # 多等待一会儿
                    self.setup_browser(use_proxy=False)
                    logger.info(f"[{self.group_id}] 二次尝试重启浏览器成功")
                except:
                    logger.error(f"[{self.group_id}] 二次尝试重启浏览器也失败，将在下一轮注册时重试")
        
    def run(self):
        """运行注册流程"""
        # 初始化浏览器（无代理模式）
        if not self.setup_browser(use_proxy=False):
            logger.error(f"[{self.group_id}] 浏览器设置失败，线程终止")
            if self.error_callback:
                self.error_callback(self.group_id, "None", "浏览器初始化失败")
            return
            
        logger.info(f"[{self.group_id}] 浏览器初始化成功（无代理模式），准备开始注册流程")
        
        # 运行指定次数的注册流程
        registration_count = 0
        
        try:
            while registration_count < DEFAULT_REGISTRATION_COUNT:
                registration_count += 1
                # 记录开始时间
                registration_start_time = time.time()
                logger.info(f"[{self.group_id}] 开始第 {registration_count}/{DEFAULT_REGISTRATION_COUNT} 次注册流程")
                
                # 重置当前尝试次数
                self.current_attempt = 0
                
                # 生成新邮箱
                self.email = self.generate_email()
                if not self.email:
                    logger.error(f"[{self.group_id}] 获取邮箱失败，可能所有邮箱都已使用完毕，终止注册流程")
                    break

                logger.info(f"[{self.group_id}] 准备注册新账号: {self.email}")

                # 生成OAuth参数
                if not self.generate_oauth_params():
                    logger.error(f"[{self.group_id}] 生成OAuth参数失败，跳过本次注册")
                    continue

                # 构建state参数
                state_data = {
                    "returnTo": f"https://cursor.com/cn/loginDeepControl?challenge={self.challenge}&uuid={self.uuid}&mode=login"
                }
                state_json = json.dumps(state_data)
                encoded_state = quote(state_json)

                # 加载注册页面（无代理模式）
                encoded_email = quote(self.email)
                url = SIGNUP_URL.format(encoded_email, encoded_state)
                logger.info(f"[{self.group_id}] 访问注册URL: {url}")
                self.page.get(url)
                
                # 开始处理注册
                time.sleep(2)
                self.process_registration()
                
                # 记录结束时间和耗时
                registration_end_time = time.time()
                elapsed_time = registration_end_time - registration_start_time
                logger.info(f"[{self.group_id}] 完成注册流程: {self.email} - 耗时: {elapsed_time:.2f} 秒")
                
                # 根据注册耗时动态调整休息时间
                # if elapsed_time >= 90:
                #     rest_time = 2
                # else:
                #     rest_time = 92 - elapsed_time
                    
                # logger.info(f"[{self.group_id}] 注册耗时 {elapsed_time:.2f} 秒，休息 {rest_time:.2f} 秒")
                time.sleep(2)
            
            logger.info(f"[{self.group_id}] 完成全部 {DEFAULT_REGISTRATION_COUNT} 次注册流程")
        finally:
            # 所有注册完成后，清理资源
            self.cleanup()
            logger.info(f"[{self.group_id}] 线程资源已清理")

    def is_proxy_expired(self):
        """检查当前代理是否已过期"""
        if not self.proxy_start_time:
            return True  # 如果没有开始时间，视为已过期
            
        current_time = time.time()
        elapsed_minutes = (current_time - self.proxy_start_time) / 60
        
        if elapsed_minutes >= PROXY_LIFETIME_MINUTES:
            logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，超过设定的 {PROXY_LIFETIME_MINUTES} 分钟有效期")
            return True
            
        logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，还在有效期内 ({PROXY_LIFETIME_MINUTES} 分钟)")
        return False


class RegistrationManager:
    def __init__(self, num_threads=DEFAULT_THREADS, headless=False):
        self.num_threads = min(num_threads, MAX_THREADS)
        self.headless = headless
        self.registers = []
        self.successful = []
        self.failed = []
        
    def on_registration_finished(self, group_id, email, token):
        self.successful.append({
            "group_id": group_id,
            "email": email,
            "token": token,
            "timestamp": datetime.now().isoformat()
        })
        logger.info(f"注册成功: {email} (组: {group_id})")
            
#         # 将成功信息保存到文件
#         with open("successful_registrations.txt", "a", encoding="utf-8") as f:
#             f.write(f"{datetime.now().isoformat()} | {email} | {token}\n")
            
    def on_registration_error(self, group_id, email, error):
        self.failed.append({
            "group_id": group_id,
            "email": email,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        logger.error(f"注册失败: {email} (组: {group_id}): {error}")
        
    def start(self):
        """开始注册进程"""
        logger.info(f"启动 {self.num_threads} 个注册线程")
        
        # 清理浏览器进程
        cleanup_chrome_processes()
        
        for i in range(self.num_threads):
            group_id = f"{i}"  # 简化为数字索引
            register = DrissionCursorRegister(group_id, self.headless)
            
            # 设置回调
            register.set_callbacks(self.on_registration_finished, self.on_registration_error)
            
            # 创建线程运行注册实例
            thread = threading.Thread(target=register.run, daemon=True)
            
            # 保存实例
            self.registers.append(register)
            
            # 启动线程
            thread.start()
            
        logger.info("所有注册线程已启动")


def cleanup_chrome_processes():
    """清理Chrome进程"""
    global _chrome_process_ids
    
    if not _chrome_process_ids:
        logger.info("没有需要清理的Chrome进程...")
        return
        
    logger.info(f"清理 {len(_chrome_process_ids)} 个Chrome进程...")
    try:
        if os.name == 'nt':
            for pid in _chrome_process_ids:
                try:
                    os.system(f'taskkill /F /PID {pid} /T 2>nul')
                except:
                    pass
        else:
            for pid in _chrome_process_ids:
                try:
                    os.kill(pid, signal.SIGTERM)
                except:
                    pass
        _chrome_process_ids = []
    except Exception as e:
        logger.error(f"清理进程时出错: {e}")


def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info("接收到退出信号，正在清理资源...")
    cleanup_chrome_processes()
    sys.exit(0)


def main():
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 全局变量声明
    global USE_PROXY
    global DEFAULT_REGISTRATION_COUNT
    global PROXY_LIFETIME_MINUTES
    
    # 命令行参数
    parser = argparse.ArgumentParser(description="Cursor自动注册工具")
    parser.add_argument("-t", "--threads", type=int, default=DEFAULT_THREADS,
                        help=f"并行注册线程数量 (最大 {MAX_THREADS})")
    parser.add_argument("--headless", action="store_true", help="使用无头模式运行")
    parser.add_argument("--proxy", action="store_true", help="启用代理")
    parser.add_argument("-n", "--count", type=int, default=DEFAULT_REGISTRATION_COUNT,
                        help="每个线程的注册次数")
    parser.add_argument("-p", "--proxy-lifetime", type=int, default=PROXY_LIFETIME_MINUTES,
                        help="每个代理的有效时间（分钟）")
    args = parser.parse_args()
    
    # 设置代理参数
    USE_PROXY = True
    
    # 设置每个代理的有效时间
    if args.proxy_lifetime and args.proxy_lifetime > 0:
        PROXY_LIFETIME_MINUTES = args.proxy_lifetime
    logger.info(f"已设置每个代理最多使用 {PROXY_LIFETIME_MINUTES} 分钟")
        
    # 设置注册次数
    if args.count and args.count > 0:
        DEFAULT_REGISTRATION_COUNT = args.count
        logger.info(f"每个线程的注册次数已设置为: {DEFAULT_REGISTRATION_COUNT}")
        
    # 启动管理器
    manager = RegistrationManager(args.threads, args.headless)
    manager.start()
    
    # 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到用户中断，正在退出...")
        cleanup_chrome_processes()


if __name__ == "__main__":
    main() 