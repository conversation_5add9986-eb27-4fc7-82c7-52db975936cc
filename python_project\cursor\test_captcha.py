#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试人机验证处理的脚本
"""

from DrissionPage import ChromiumOptions, ChromiumPage
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_captcha_handling():
    """测试人机验证处理"""
    page = None
    try:
        logger.info("开始测试人机验证处理")
        
        # 设置浏览器选项
        co = ChromiumOptions()
        co.set_argument("--disable-extensions")
        co.set_argument("--disable-gpu")
        co.set_argument("--disable-dev-shm-usage")
        co.set_argument("--disable-infobars")
        co.set_argument("--disable-notifications")
        co.set_argument("--no-first-run")
        co.set_argument("--no-default-browser-check")
        co.auto_port()
        co.headless(False)  # 显示浏览器窗口以便观察
        
        # 启动浏览器
        page = ChromiumPage(co)
        time.sleep(2)
        
        # 访问测试URL
        test_url = "https://checkout.stripe.com/c/pay/cs_live_a16z8LhEj8gxx9bKWg7IOmxKO1mVSuZuj7iukPQKbFOdLrB5A8OCA2IsBf#fid2cGd2ZndsdXFsamtQa2x0cGBrYHZ2QGtkZ2lgYSc%2FY2RpdmApJ2R1bE5gfCc%2FJ3VuWmlsc2BaMDRJZzBJf0cxUV9SfVZMQlAxSWZkV3xzUTB9UjRMcj1fNkA0bEp1cEZnaUdJakltanQ2fVRrcTdQM3ZXNW5jdzNyclFhSWFURnxuY39La3IyMj1VZEoyazc1NXF2V2hTYDI3JyknY3dqaFZgd3Ngdyc%2FcXdwYCknaWR8anBxUXx1YCc%2FJ3Zsa2JpYFpscWBoJyknYGtkZ2lgVWlkZmBtamlhYHd2Jz9xd3BgeCUl"
        
        logger.info(f"访问测试URL: {test_url}")
        page.get(test_url)
        time.sleep(5)
        
        # 检测人机验证元素
        hcaptcha_elements = [
            "xpath://div[@id='h-captcha']",
            "xpath://iframe[contains(@src,'hcaptcha')]",
            "xpath://div[contains(@class,'h-captcha')]",
            "xpath://div[contains(@class,'hcaptcha')]",
            "@id=HCaptcha-root"
        ]
        
        hcaptcha_found = False
        hcaptcha_root = None
        for selector in hcaptcha_elements:
            hcaptcha_root = page.ele(selector, timeout=3)
            if hcaptcha_root:
                logger.info(f"检测到人机验证: {selector}")
                logger.info(f"元素类型: {type(hcaptcha_root)}")
                if hasattr(hcaptcha_root, 'tag'):
                    logger.info(f"元素标签: {hcaptcha_root.tag}")
                hcaptcha_found = True
                break
        
        if hcaptcha_found:
            logger.info("检测到人机验证，尝试处理")
            try:
                # 使用改进的处理方法
                if hcaptcha_root:
                    # 检查元素类型，避免对iframe使用run_js
                    if hasattr(hcaptcha_root, 'tag') and hcaptcha_root.tag == 'iframe':
                        logger.info("检测到iframe元素，直接点击获取焦点")
                        hcaptcha_root.click()
                    else:
                        # 将验证元素滚动到视图中并点击以获取焦点
                        try:
                            page.run_js("arguments[0].scrollIntoView(true);", hcaptcha_root)
                            logger.info("scrollIntoView成功")
                        except Exception as js_e:
                            logger.warning(f"scrollIntoView失败: {js_e}")
                        hcaptcha_root.click()
                    logger.info("已点击验证容器获取焦点")
                    time.sleep(1)

                # 发送Tab键聚焦到复选框，然后发送空格键进行点击
                page.actions.key_down('TAB')
                page.actions.key_up('TAB')
                time.sleep(1)
                page.actions.key_down('space')
                page.actions.key_up('space')
                logger.info("已发送Tab和Space键")
                time.sleep(2)
                
                logger.info("人机验证处理完成，等待30秒观察结果...")
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"处理人机验证时出错: {e}")
                return False
        else:
            logger.info("未检测到人机验证")
            time.sleep(10)
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return False
    finally:
        if page:
            try:
                page.quit()
            except:
                pass

if __name__ == "__main__":
    test_captcha_handling()
