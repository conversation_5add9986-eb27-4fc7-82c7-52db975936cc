#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理获取修改的脚本
"""

import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_proxy_data_parsing():
    """测试代理数据解析"""
    # 模拟您提供的返回数据格式
    mock_response = {
        "code": "SUCCESS",
        "data": [
            {
                "server": "*************:13633",
                "area_code": 990100,
                "area": "美国",
                "deadline": "2025-07-22 16:56:23"
            }
        ],
        "request_id": "4dfd670c-1bca-4f7d-bbb3-446b0eda0396"
    }
    
    logger.info("测试代理数据解析...")
    logger.info(f"模拟API返回数据: {json.dumps(mock_response, ensure_ascii=False, indent=2)}")
    
    # 模拟解析逻辑
    if mock_response.get("code") == "SUCCESS" and mock_response.get("data") and len(mock_response["data"]) > 0:
        proxy_info = mock_response["data"][0]
        server = proxy_info.get("server", "")
        area = proxy_info.get("area", "未知")
        area_code = proxy_info.get("area_code", "")
        deadline = proxy_info.get("deadline", "未知")
        request_id = mock_response.get("request_id", "")
        
        if ":" in server:
            proxy_host, proxy_port = server.split(":")
            proxy_start_time = time.time()
            
            logger.info("✅ 代理解析成功!")
            logger.info(f"   服务器: {server}")
            logger.info(f"   主机: {proxy_host}")
            logger.info(f"   端口: {proxy_port}")
            logger.info(f"   地区: {area} ({area_code})")
            logger.info(f"   到期时间: {deadline}")
            logger.info(f"   请求ID: {request_id}")
            return True
        else:
            logger.error(f"❌ 代理服务器格式错误: {server}")
            return False
    else:
        logger.error("❌ API返回数据格式错误")
        return False

def test_proxy_clearing():
    """测试代理信息清理"""
    logger.info("\n测试代理信息清理...")
    
    # 模拟代理信息
    proxy_host = "*************"
    proxy_port = "13633"
    proxy_start_time = time.time()
    
    logger.info(f"设置代理信息: {proxy_host}:{proxy_port}")
    
    # 清理代理信息
    proxy_host = None
    proxy_port = None
    proxy_start_time = None
    
    logger.info("✅ 代理信息已清理")
    logger.info(f"   proxy_host: {proxy_host}")
    logger.info(f"   proxy_port: {proxy_port}")
    logger.info(f"   proxy_start_time: {proxy_start_time}")
    
    return True

def main():
    """主测试函数"""
    logger.info("开始测试代理获取修改...")
    
    # 测试1: 代理数据解析
    if not test_proxy_data_parsing():
        logger.error("代理数据解析测试失败")
        return False
    
    # 测试2: 代理信息清理
    if not test_proxy_clearing():
        logger.error("代理信息清理测试失败")
        return False
    
    logger.info("\n🎉 所有测试通过!")
    logger.info("修改要点总结:")
    logger.info("1. ✅ 每次获取验证码后都会获取新的代理")
    logger.info("2. ✅ 正确解析API返回的数据格式 (code: SUCCESS, data数组)")
    logger.info("3. ✅ 记录详细的代理信息 (服务器、地区、到期时间、请求ID)")
    logger.info("4. ✅ 每次注册完成后清理代理信息")
    logger.info("5. ✅ 不再重复使用同一个代理进行多次注册")
    
    return True

if __name__ == "__main__":
    main()
