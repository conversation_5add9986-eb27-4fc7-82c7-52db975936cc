#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import logging
import json
import random
from DrissionPage import ChromiumPage, ChromiumOptions

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_simple_binding():
    """简化版绑卡测试"""
    try:
        logger.info("开始简化版绑卡测试...")
        
        # 创建浏览器
        co = ChromiumOptions()
        co.headless(False)
        page = ChromiumPage(co)
        
        logger.info("浏览器启动成功")
        
        # 先访问cursor.com域名
        logger.info("访问cursor.com...")
        page.get("https://cursor.com")
        time.sleep(3)
        
        # 设置WorkosCursorSessionToken cookie
        logger.info("设置cookie...")
        cookie_value = "user_01K130TGCZZVKV8TK55VHEHYA6%3A%3AeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzEzMFRHQ1paVktWOFRLNTVWSEVIWUE2IiwidGltZSI6IjE3NTM1MjE5NjYiLCJyYW5kb21uZXNzIjoiMjA2N2M5MTktYzg3Yy00ZmUxIiwiZXhwIjoxNzU4NzA1OTY2LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoid2ViIn0.os3g0uPBe5fAZjrsYFE2WaNvAMs8UAuinaY-ft3RV40"
        
        page.set.cookies([{
            'name': 'WorkosCursorSessionToken',
            'value': cookie_value,
            'domain': '.cursor.com',
            'path': '/',
            'secure': True,
            'httpOnly': True
        }])
        
        logger.info("Cookie设置完成")
        
        # 访问cursor trial页面
        trial_url = "https://cursor.com/cn/trial"
        logger.info(f"访问cursor trial页面: {trial_url}")
        page.get(trial_url)
        time.sleep(5)
        
        logger.info(f"当前页面URL: {page.url}")
        logger.info(f"页面标题: {page.title}")
        
        # 查找页面上的所有按钮
        logger.info("查找页面上的所有按钮...")
        buttons = page.eles("tag:button")
        logger.info(f"找到 {len(buttons)} 个按钮")
        
        for i, button in enumerate(buttons[:10]):  # 只显示前10个
            try:
                button_text = button.text.strip()
                if button_text:
                    logger.info(f"按钮 {i+1}: {button_text}")
            except:
                logger.info(f"按钮 {i+1}: 无法获取文本")
        
        # 查找链接
        logger.info("查找页面上的所有链接...")
        links = page.eles("tag:a")
        logger.info(f"找到 {len(links)} 个链接")
        
        for i, link in enumerate(links[:10]):  # 只显示前10个
            try:
                link_text = link.text.strip()
                if link_text:
                    logger.info(f"链接 {i+1}: {link_text}")
            except:
                logger.info(f"链接 {i+1}: 无法获取文本")
        
        # 尝试查找可能的试用或Continue按钮
        logger.info("查找试用相关按钮...")
        possible_selectors = [
            "xpath://button[contains(text(),'开始试用')]",
            "xpath://button[contains(text(),'Start Trial')]",
            "xpath://button[contains(text(),'试用')]",
            "xpath://button[contains(text(),'Continue')]",
            "xpath://button[contains(text(),'继续')]",
            "xpath://a[contains(text(),'开始试用')]",
            "xpath://a[contains(text(),'Start Trial')]",
            "xpath://a[contains(text(),'Continue')]",
            "xpath://a[contains(text(),'继续')]",
            "xpath://*[contains(@class,'trial')]",
            "xpath://*[contains(@class,'start')]",
            "xpath://*[contains(@class,'continue')]"
        ]
        
        found_elements = []
        for selector in possible_selectors:
            elements = page.eles(selector, timeout=2)
            if elements:
                for element in elements:
                    try:
                        text = element.text.strip()
                        if text:
                            found_elements.append((selector, text))
                            logger.info(f"找到元素: {selector} - 文本: {text}")
                    except:
                        pass
        
        if found_elements:
            logger.info(f"总共找到 {len(found_elements)} 个相关元素")
            
            # 尝试点击第一个找到的元素
            first_selector, first_text = found_elements[0]
            logger.info(f"尝试点击第一个元素: {first_text}")
            
            element = page.ele(first_selector, timeout=5)
            if element:
                element.click()
                logger.info("点击成功，等待页面跳转...")
                time.sleep(5)
                
                new_url = page.url
                logger.info(f"跳转后的URL: {new_url}")
                
                if "stripe.com" in new_url or "checkout.stripe.com" in new_url:
                    logger.info("✅ 成功跳转到Stripe绑卡页面！")
                else:
                    logger.warning("未跳转到Stripe页面")
            else:
                logger.error("无法找到要点击的元素")
        else:
            logger.warning("未找到任何相关的试用按钮")
        
        # 保持浏览器打开
        logger.info("测试完成，浏览器将保持打开状态...")
        input("按Enter键关闭浏览器...")
        page.quit()
        
    except Exception as e:
        logger.error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_binding()
