#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import logging
from DrissionPage import ChromiumPage, ChromiumOptions

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_card_binding.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def test_card_binding_captcha(test_url=None):
    """测试绑卡页面的人机验证处理"""

    # 如果没有提供测试URL，使用默认的
    if not test_url:
        test_url = "https://checkout.stripe.com/c/pay/cs_live_a1r6myAangpVJF0SF08EuWcrRhqV61ujVpv9MZDY7EqM6HvF9B7BTK4N28#fid2cGd2ZndsdXFsamtQa2x0cGBrYHZ2QGtkZ2lgYSc%2FY2RpdmApJ2R1bE5gfCc%2FJ3VuWmlsc2BaMDRJZzBJf0cxUV9SfVZMQlAxSWZkV3xzUTB9UjRMcj1fNkA0bEp1cEZnaUdJakltanQ2fVRrcTdQM3ZXNW5jdzNyclFhSWFURnxuY39La3IyMj1VZEoyazc1NXF2V2hTYDI3JyknY3dqaFZgd3Ngdyc%2FcXdwYCknaWR8anBxUXx1YCc%2FJ3Zsa2JpYFpscWBoJyknYGtkZ2lgVWlkZmBtamlhYHd2Jz9xd3BgeCUl"
        logger.warning("使用默认测试URL，如果页面过期请提供新的URL")

    logger.info(f"测试URL: {test_url}")
    
    # 配置浏览器选项
    options = ChromiumOptions()
    options.set_argument('--no-sandbox')
    options.set_argument('--disable-dev-shm-usage')
    options.set_argument('--disable-blink-features=AutomationControlled')
    options.set_pref('excludeSwitches', ['enable-automation'])
    options.set_pref('useAutomationExtension', False)
    
    try:
        # 创建浏览器实例
        logger.info("创建浏览器实例...")
        page = ChromiumPage(addr_or_opts=options)
        
        # 设置用户代理
        page.run_js('Object.defineProperty(navigator, "webdriver", {get: () => undefined})')
        
        # 访问测试URL
        logger.info(f"访问测试URL: {test_url}")
        page.get(test_url)
        time.sleep(5)
        
        # 等待页面加载
        logger.info("等待页面加载...")
        time.sleep(3)
        
        # 检查页面是否正确加载
        current_url = page.url
        logger.info(f"当前页面URL: {current_url}")
        
        # 等待页面完全加载
        time.sleep(5)

        # 检查页面内容
        page_title = page.title
        logger.info(f"页面标题: {page_title}")

        # 检查是否有错误信息
        error_elements = page.eles("xpath://*[contains(text(),'error') or contains(text(),'Error') or contains(text(),'expired') or contains(text(),'invalid')]")
        if error_elements:
            for error in error_elements:
                logger.warning(f"页面错误信息: {error.text}")

        # 查找信用卡输入框，确认页面加载正确
        logger.info("尝试查找信用卡输入框...")

        # 尝试多种可能的选择器
        card_selectors = [
            "xpath://input[@placeholder='1234 1234 1234 1234']",
            "xpath://input[@name='cardnumber']",
            "xpath://input[contains(@placeholder,'card')]",
            "xpath://input[contains(@placeholder,'1234')]",
            "xpath://input[@data-testid='card-number']",
            "xpath://input[@id='cardNumber']",
            "xpath://input[contains(@class,'card')]",
            "xpath://iframe[@name='__privateStripeFrame']//input"  # Stripe iframe
        ]

        card_input = None
        for selector in card_selectors:
            logger.info(f"尝试选择器: {selector}")
            card_input = page.ele(selector, timeout=3)
            if card_input:
                logger.info(f"找到信用卡输入框: {selector}")
                break

        if not card_input:
            # 尝试选择信用卡支付方式
            logger.info("尝试选择信用卡支付方式...")
            card_option_selectors = [
                "xpath://input[@id='payment-method-accordion-item-title-card']",
                "xpath://label[contains(text(),'Card')]",
                "xpath://button[contains(text(),'Card')]",
                "xpath://*[@data-testid='payment-method-card']"
            ]

            card_option_selected = False
            for selector in card_option_selectors:
                card_option = page.ele(selector, timeout=3)
                if card_option:
                    logger.info(f"找到信用卡选项: {selector}")
                    card_option.click()
                    time.sleep(2)
                    card_option_selected = True
                    break

            if card_option_selected:
                # 重新尝试查找信用卡输入框
                logger.info("已选择信用卡支付方式，重新查找输入框...")
                for selector in card_selectors:
                    card_input = page.ele(selector, timeout=3)
                    if card_input:
                        logger.info(f"找到信用卡输入框: {selector}")
                        break

            if not card_input:
                # 尝试切换到iframe
                logger.info("尝试切换到Stripe iframe...")
                iframes = page.eles("xpath://iframe")
                logger.info(f"找到 {len(iframes)} 个iframe")

                for i, iframe in enumerate(iframes):
                    try:
                        src = iframe.attr('src') or ''
                        name = iframe.attr('name') or ''
                        logger.info(f"Iframe {i}: src='{src[:100]}...', name='{name}'")

                        if 'stripe' in src.lower() or 'card' in name.lower():
                            logger.info(f"尝试切换到Stripe iframe {i}")
                            # 注意：DrissionPage可能不支持iframe切换，这里只是尝试
                            try:
                                page.get_frame(iframe)
                                card_input = page.ele("xpath://input", timeout=3)
                                if card_input:
                                    logger.info("在iframe中找到输入框")
                                    break
                            except Exception as e:
                                logger.warning(f"切换iframe失败: {e}")
                    except Exception as e:
                        logger.warning(f"处理iframe {i}时出错: {e}")

            if not card_input:
                # 打印页面所有input元素用于调试
                logger.info("未找到信用卡输入框，列出所有input元素:")
                all_inputs = page.eles("xpath://input")
                for i, inp in enumerate(all_inputs[:10]):  # 只显示前10个
                    try:
                        placeholder = inp.attr('placeholder') or ''
                        name = inp.attr('name') or ''
                        id_attr = inp.attr('id') or ''
                        logger.info(f"Input {i}: placeholder='{placeholder}', name='{name}', id='{id_attr}'")
                    except:
                        logger.info(f"Input {i}: 无法获取属性")

                logger.error("未找到信用卡输入框，页面可能未正确加载或已过期")
                return False

        # 填写测试信用卡信息
        logger.info("开始填写测试信用卡信息...")

        # 填写卡号
        logger.info("填写卡号...")
        card_input.click()  # 先点击获取焦点
        time.sleep(0.5)
        card_input.clear()
        time.sleep(0.5)
        card_input.input("****************", clear=True)
        time.sleep(1)
        logger.info("卡号填写完成")

        # 填写过期日期
        expiry_input = page.ele("xpath://input[@placeholder='MM / YY']", timeout=5)
        if not expiry_input:
            expiry_input = page.ele("xpath://input[@name='exp-date']", timeout=5)
        if not expiry_input:
            expiry_input = page.ele("xpath://input[contains(@placeholder,'MM')]", timeout=5)

        if expiry_input:
            logger.info("填写过期日期...")
            expiry_input.click()
            time.sleep(0.5)
            expiry_input.clear()
            time.sleep(0.5)
            expiry_input.input("1225", clear=True)  # 使用MMYY格式
            time.sleep(1)
            logger.info("过期日期填写完成")
        else:
            logger.error("未找到过期日期输入框")
            return False

        # 填写CVC
        cvc_input = page.ele("xpath://input[@placeholder='CVC']", timeout=5)
        if not cvc_input:
            cvc_input = page.ele("xpath://input[@name='cvc']", timeout=5)
        if not cvc_input:
            cvc_input = page.ele("xpath://input[contains(@placeholder,'CVC')]", timeout=5)

        if cvc_input:
            logger.info("填写CVC...")
            cvc_input.click()
            time.sleep(0.5)
            cvc_input.clear()
            time.sleep(0.5)
            cvc_input.input("123", clear=True)
            time.sleep(1)
            logger.info("CVC填写完成")
        else:
            logger.error("未找到CVC输入框")
            return False

        # 填写姓名
        name_input = page.ele("xpath://input[@id='billingName']", timeout=5)
        if not name_input:
            name_input = page.ele("xpath://input[@name='billingName']", timeout=5)
        if not name_input:
            name_input = page.ele("xpath://input[contains(@placeholder,'name')]", timeout=5)

        if name_input:
            logger.info("填写姓名...")
            name_input.click()
            time.sleep(0.5)
            name_input.clear()
            time.sleep(0.5)
            name_input.input("Test User", clear=True)
            time.sleep(1)
            logger.info("姓名填写完成")
        else:
            logger.error("未找到姓名输入框")
            return False

        # 验证所有字段都已填写
        logger.info("验证所有字段填写情况...")
        card_value = card_input.attr('value') or page.run_js("return arguments[0].value;", card_input)
        expiry_value = expiry_input.attr('value') or page.run_js("return arguments[0].value;", expiry_input)
        cvc_value = cvc_input.attr('value') or page.run_js("return arguments[0].value;", cvc_input)
        name_value = name_input.attr('value') or page.run_js("return arguments[0].value;", name_input)

        logger.info(f"卡号: {card_value}")
        logger.info(f"过期日期: {expiry_value}")
        logger.info(f"CVC: {cvc_value}")
        logger.info(f"姓名: {name_value}")

        if not all([card_value, expiry_value, cvc_value, name_value]):
            logger.error("部分字段未正确填写")
            return False

        logger.info("所有信用卡信息填写完成，准备提交")
        
        # 点击提交按钮
        submit_button = page.ele("xpath://button[@data-testid='hosted-payment-submit-button']", timeout=10)
        if submit_button:
            logger.info("找到提交按钮，点击提交...")
            submit_button.click()
            time.sleep(5)
        else:
            logger.error("未找到提交按钮")
            return False
        
        # 检查是否出现人机验证
        logger.info("检查是否出现人机验证...")
        hcaptcha_elements = [
            "@id=HCaptcha-root",
            "xpath://div[@id='h-captcha']", 
            "xpath://iframe[contains(@src,'hcaptcha')]",
            "xpath://div[contains(@class,'h-captcha')]",
            "xpath://div[contains(@class,'hcaptcha')]",
            "xpath://div[contains(@class,'HCaptcha-container')]"
        ]
        
        hcaptcha_found = False
        hcaptcha_root = None
        for selector in hcaptcha_elements:
            hcaptcha_root = page.ele(selector, timeout=3)
            if hcaptcha_root:
                logger.info(f"检测到人机验证: {selector}")
                hcaptcha_found = True
                break
        
        if hcaptcha_found:
            logger.info("检测到人机验证，开始处理...")
            
            # 使用Tab+空格方式处理验证
            logger.info("使用Tab+空格方式处理人机验证")

            # 发送两次Tab键：第一次到关闭按钮，第二次到复选框，然后发送空格键进行点击
            page.actions.key_down('TAB')
            page.actions.key_up('TAB')
            time.sleep(0.5)
            page.actions.key_down('TAB')
            page.actions.key_up('TAB')
            time.sleep(1)
            page.actions.key_down('space')
            page.actions.key_up('space')
            logger.info("已发送两次Tab和Space键")
            time.sleep(2)
            
            # 检测验证是否完成的函数
            def is_captcha_completed():
                # 检查h-captcha-response是否有值
                response_element = page.ele("xpath://textarea[@name='h-captcha-response']", timeout=1)
                if response_element:
                    try:
                        response_value = response_element.attr('value') or page.run_js("return arguments[0].value;", response_element)
                        if response_value and response_value.strip():
                            logger.info("检测到h-captcha-response有值，验证完成")
                            return True
                    except Exception as e:
                        logger.warning(f"检查h-captcha-response时出错: {e}")
                
                # 检查g-recaptcha-response是否有值
                recaptcha_element = page.ele("xpath://textarea[@name='g-recaptcha-response']", timeout=1)
                if recaptcha_element:
                    try:
                        recaptcha_value = recaptcha_element.attr('value') or page.run_js("return arguments[0].value;", recaptcha_element)
                        if recaptcha_value and recaptcha_value.strip():
                            logger.info("检测到g-recaptcha-response有值，验证完成")
                            return True
                    except Exception as e:
                        logger.warning(f"检查g-recaptcha-response时出错: {e}")
                
                # 检查验证容器是否消失
                captcha_container = page.ele("xpath://div[contains(@class,'HCaptcha-container')]", timeout=1)
                if not captcha_container:
                    logger.info("验证容器消失，验证完成")
                    return True
                    
                return False
            
            # 等待人机验证完成
            logger.info("等待人机验证完成...")
            for i in range(60):
                time.sleep(1)
                
                # 检查验证是否完成
                if is_captcha_completed():
                    logger.info("人机验证已完成！")
                    # 验证完成后再等待一段时间让页面跳转
                    for j in range(10):
                        time.sleep(1)
                        current_url = page.url
                        if "cursor.com" in current_url:
                            logger.info(f"验证完成，页面已跳转到: {current_url}")
                            return True
                    logger.info(f"验证完成但未跳转，当前URL: {page.url}")
                    return True
                
                # 检查是否直接跳转了
                current_url = page.url
                if "cursor.com" in current_url:
                    logger.info(f"页面已跳转到: {current_url}")
                    return True
                
                if i % 10 == 0:
                    logger.info(f"等待验证完成中... ({i}/60秒)")
            
            logger.warning("人机验证等待超时")
            return False
        else:
            logger.info("未检测到人机验证")
            # 检查是否直接成功
            time.sleep(5)
            current_url = page.url
            if "cursor.com" in current_url:
                logger.info(f"绑定成功，页面已跳转到: {current_url}")
                return True
            else:
                logger.info(f"当前页面: {current_url}")
                return False
    
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        return False
    
    finally:
        try:
            # 保持浏览器打开一段时间以便观察
            logger.info("测试完成，保持浏览器打开30秒以便观察...")
            time.sleep(30)
            page.quit()
        except:
            pass

if __name__ == "__main__":
    logger.info("开始测试绑卡页面人机验证处理...")
    success = test_card_binding_captcha()
    if success:
        logger.info("测试成功！")
    else:
        logger.info("测试失败！")
