from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import signal
import random
import logging
import json
import sys
from urllib.parse import quote
import requests
from datetime import datetime
import threading
import psutil
from colorama import Fore, Style
import argparse
from DrissionPage.errors import ElementLostError
import uuid
import base64
import hashlib
import secrets
import urllib3
import queue

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cursor_registration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
SIGNUP_URL = "https://authenticator.cursor.sh/password?email={}&state={}&redirect_uri=https://cursor.com/api/auth/callback"
API_BASE_URL = "http://119.29.20.123:8080/admin-api"  # 修改为你的实际API端点
EMAIL_CODE_ENDPOINT = "/cqp/cursoremailpool/getEmailCode?token=fe84864d7d354b53ae65e9fee25b0067"
TOKEN_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/saveRecord?token=fe84864d7d354b53ae65e9fee25b0067"

# 代理配置
USE_PROXY = False  # 是否使用代理
USE_PROXY_FULL_PROCESS = False  # 是否全程使用代理（仅当USE_PROXY=True时生效）
# 当USE_PROXY=True且USE_PROXY_FULL_PROCESS=True时：全程使用代理，获取验证码后不关闭浏览器，直接输入验证码
# 当USE_PROXY=True且USE_PROXY_FULL_PROCESS=False时：部分代理模式，获取验证码后重启浏览器并启用代理
# 当USE_PROXY=False时：无代理模式，获取验证码后不关闭浏览器，直接输入验证码

# 主要代理API
PROXY_API_URL = "https://overseas.proxy.qg.net/get?key=EB914784&num=1&keep_alive=5&area=990201"
# 备用代理API（查询正在使用的代理）
BACKUP_PROXY_API_URL = "https://overseas.proxy.qg.net/query?key=EB914784"
PROXY_LIFETIME_MINUTES = 5  # 每个代理的有效时间（分钟）
PROXY_RETRY_TIMES = 3  # 主要API重试次数（3次失败后切换到备用API）
PROXY_RETRY_INTERVAL = 3  # 代理获取重试间隔（秒）

# 控制注册线程数量的常量
DEFAULT_THREADS = 1  # 默认并发线程数
MAX_THREADS = 20  # 最大并发线程数
DEFAULT_REGISTRATION_COUNT = 1000  # 每个线程默认注册次数

# 邮箱读取类型常量（保留以备将来使用）
EMAIL_SOURCE_HUMKT = "humkt"  # 从humkt API读取
EMAIL_SOURCE_TXT = "txt"  # 从TXT文件读取
# 注意：当前逻辑已改为优先从gmail_accounts.txt读取，然后从API获取

# 信用卡读取控制常量
USE_FIXED_FIRST_CARD = True  # 是否固定读取第一条信用卡数据（True：固定读取第一条且不删除，False：读取第一条并删除）

# 浏览器优化常量
BROWSER_RESTART_INTERVAL = 1  # 每隔多少次注册重启一次浏览器（防止资源累积）

# 全局变量，用于存储浏览器进程ID
_chrome_process_ids = []

# 全局邮箱池，用于存储从humkt获取的邮箱
_email_pool = []
_email_pool_lock = threading.Lock()  # 线程锁，保证邮箱池操作的线程安全

# 绑卡任务队列和管理器
_card_binding_queue = queue.Queue()
_card_binding_manager = None
_card_binding_lock = threading.Lock()

class DrissionCursorRegister:
    def __init__(self, group_id, headless=False):
        self.group_id = group_id
        self.headless = headless
        self.email = None
        self.email_password = None  # 添加邮箱密码属性
        self.page = None
        self.token = None
        self.code = None
        self.profile_path = f"chrome-data-cursor/group_{group_id}"  # 简化命名为固定格式
        self.max_attempts = 10  # 最大尝试次数
        self.current_attempt = 0
        self.before_pids = []
        self.success_callback = None
        self.error_callback = None
        self.registration_count = 0  # 添加注册计数器，用于定期重启浏览器
        # 代理相关属性（仅在启用代理时初始化）
        if USE_PROXY:
            self.proxy_host = None
            self.proxy_port = None
            self.proxy_start_time = None  # 记录代理获取的时间
        else:
            # 确保代理相关属性为None，避免残留信息
            self.proxy_host = None
            self.proxy_port = None
            self.proxy_start_time = None
        # OAuth相关参数
        self.uuid = None
        self.challenge = None
        self.verifier = None

    @staticmethod
    def get_email_from_pool():
        """从邮箱池中获取一个邮箱"""
        global _email_pool, _email_pool_lock

        with _email_pool_lock:
            if _email_pool:
                email_data = _email_pool.pop(0)  # 取出第一个邮箱
                logger.info(f"从邮箱池中获取邮箱: {email_data['email']} (池中剩余: {len(_email_pool)})")
                return email_data
            else:
                logger.info("邮箱池为空")
                return None

    @staticmethod
    def add_emails_to_pool(email_list):
        """将邮箱列表添加到邮箱池"""
        global _email_pool, _email_pool_lock

        with _email_pool_lock:
            for email_data in email_list:
                if "|" in email_data:
                    parts = email_data.split("|")
                    if len(parts) == 2:
                        email, password = parts
                        _email_pool.append({
                            'email': email,
                            'password': password
                        })
                        logger.info(f"添加邮箱到池: {email}")
                    else:
                        logger.error(f"邮箱数据格式错误: {email_data}")
                        # 记录格式错误的邮箱
                        DrissionCursorRegister.write_failed_email_static(email_data, "邮箱数据格式错误")
                else:
                    logger.error(f"邮箱数据缺少分隔符: {email_data}")
                    # 记录格式错误的邮箱
                    DrissionCursorRegister.write_failed_email_static(email_data, "邮箱数据缺少分隔符")

            logger.info(f"邮箱池更新完成，当前池中邮箱数量: {len(_email_pool)}")

    @staticmethod
    def get_pool_size():
        """获取邮箱池大小"""
        global _email_pool, _email_pool_lock

        with _email_pool_lock:
            return len(_email_pool)

    @staticmethod
    def write_failed_email_static(email_data, reason):
        """静态方法：将失败的邮箱账号写入文件"""
        try:
            with open("gmail_humkt_fail.txt", "a", encoding="utf-8") as f:
                # 如果email_data包含密码，只记录邮箱和密码
                if "|" in email_data:
                    parts = email_data.split("|")
                    if len(parts) >= 2:
                        email, password = parts[0], parts[1]
                        f.write(f"{email}|{password}\n")
                    else:
                        f.write(f"{email_data}\n")
                else:
                    f.write(f"{email_data}\n")
            logger.info(f"已记录失败邮箱: {email_data}")
        except Exception as e:
            logger.error(f"写入失败邮箱文件时出错: {e}")



    @staticmethod
    def load_emails_from_txt(file_path="gmail_accounts.txt"):
        """从TXT文件加载邮箱账号"""
        email_list = []
        try:
            if not os.path.exists(file_path):
                logger.warning(f"邮箱文件不存在: {file_path}")
                return email_list

            with open(file_path, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith("#"):  # 跳过空行和注释行
                        continue

                    if "|" in line:
                        parts = line.split("|")
                        if len(parts) >= 2:
                            email, password = parts[0].strip(), parts[1].strip()
                            email_list.append({
                                'email': email,
                                'password': password
                            })
                            logger.info(f"从TXT文件加载邮箱: {email}")
                        else:
                            logger.warning(f"TXT文件第{line_num}行格式错误: {line}")
                    else:
                        logger.warning(f"TXT文件第{line_num}行缺少分隔符: {line}")

            logger.info(f"从TXT文件成功加载 {len(email_list)} 个邮箱账号")
            return email_list

        except Exception as e:
            logger.error(f"读取TXT邮箱文件时出错: {e}")
            return email_list

    def set_callbacks(self, success_callback, error_callback):
        """设置回调函数"""
        self.success_callback = success_callback
        self.error_callback = error_callback

    def generate_oauth_params(self):
        """生成OAuth相关参数：uuid、challenge、verifier"""
        try:
            # 生成UUID
            self.uuid = str(uuid.uuid4())
            logger.info(f"[{self.group_id}] 生成UUID: {self.uuid}")

            # 生成code_verifier (32字节随机数，Base64编码)
            random_bytes = secrets.token_bytes(32)
            self.verifier = base64.urlsafe_b64encode(random_bytes).decode('utf-8').rstrip('=')
            logger.info(f"[{self.group_id}] 生成verifier: {self.verifier}")

            # 生成code_challenge (SHA-256哈希 + Base64编码)
            sha256_hash = hashlib.sha256(self.verifier.encode('utf-8')).digest()
            self.challenge = base64.urlsafe_b64encode(sha256_hash).decode('utf-8').rstrip('=')
            logger.info(f"[{self.group_id}] 生成challenge: {self.challenge}")

            return True
        except Exception as e:
            logger.error(f"[{self.group_id}] 生成OAuth参数失败: {e}")
            return False

    def get_access_token_and_refresh_token(self, token):
        """根据token获取accessToken和refreshToken"""
        try:
            logger.info(f"[{self.group_id}] 开始获取accessToken和refreshToken")

            # 处理token格式
            if not "user_01" in token:
                # 这里简化处理，实际可能需要JWT解析
                # 假设token格式需要调整
                processed_token = f"user_01::{token}"
            else:
                processed_token = token

            # URL编码处理
            encoded_token = quote(processed_token, safe='')

            # 调用API获取tokens
            api_url = f"https://api2.cursor.sh/auth/poll?uuid={self.uuid}&verifier={self.verifier}"
            logger.info(f"[{self.group_id}] 调用API: {api_url}")

            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"[{self.group_id}] API响应: {result}")

                # 提取accessToken和refreshToken
                access_token = result.get('accessToken')
                refresh_token = result.get('refreshToken')

                if access_token and refresh_token:
                    logger.info(f"[{self.group_id}] 成功获取tokens")
                    return access_token, refresh_token
                else:
                    logger.error(f"[{self.group_id}] API响应中未找到tokens")
                    return None, None
            else:
                logger.error(f"[{self.group_id}] API请求失败: {response.status_code} - {response.text}")
                return None, None

        except Exception as e:
            logger.error(f"[{self.group_id}] 获取tokens时出错: {e}")
            return None, None
        
    def generate_email(self):
        """获取Gmail邮箱地址（优先从TXT文件获取，然后从API获取）"""
        # 优先从gmail_accounts.txt文件获取邮箱
        email_data = self.get_email_from_txt_file()
        if email_data:
            self.email_password = email_data['password']
            logger.info(f"[{self.group_id}] 从TXT文件获取Gmail邮箱: {email_data['email']}")
            return email_data['email']

        # TXT文件没有邮箱，从邮箱池获取
        email_data = self.get_email_from_pool()
        if email_data:
            self.email_password = email_data['password']
            logger.info(f"[{self.group_id}] 从邮箱池获取Gmail邮箱: {email_data['email']}")
            return email_data['email']

        # 邮箱池也为空，从API获取新邮箱
        logger.info(f"[{self.group_id}] TXT文件和邮箱池都为空，开始从API获取新邮箱")
        return self.buy_new_emails()

    def get_email_from_txt_file(self, file_path="gmail_accounts.txt"):
        """从TXT文件获取一个邮箱并从文件中移除"""
        try:
            if not os.path.exists(file_path):
                logger.info(f"[{self.group_id}] TXT文件不存在: {file_path}")
                return None

            # 读取所有邮箱
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # 过滤有效的邮箱行
            valid_emails = []
            other_lines = []  # 保存注释行和空行

            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    other_lines.append(line)
                    continue

                if "|" in line:
                    parts = line.split("|")
                    if len(parts) >= 2:
                        email, password = parts[0].strip(), parts[1].strip()
                        valid_emails.append({
                            'email': email,
                            'password': password,
                            'original_line': line
                        })
                    else:
                        other_lines.append(line)  # 格式错误的行保留
                else:
                    other_lines.append(line)  # 格式错误的行保留

            if not valid_emails:
                logger.info(f"[{self.group_id}] TXT文件中没有可用的邮箱")
                return None

            # 取第一个邮箱
            selected_email = valid_emails[0]
            remaining_emails = valid_emails[1:]  # 剩余的邮箱

            # 重新写入文件，移除已使用的邮箱
            with open(file_path, "w", encoding="utf-8") as f:
                # 先写入注释行和空行
                for line in other_lines:
                    f.write(line + "\n")

                # 写入剩余的邮箱
                for email_data in remaining_emails:
                    f.write(f"{email_data['email']}|{email_data['password']}\n")

            logger.info(f"[{self.group_id}] 从TXT文件获取邮箱: {selected_email['email']}，剩余 {len(remaining_emails)} 个邮箱")
            return selected_email

        except Exception as e:
            logger.error(f"[{self.group_id}] 从TXT文件获取邮箱时出错: {e}")
            return None

    def buy_new_emails(self):
        """下单获取新邮箱并加入邮箱池"""
        max_retries = 20
        retry_delay = 10

        for attempt in range(max_retries + 1):
            try:
                logger.info(f"[{self.group_id}] 正在通过humkt下单获取Gmail邮箱 (尝试 {attempt + 1}/{max_retries + 1})")

                # 第一步：下单获取订单ID
                buy_url = "https://www.humkt.com/api/buy?id=1514&qty=1&token=qUunKMEIzCeAXLB6YOfAfdyyRBgesJ1Q"
                response = requests.get(buy_url, timeout=20, verify=False)

                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"[{self.group_id}] 下单接口完整响应数据:")
                    logger.info(f"[{self.group_id}] 响应状态码: {response.status_code}")
                    logger.info(f"[{self.group_id}] 完整JSON响应: {result}")
                    logger.info(f"[{self.group_id}] 响应数据类型: {type(result)}")

                    if result.get("code") == 1 and result.get("data"):
                        data = result["data"]
                        logger.info(f"[{self.group_id}] 下单data字段内容: {data}")
                        logger.info(f"[{self.group_id}] 下单data字段类型: {type(data)}")

                        order_id = data["id"]
                        logger.info(f"[{self.group_id}] 下单成功，订单ID: {order_id}")

                        # 第二步：获取订单详情（循环查询）
                        email_list = self.get_order_details(order_id)
                        if email_list:
                            # 启动延迟获取线程，30秒后再次获取订单详情
                            delay_thread = threading.Thread(
                                target=self.delayed_order_check,
                                args=(order_id, email_list),
                                daemon=True
                            )
                            delay_thread.start()

                            # 从邮箱列表中取出第一个邮箱用于当前注册
                            if email_list:
                                first_email_str = email_list[0]
                                if "|" in first_email_str:
                                    parts = first_email_str.split("|")
                                    if len(parts) >= 2:
                                        email, password = parts[0], parts[1]
                                        self.email_password = password

                                        # 将剩余的邮箱保存到TXT文件
                                        remaining_emails = email_list[1:]  # 剩余的邮箱
                                        if remaining_emails:
                                            self.save_emails_to_txt_file(remaining_emails)

                                        logger.info(f"[{self.group_id}] 成功获取Gmail邮箱: {email}")
                                        return email
                                    else:
                                        logger.error(f"[{self.group_id}] 邮箱格式错误: {first_email_str}")
                                        return None
                                else:
                                    logger.error(f"[{self.group_id}] 邮箱数据缺少分隔符: {first_email_str}")
                                    return None
                            else:
                                logger.error(f"[{self.group_id}] 邮箱列表为空")
                                return None
                        else:
                            logger.error(f"[{self.group_id}] 订单详情查询失败")
                    else:
                        logger.error(f"[{self.group_id}] 下单失败")
                        logger.error(f"[{self.group_id}] code字段: {result.get('code')}")
                        logger.error(f"[{self.group_id}] message字段: {result.get('message')}")
                        logger.error(f"[{self.group_id}] data字段: {result.get('data')}")
                else:
                    logger.error(f"[{self.group_id}] 下单请求失败: {response.status_code}")
                    logger.error(f"[{self.group_id}] 响应内容: {response.text}")

            except Exception as e:
                logger.error(f"[{self.group_id}] 获取Gmail邮箱异常: {e}")

            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries:
                logger.info(f"[{self.group_id}] {retry_delay}秒后重试获取邮箱...")
                time.sleep(retry_delay)

        logger.error(f"[{self.group_id}] 获取Gmail邮箱失败，已达到最大重试次数")
        return None

    def save_emails_to_txt_file(self, email_list, file_path="gmail_accounts.txt"):
        """将邮箱列表保存到TXT文件"""
        try:
            logger.info(f"[{self.group_id}] 开始保存 {len(email_list)} 个邮箱到TXT文件")

            # 读取现有文件内容（如果存在）
            existing_lines = []
            if os.path.exists(file_path):
                with open(file_path, "r", encoding="utf-8") as f:
                    existing_lines = f.readlines()

            # 写入文件，追加新邮箱
            with open(file_path, "w", encoding="utf-8") as f:
                # 先写入现有内容
                for line in existing_lines:
                    f.write(line)

                # 追加新邮箱
                for email_str in email_list:
                    if "|" in email_str:
                        parts = email_str.split("|")
                        if len(parts) >= 2:
                            email, password = parts[0], parts[1]
                            f.write(f"{email}|{password}\n")
                            logger.info(f"[{self.group_id}] 保存邮箱到TXT: {email}")
                        else:
                            logger.warning(f"[{self.group_id}] 邮箱格式错误，跳过: {email_str}")
                    else:
                        logger.warning(f"[{self.group_id}] 邮箱缺少分隔符，跳过: {email_str}")

            logger.info(f"[{self.group_id}] 成功保存邮箱到TXT文件: {file_path}")

        except Exception as e:
            logger.error(f"[{self.group_id}] 保存邮箱到TXT文件时出错: {e}")

    def delayed_order_check(self, order_id, initial_email_list):
        """延迟30秒后再次检查订单详情，获取可能延迟发货的邮箱"""
        try:
            logger.info(f"[{self.group_id}] 启动延迟检查线程，30秒后再次获取订单 {order_id} 详情")
            time.sleep(30)  # 等待30秒

            logger.info(f"[{self.group_id}] 开始延迟检查订单 {order_id} 详情")
            delayed_email_list = self.get_order_details(order_id)

            if delayed_email_list:
                # 比较两次获取的邮箱列表，找出新增的邮箱
                initial_emails = set(initial_email_list) if initial_email_list else set()
                delayed_emails = set(delayed_email_list)
                new_emails = delayed_emails - initial_emails

                if new_emails:
                    logger.info(f"[{self.group_id}] 延迟检查发现 {len(new_emails)} 个新邮箱")
                    # 将新邮箱保存到TXT文件
                    self.save_emails_to_txt_file(list(new_emails))
                else:
                    logger.info(f"[{self.group_id}] 延迟检查未发现新邮箱")
            else:
                logger.warning(f"[{self.group_id}] 延迟检查订单详情失败")

        except Exception as e:
            logger.error(f"[{self.group_id}] 延迟检查订单详情时出错: {e}")

    def get_order_details(self, order_id):
        """获取订单详情，循环查询直到有数据或超时，返回邮箱列表"""
        max_attempts = 10
        retry_delay = 3

        for attempt in range(max_attempts):
            try:
                logger.info(f"[{self.group_id}] 查询订单详情 (尝试 {attempt + 1}/{max_attempts})")

                order_url = f"https://www.humkt.com/api/order?id={order_id}&token=qUunKMEIzCeAXLB6YOfAfdyyRBgesJ1Q"
                response = requests.get(order_url, timeout=20, verify=False)

                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"[{self.group_id}] 订单详情接口完整响应数据:")
                    logger.info(f"[{self.group_id}] 响应状态码: {response.status_code}")
                    logger.info(f"[{self.group_id}] 响应头: {dict(response.headers)}")
                    logger.info(f"[{self.group_id}] 完整JSON响应: {result}")
                    logger.info(f"[{self.group_id}] 响应数据类型: {type(result)}")

                    if result.get("code") == 1 and result.get("data"):
                        data = result["data"]
                        logger.info(f"[{self.group_id}] data字段内容: {data}")
                        logger.info(f"[{self.group_id}] data字段类型: {type(data)}")

                        if isinstance(data, list) and len(data) > 0:
                            logger.info(f"[{self.group_id}] 成功获取订单详情，共 {len(data)} 个邮箱")
                            for i, email_item in enumerate(data):
                                logger.info(f"[{self.group_id}] 邮箱 {i+1}: {email_item} (类型: {type(email_item)})")
                            return data  # 返回整个邮箱列表
                        else:
                            logger.info(f"[{self.group_id}] 订单详情暂无数据，data为空或非列表，{retry_delay}秒后重试")
                            logger.info(f"[{self.group_id}] data内容: {data}, 类型: {type(data)}")
                    else:
                        logger.warning(f"[{self.group_id}] 订单详情查询返回失败")
                        logger.warning(f"[{self.group_id}] code字段: {result.get('code')}")
                        logger.warning(f"[{self.group_id}] message字段: {result.get('message')}")
                        logger.warning(f"[{self.group_id}] data字段: {result.get('data')}")
                else:
                    logger.error(f"[{self.group_id}] 订单详情查询请求失败: {response.status_code}")
                    logger.error(f"[{self.group_id}] 响应内容: {response.text}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_attempts - 1:
                    time.sleep(retry_delay)

            except Exception as e:
                logger.error(f"[{self.group_id}] 查询订单详情异常: {e}")
                if attempt < max_attempts - 1:
                    time.sleep(retry_delay)

        logger.error(f"[{self.group_id}] 订单详情查询失败，已达到最大重试次数")
        return None

    def write_failed_email(self, email_data, reason):
        """将失败的邮箱账号写入文件"""
        try:
            with open("gmail_humkt_fail.txt", "a", encoding="utf-8") as f:
                # 如果有邮箱和密码，只记录邮箱和密码
                if self.email and self.email_password:
                    f.write(f"{self.email}|{self.email_password}\n")
                elif "|" in str(email_data):
                    parts = str(email_data).split("|")
                    if len(parts) >= 2:
                        email, password = parts[0], parts[1]
                        f.write(f"{email}|{password}\n")
                    else:
                        f.write(f"{email_data}\n")
                else:
                    f.write(f"{email_data}\n")
            logger.info(f"[{self.group_id}] 已记录失败邮箱: {email_data}")
        except Exception as e:
            logger.error(f"[{self.group_id}] 写入失败邮箱文件时出错: {e}")
    
    def get_new_proxy(self, max_duration_minutes=5):
        """从API获取新的代理IP，支持重试机制和备用API"""
        logger.info(f"[{self.group_id}] 正在获取新代理...")

        # 首先尝试主要API（重试3次）
        if self._try_get_proxy_from_main_api():
            return True

        # 主要API失败后，尝试备用API（查询正在使用的代理）
        logger.warning(f"[{self.group_id}] 主要API获取代理失败，尝试备用API...")
        if self._try_get_proxy_from_backup_api():
            return True

        # 如果备用API也失败，使用原来的循环获取逻辑作为最后手段
        logger.warning(f"[{self.group_id}] 备用API也失败，使用循环获取作为最后手段...")
        return self._try_get_proxy_with_loop(max_duration_minutes)

    def _try_get_proxy_from_main_api(self):
        """尝试从主要API获取代理，重试3次"""
        for attempt in range(1, PROXY_RETRY_TIMES + 1):
            try:
                logger.info(f"[{self.group_id}] 主要API第 {attempt}/{PROXY_RETRY_TIMES} 次尝试...")
                response = requests.get(PROXY_API_URL, timeout=10, verify=False)

                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                        proxy_info = data["data"][0]
                        server = proxy_info.get("server", "")
                        if ":" in server:
                            self.proxy_host, self.proxy_port = server.split(":")
                            self.proxy_start_time = time.time()
                            logger.info(f"[{self.group_id}] 主要API成功获取代理: {self.proxy_host}:{self.proxy_port}")
                            return True
                        else:
                            logger.error(f"[{self.group_id}] 代理服务器格式错误: {server}")
                    else:
                        logger.error(f"[{self.group_id}] 主要API返回失败: {data}")
                else:
                    logger.error(f"[{self.group_id}] 主要API请求失败: {response.status_code}")

            except Exception as e:
                logger.error(f"[{self.group_id}] 主要API请求异常: {e}")

            # 如果不是最后一次尝试，等待3秒
            if attempt < PROXY_RETRY_TIMES:
                logger.info(f"[{self.group_id}] {PROXY_RETRY_INTERVAL}秒后重试主要API...")
                time.sleep(PROXY_RETRY_INTERVAL)

        return False

    def _try_get_proxy_from_backup_api(self):
        """尝试从备用API获取正在使用的代理"""
        try:
            logger.info(f"[{self.group_id}] 尝试备用API获取正在使用的代理...")
            response = requests.get(BACKUP_PROXY_API_URL, timeout=10, verify=False)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                    # 选择第一个可用的代理
                    proxy_info = data["data"][0]
                    server = proxy_info.get("server", "")
                    if ":" in server:
                        self.proxy_host, self.proxy_port = server.split(":")
                        self.proxy_start_time = time.time()
                        deadline = proxy_info.get("deadline", "未知")
                        logger.info(f"[{self.group_id}] 备用API成功获取代理: {self.proxy_host}:{self.proxy_port} (到期时间: {deadline})")
                        return True
                    else:
                        logger.error(f"[{self.group_id}] 备用API代理格式错误: {server}")
                else:
                    logger.error(f"[{self.group_id}] 备用API返回失败: {data}")
            else:
                logger.error(f"[{self.group_id}] 备用API请求失败: {response.status_code}")

        except Exception as e:
            logger.error(f"[{self.group_id}] 备用API请求异常: {e}")

        return False

    def _try_get_proxy_with_loop(self, max_duration_minutes):
        """使用循环获取代理作为最后手段"""
        start_time = time.time()
        attempt = 1

        while True:
            # 检查是否超时
            elapsed_minutes = (time.time() - start_time) / 60
            if elapsed_minutes >= max_duration_minutes:
                logger.error(f"[{self.group_id}] 循环获取代理超时，已尝试 {max_duration_minutes} 分钟，共 {attempt-1} 次尝试")
                return False

            try:
                logger.info(f"[{self.group_id}] 循环第 {attempt} 次尝试获取代理...")
                response = requests.get(PROXY_API_URL, timeout=10, verify=False)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == "SUCCESS" and data.get("data") and len(data["data"]) > 0:
                        proxy_info = data["data"][0]
                        server = proxy_info.get("server", "")
                        if ":" in server:
                            self.proxy_host, self.proxy_port = server.split(":")
                            self.proxy_start_time = time.time()
                            logger.info(f"[{self.group_id}] 循环获取成功: {self.proxy_host}:{self.proxy_port} (尝试 {attempt} 次)")
                            return True
                        else:
                            logger.error(f"[{self.group_id}] 循环获取代理格式错误: {server}")
                    else:
                        logger.error(f"[{self.group_id}] 循环获取失败: {data}")
                else:
                    logger.error(f"[{self.group_id}] 循环获取请求失败: {response.status_code}")

            except Exception as e:
                logger.error(f"[{self.group_id}] 循环获取异常: {e}")

            # 等待5秒后重试
            time.sleep(5)
            attempt += 1

    def test_proxy(self, max_attempts=3):
        """测试当前代理是否可用，如果不可用则重新获取，直到可用或达到最大尝试次数"""
        # 如果没有代理信息，直接返回False
        if not self.proxy_host or not self.proxy_port:
            logger.warning(f"[{self.group_id}] 代理信息不完整，跳过代理测试")
            return False

        # 首先检查代理是否过期
        if self.is_proxy_expired():
            logger.info(f"[{self.group_id}] 当前代理已过期，尝试获取新代理")
            if self.get_new_proxy():
                logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.error(f"[{self.group_id}] 获取新代理失败")
                return False

        for attempt in range(1, max_attempts + 1):
            try:
                # 使用更简单的测试URL，避免某些网站的反爬机制
                test_url = "http://httpbin.org/ip"
                test_proxies = {
                    "http": f"http://{self.proxy_host}:{self.proxy_port}",
                    "https": f"http://{self.proxy_host}:{self.proxy_port}"
                }

                # 添加更多的请求头，模拟正常浏览器
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }

                response = requests.get(test_url, proxies=test_proxies, timeout=10, headers=headers)
                if response.status_code == 200:
                    logger.info(f"[{self.group_id}] 代理测试成功")
                    return True
                else:
                    logger.error(f"[{self.group_id}] 代理测试失败: {response.status_code}")
            except requests.exceptions.ProxyError as e:
                logger.error(f"[{self.group_id}] 代理认证或连接错误: {e}")
                # 代理认证错误，强制获取新代理
                logger.info(f"[{self.group_id}] 检测到代理认证问题，强制获取新代理")
                break
            except Exception as e:
                logger.error(f"[{self.group_id}] 代理测试异常: {e}")

            # 如果测试失败且未达到最大尝试次数，则重新获取代理
            if attempt < max_attempts:
                logger.info(f"[{self.group_id}] 尝试获取新代理 (尝试 {attempt}/{max_attempts})")
                if self.get_new_proxy():
                    logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
                    continue
                else:
                    logger.error(f"[{self.group_id}] 获取新代理失败")

        logger.error(f"[{self.group_id}] 多次尝试后未能获取可用代理")
        return False

    def reset_proxy_state(self):
        """重置代理状态，清理可能的认证问题"""
        try:
            logger.info(f"[{self.group_id}] 重置代理状态")

            # 清理当前代理信息
            self.proxy_host = None
            self.proxy_port = None
            self.proxy_start_time = None

            # 等待一小段时间，让之前的连接完全关闭
            time.sleep(2)

            # 强制获取新代理，最多尝试3次
            for attempt in range(3):
                logger.info(f"[{self.group_id}] 尝试获取新代理 (第{attempt + 1}次)")
                if self.get_new_proxy():
                    logger.info(f"[{self.group_id}] 代理状态重置成功，新代理: {self.proxy_host}:{self.proxy_port}")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 第{attempt + 1}次获取代理失败")
                    if attempt < 2:  # 不是最后一次尝试
                        time.sleep(3)  # 等待3秒后重试

            logger.error(f"[{self.group_id}] 代理状态重置失败，已尝试3次")
            return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 重置代理状态异常: {e}")
            return False
    def setup_browser(self, use_proxy=False):
        """设置浏览器，优化启动速度"""
        logger.info(f"[{self.group_id}] 开始设置浏览器...")

        # 先尝试删除已存在的目录
        try:
            if os.path.exists(self.profile_path):
                import shutil
                shutil.rmtree(self.profile_path)
                logger.info(f"[{self.group_id}] 已删除旧的浏览器配置文件夹: {self.profile_path}")
        except Exception as e:
            logger.warning(f"[{self.group_id}] 删除旧的浏览器配置文件夹时出错: {e}")

        # 创建新的目录
        os.makedirs(self.profile_path, exist_ok=True)

        try:
            # 记录启动前的浏览器进程
            self.before_pids = self.get_browser_processes()

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 设置用户数据目录
            co.set_user_data_path(self.profile_path)

            # 优化浏览器启动参数 - 提高性能，减少资源使用
            co.set_argument("--incognito")  # 隐身模式
            co.set_argument("--disable-extensions")  # 禁用扩展
            co.set_argument("--disable-gpu")  # 禁用GPU加速
            co.set_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
            co.set_argument("--disable-infobars")  # 禁用信息栏
            co.set_argument("--disable-browser-side-navigation")  # 禁用浏览器侧导航
            co.set_argument("--disable-features=TranslateUI,BlinkGenPropertyTrees")  # 禁用翻译UI等
            co.set_argument("--disable-notifications")  # 禁用通知
            co.set_argument("--disable-popup-blocking")  # 禁用弹窗阻止
            co.set_argument("--disable-background-timer-throttling")  # 禁用后台计时器限制
            co.set_argument("--disable-backgrounding-occluded-windows")  # 禁用背景窗口
            co.set_argument("--disable-breakpad")  # 禁用崩溃报告
            co.set_argument("--disable-client-side-phishing-detection")  # 禁用钓鱼检测
            co.set_argument("--disable-default-apps")  # 禁用默认应用
            co.set_argument("--disable-hang-monitor")  # 禁用挂起监视器
            co.set_argument("--disable-prompt-on-repost")  # 禁用重新发布提示
            co.set_argument("--disable-sync")  # 禁用同步
            co.set_argument("--no-first-run")  # 禁止首次运行
            co.set_argument("--no-default-browser-check")  # 禁止默认浏览器检查
            co.set_argument("--disable-webrtc")  # 禁用WebRTC
            co.set_argument("--enforce-webrtc-ip-permission-check")  # 强制WebRTC IP权限检查

            # 设置代理（如果启用）
            if use_proxy and USE_PROXY and self.proxy_host and self.proxy_port:
                co.set_proxy(f"{self.proxy_host}:{self.proxy_port}")
                logger.info(f"[{self.group_id}] 已设置浏览器代理: {self.proxy_host}:{self.proxy_port}")
                # 注意：不在这里测试代理，避免启动时的额外延迟和错误
            else:
                logger.info(f"[{self.group_id}] 当前不使用代理")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动浏览器
            start_time = time.time()
            logger.info(f"[{self.group_id}] 启动浏览器")
            self.page = ChromiumPage(co)

            # 等待浏览器准备就绪
            time.sleep(1)

            # 显示启动用时
            elapsed = time.time() - start_time
            logger.info(f"[{self.group_id}] 浏览器启动完成，用时 {elapsed:.2f} 秒")

            # 记录新增的浏览器进程
            self.track_browser_processes()

            return True

        except Exception as e:
            logger.exception(f"[{self.group_id}] 设置浏览器时出现异常: {e}")
            return False

    def get_default_browser_path(self):
        """获取默认浏览器路径"""
        # 简化示例，实际使用时可以依据不同操作系统获取更精确的路径
        if sys.platform == "win32":
            return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        elif sys.platform == "darwin":
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            return "/usr/bin/google-chrome"

    def get_browser_processes(self):
        """获取当前浏览器进程ID列表"""
        try:
            browser_process_names = ['chrome', 'chromium', 'msedge', 'edge', 'brave']
            return [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in browser_process_names)]
        except:
            return []

    def track_browser_processes(self):
        """跟踪新启动的浏览器进程（只跟踪主浏览器，不跟踪异步浏览器）"""
        global _chrome_process_ids
        try:
            after_pids = self.get_browser_processes()
            new_pids = [pid for pid in after_pids if pid not in self.before_pids]

            # 只添加主浏览器进程到全局列表，不添加异步浏览器进程
            main_browser_pids = []
            for pid in new_pids:
                try:
                    import psutil
                    process = psutil.Process(pid)
                    cmdline = ' '.join(process.cmdline())

                    # 检查是否是异步浏览器进程
                    is_async_browser = (
                        'chrome-data-async-card' in cmdline or
                        'async_' in cmdline or
                        any(f'--remote-debugging-port={port}' in cmdline for port in range(20000, 25000))
                    )

                    if not is_async_browser:
                        main_browser_pids.append(pid)
                    else:
                        logger.info(f"[{self.group_id}] 跳过异步浏览器进程跟踪 PID: {pid}")

                except Exception as e:
                    # 如果无法检查进程，保守地添加到主浏览器列表
                    main_browser_pids.append(pid)

            _chrome_process_ids.extend(main_browser_pids)

            if new_pids:
                logger.info(f"[{self.group_id}] 跟踪到 {len(new_pids)} 个新浏览器进程，其中 {len(main_browser_pids)} 个主浏览器进程")
            else:
                logger.warning(f"[{self.group_id}] 未检测到新的浏览器进程")
        except Exception as e:
            logger.error(f"[{self.group_id}] 跟踪进程失败: {e}")

    def cleanup(self):
        """清理资源，更彻底地清理浏览器"""
        try:
            # 关闭浏览器
            if self.page:
                # 先清除浏览器缓存
                try:
                    self.page.run_cdp('Network.clearBrowserCache', {})
                    self.page.run_cdp('Network.clearBrowserCookies', {})
                    self.page.run_cdp('Storage.clearDataForOrigin', {'origin': '*', 'storageTypes': 'all'})
                    logger.info(f"[{self.group_id}] 已清除浏览器缓存和Cookie")
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 清除缓存失败: {e}")

                # 关闭浏览器
                self.page.quit()
                time.sleep(1)

            # 强制结束可能残留的进程（只清理主浏览器相关进程，避免影响异步浏览器）
            try:
                import psutil
                current_processes = []
                try:
                    current_processes = [p for p in psutil.process_iter() if 'chrome' in p.name().lower()]
                except:
                    pass

                for process in current_processes:
                    try:
                        pid = process.pid
                        if pid in self.before_pids:
                            continue  # 跳过启动前已存在的进程

                        # 检查进程命令行，只清理属于主浏览器的进程
                        cmdline = ' '.join(process.cmdline())

                        # 检查是否是异步浏览器进程（更精确的检测）
                        is_async_browser = (
                            'chrome-data-async-card' in cmdline or
                            'async_' in cmdline or
                            any(f'--remote-debugging-port={port}' in cmdline for port in range(20000, 25000)) or
                            'ASYNC-' in cmdline  # 检查是否包含异步标识
                        )

                        if is_async_browser:
                            # 跳过异步浏览器进程，不清理
                            logger.info(f"[{self.group_id}] 跳过异步浏览器进程 PID: {pid} (命令行: {cmdline[:100]}...)")
                            continue

                        # 如果进程命令行包含主浏览器的用户数据目录，则清理
                        if self.profile_path in cmdline:
                            # 再次确认不是异步浏览器（双重保护）
                            if 'async' not in cmdline.lower() and 'chrome-data-async-card' not in cmdline:
                                if sys.platform == "win32":
                                    os.system(f'taskkill /F /PID {pid} /T >nul 2>&1')
                                else:
                                    try:
                                        os.kill(pid, signal.SIGKILL)
                                    except:
                                        pass
                                logger.info(f"[{self.group_id}] 清理主浏览器进程 PID: {pid}")
                            else:
                                logger.info(f"[{self.group_id}] 跳过可能的异步浏览器进程 PID: {pid} (双重保护)")
                        else:
                            # 对于其他Chrome进程，非常保守地处理
                            # 只清理明确属于主浏览器且不是异步浏览器的进程
                            main_group_identifier = f"chrome-data-cursor/group_{self.group_id}"
                            if main_group_identifier in cmdline and 'async' not in cmdline.lower():
                                if sys.platform == "win32":
                                    os.system(f'taskkill /F /PID {pid} /T >nul 2>&1')
                                else:
                                    try:
                                        os.kill(pid, signal.SIGKILL)
                                    except:
                                        pass
                                logger.info(f"[{self.group_id}] 清理主浏览器相关进程 PID: {pid}")
                            else:
                                # 其他进程不清理，避免误杀
                                logger.debug(f"[{self.group_id}] 跳过其他Chrome进程 PID: {pid}")
                    except Exception as e:
                        logger.warning(f"[{self.group_id}] 处理进程 {pid} 时出错: {e}")

                logger.info(f"[{self.group_id}] 已尝试强制结束主浏览器残留进程（保护异步浏览器）")
            except Exception as e:
                logger.warning(f"[{self.group_id}] 结束残留进程出错: {e}")

            # 删除配置文件目录
            try:
                import shutil
                if os.path.exists(self.profile_path):
                    shutil.rmtree(self.profile_path)
                    logger.info(f"[{self.group_id}] 已删除浏览器配置目录")
                os.makedirs(self.profile_path, exist_ok=True)
            except Exception as e:
                logger.error(f"[{self.group_id}] 删除配置目录时出错: {e}")

            logger.info(f"[{self.group_id}] 资源清理完成")
        except Exception as e:
            logger.error(f"[{self.group_id}] 清理资源时出错: {e}")

    def should_restart_browser(self):
        """检查是否需要重启浏览器（防止资源累积）"""
        return self.registration_count > 0 and self.registration_count % BROWSER_RESTART_INTERVAL == 0

    def restart_browser_if_needed(self):
        """如果需要，重启浏览器以释放资源"""
        if self.should_restart_browser():
            logger.info(f"[{self.group_id}] 已完成 {self.registration_count} 次注册，重启浏览器释放资源")
            try:
                # 清理当前浏览器
                self.cleanup()
                time.sleep(2)

                # 重新启动浏览器
                restart_with_proxy = USE_PROXY and USE_PROXY_FULL_PROCESS
                if self.setup_browser(use_proxy=restart_with_proxy):
                    proxy_status = "代理模式" if restart_with_proxy else "无代理模式"
                    logger.info(f"[{self.group_id}] 浏览器重启成功（{proxy_status}）")
                    return True
                else:
                    logger.error(f"[{self.group_id}] 浏览器重启失败")
                    return False
            except Exception as e:
                logger.error(f"[{self.group_id}] 重启浏览器时出错: {e}")
                return False
        return True

    def handle_turnstile(self):
        """处理 Turnstile 验证"""
        logger.info(f"[{self.group_id}] 开始处理 Turnstile 人机验证...")

        # 等待turnstile元素出现
        turnstile = self.page.ele("@id=cf-turnstile", timeout=5)
        if not turnstile:
            logger.error(f"[{self.group_id}] 未找到 cf-turnstile 元素")
            return False

        try:
            # 将turnstile元素滚动到视图中并点击以获取焦点
            self.page.run_js("arguments[0].scrollIntoView(true);", turnstile)
            turnstile.click()
            logger.info(f"[{self.group_id}] 已点击Turnstile容器获取焦点")
            time.sleep(1)

            # 发送Tab键聚焦到复选框，然后发送空格键进行点击
            self.page.actions.key_down('TAB')
            self.page.actions.key_up('TAB')
            time.sleep(1)
            self.page.actions.key_down('space')
            self.page.actions.key_up('space')
            logger.info(f"[{self.group_id}] 已发送Tab和Space键")
            time.sleep(2)

            # 检查验证结果
            if self.check_verification_success():
                logger.info(f"[{self.group_id}] Turnstile 验证通过")
                return True

            logger.warning(f"[{self.group_id}] Turnstile 验证未通过")
            return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 处理验证时出错: {e}")
            return False

    def check_verification_success(self):
        """检查验证是否成功"""
        try:
            # 检查是否存在后续表单元素
            if (self.page.ele("@name=password", timeout=0.5) or
                self.page.ele("@name=email", timeout=0.5) or
                self.page.ele("@data-index=0", timeout=0.5) or
                self.page.ele("Account Settings", timeout=0.5)):
                return True

            # 检查是否有错误信息
            error_xpaths = [
                'xpath://div[contains(text(), "Can\'t verify the user is human")]',
                'xpath://div[contains(text(), "Error: 600010")]',
                'xpath://div[contains(text(), "Please try again")]'
            ]

            for xpath in error_xpaths:
                if self.page.ele(xpath):
                    return False

            return False
        except:
            return False

    def input_verification_code(self, code):
        """输入验证码"""
        if not code or len(code) != 6:
            logger.error(f"[{self.group_id}] 无效的验证码: {code}")
            return False

        try:
            # 查找验证码输入框
            for i, digit in enumerate(code):
                input_ele = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='" + str(i) + "']")
                if input_ele:
                    input_ele.input(digit)
                    time.sleep(0.2)
                else:
                    logger.error(f"[{self.group_id}] 未找到第{i}个验证码输入框")
                    return False

            logger.info(f"[{self.group_id}] 验证码输入成功")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 输入验证码出错: {e}")
            return False

    def get_email_code(self, email):
        """通过自动登录Gmail获取验证码"""
        gmail_page = None
        try:
            logger.info(f"[{self.group_id}] 开始自动登录Gmail获取验证码")

            # 创建新的浏览器实例用于Gmail登录
            gmail_page = self.create_gmail_browser()
            if not gmail_page:
                logger.error(f"[{self.group_id}] 创建Gmail浏览器失败")
                return None

            # 第一步：打开Google账号登录页面
            logger.info(f"[{self.group_id}] 打开Google账号登录页面")
            gmail_page.get("https://accounts.google.com/")
            time.sleep(3)

            # 第二步：输入邮箱
            logger.info(f"[{self.group_id}] 输入邮箱: {email}")
            email_input = gmail_page.ele("xpath://*[@type='email']", timeout=10)
            if not email_input:
                logger.error(f"[{self.group_id}] 未找到邮箱输入框")
                return None

            email_input.input(email)
            time.sleep(1)

            # 点击下一步按钮
            next_button = gmail_page.ele("xpath://*[@id='identifierNext']//button", timeout=10)
            if not next_button:
                logger.error(f"[{self.group_id}] 未找到邮箱下一步按钮")
                return None

            next_button.click()
            time.sleep(3)

            # 第三步：输入密码
            logger.info(f"[{self.group_id}] 输入邮箱密码")
            password_input = gmail_page.ele("xpath://*[@type='password']", timeout=10)
            if not password_input:
                logger.error(f"[{self.group_id}] 未找到密码输入框")
                return None

            password_input.input(self.email_password)
            time.sleep(1)

            # 点击密码下一步按钮
            password_next_button = gmail_page.ele("xpath://*[@id='passwordNext']//button", timeout=10)
            if not password_next_button:
                logger.error(f"[{self.group_id}] 未找到密码下一步按钮")
                return None

            password_next_button.click()
            time.sleep(5)

            # 处理中转页面的确认按钮
            logger.info(f"[{self.group_id}] 检查是否有中转页面确认按钮")
            try:
                # 查找确认按钮（使用ID选择器）
                confirm_button = gmail_page.ele("#confirm", timeout=5)
                if confirm_button:
                    logger.info(f"[{self.group_id}] 找到中转页面确认按钮，点击确认")
                    confirm_button.click()
                    time.sleep(5)
                else:
                    logger.info(f"[{self.group_id}] 未找到中转页面确认按钮，可能已跳过")
            except Exception as e:
                logger.info(f"[{self.group_id}] 中转页面确认按钮处理异常（可能不存在）: {e}")

            # 第四步：打开Gmail收件箱
            logger.info(f"[{self.group_id}] 打开Gmail收件箱")
            gmail_page.get("https://mail.google.com/mail/u/0/#inbox")
            time.sleep(5)

            # 循环查找验证码邮件
            logger.info(f"[{self.group_id}] 开始查找Cursor验证码邮件")
            verification_code = None

            for attempt in range(3):  # 最多尝试50次，每次间隔3秒
                try:
                    # 刷新页面
                    gmail_page.refresh()
                    time.sleep(3)

                    # 查找包含验证码的邮件
                    code_element = gmail_page.ele("xpath://span[contains(text(), 'Your one-time code is')]", timeout=2)
                    if code_element:
                        logger.info(f"[{self.group_id}] 找到验证码邮件")
                        # 点击邮件
                        code_element.click()
                        time.sleep(2)

                        # 解析验证码
                        email_text = code_element.text
                        logger.info(f"[{self.group_id}] 邮件内容: {email_text}")

                        # 根据"Your one-time code is "拆分并提取验证码
                        if "Your one-time code is " in email_text:
                            parts = email_text.split("Your one-time code is ")
                            if len(parts) > 1:
                                # 获取后面的部分，取前6位数字
                                code_part = parts[1].strip()
                                verification_code = code_part[:6]
                                if verification_code.isdigit() and len(verification_code) == 6:
                                    logger.info(f"[{self.group_id}] 成功提取验证码: {verification_code}")
                                    break
                                else:
                                    logger.warning(f"[{self.group_id}] 提取的验证码格式不正确: {verification_code}")

                    logger.info(f"[{self.group_id}] 第{attempt + 1}次查找，未找到验证码邮件，3秒后重试")
                    time.sleep(3)

                except Exception as e:
                    logger.warning(f"[{self.group_id}] 查找验证码时出现异常: {e}")
                    time.sleep(3)

            if verification_code:
                logger.info(f"[{self.group_id}] 成功获取验证码: {verification_code}")
                return verification_code
            else:
                logger.error(f"[{self.group_id}] 未能获取到验证码")
                return None

        except Exception as e:
            logger.error(f"[{self.group_id}] 获取Gmail验证码时出错: {e}")
            return None
        finally:
            # 确保Gmail浏览器进程被关闭
            if gmail_page:
                try:
                    logger.info(f"[{self.group_id}] 正在关闭Gmail浏览器进程")
                    gmail_page.quit()
                    time.sleep(1)  # 等待进程完全关闭
                    logger.info(f"[{self.group_id}] Gmail浏览器进程已关闭")
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 关闭Gmail浏览器时出错: {e}")
                    # 如果正常关闭失败，尝试强制关闭相关进程
                    try:
                        self.force_close_gmail_processes()
                    except Exception as force_e:
                        logger.error(f"[{self.group_id}] 强制关闭Gmail进程也失败: {force_e}")

    def create_gmail_browser(self):
        """创建用于Gmail登录的浏览器实例"""
        try:
            logger.info(f"[{self.group_id}] 创建Gmail浏览器实例（无痕模式）")

            # 设置Gmail浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 使用临时用户数据目录
            gmail_profile_path = f"chrome-data-gmail/group_{self.group_id}"
            os.makedirs(gmail_profile_path, exist_ok=True)
            co.set_user_data_path(gmail_profile_path)

            # 设置无痕模式
            co.set_argument("--incognito")
            logger.info(f"[{self.group_id}] 已启用Gmail浏览器无痕模式")

            # 基本浏览器参数
            co.set_argument("--disable-extensions")
            co.set_argument("--disable-gpu")
            co.set_argument("--disable-dev-shm-usage")
            co.set_argument("--disable-infobars")
            co.set_argument("--disable-notifications")
            co.set_argument("--no-first-run")
            co.set_argument("--no-default-browser-check")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动Gmail浏览器
            gmail_page = ChromiumPage(co)
            time.sleep(1)

            logger.info(f"[{self.group_id}] Gmail浏览器创建成功（无痕模式）")
            return gmail_page

        except Exception as e:
            logger.error(f"[{self.group_id}] 创建Gmail浏览器失败: {e}")
            return None

    def force_close_gmail_processes(self):
        """强制关闭Gmail相关的浏览器进程"""
        try:
            logger.info(f"[{self.group_id}] 尝试强制关闭Gmail相关进程")

            # 获取当前所有浏览器进程
            current_processes = self.get_browser_processes()

            # 强制结束进程
            for pid in current_processes:
                try:
                    if sys.platform == "win32":
                        os.system(f'taskkill /F /PID {pid} /T >nul 2>&1')
                    else:
                        os.kill(pid, signal.SIGKILL)
                    logger.info(f"[{self.group_id}] 已强制关闭进程 PID: {pid}")
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 无法关闭进程 PID {pid}: {e}")

            time.sleep(2)  # 等待进程完全关闭
            logger.info(f"[{self.group_id}] Gmail进程强制关闭完成")

        except Exception as e:
            logger.error(f"[{self.group_id}] 强制关闭Gmail进程时出错: {e}")

    def get_token(self):
        """从cookies中提取WorkosCursorSessionToken"""
        try:
            cookies = self.page.cookies()
            for cookie in cookies:
                if cookie.get('name') == 'WorkosCursorSessionToken':
                    token = cookie.get('value')
                    # if token and '::' in token:
                    #     return token.split('::')[1]
                    return token
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取token出错: {e}")
            return None

    def submit_token(self, email, token, access_token=None, refresh_token=None):
        """将token提交到API（异步方式）"""
        # 创建异步线程执行提交操作
        submit_thread = threading.Thread(
            target=self._async_submit_token,
            args=(email, token, access_token, refresh_token),
            daemon=True
        )
        submit_thread.start()
        # 立即返回，不等待提交完成
        return True

    def _async_submit_token(self, email, token, access_token=None, refresh_token=None):
        """异步执行token提交和日志记录"""
        try:
            # 准备请求参数
            submit_data = {
                "email": email,
                "token": token
            }

            # 添加accessToken和refreshToken（如果有）
            if access_token:
                submit_data["accessToken"] = access_token
            if refresh_token:
                submit_data["refreshToken"] = refresh_token

            request_kwargs = {
                "json": submit_data,
                "timeout": 20
            }

            # 添加代理配置（如果启用）
            # if USE_PROXY:
            #     request_kwargs["proxies"] = {
            #         "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
            #         "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
            #     }

            response = requests.post(f"{API_BASE_URL}{TOKEN_SUBMIT_ENDPOINT}", **request_kwargs)

            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info(f"[{self.group_id}] 成功提交{email}的token和tokens (异步)")
                    if self.success_callback:
                        self.success_callback(self.group_id, email, token)
                    return

            logger.error(f"[{self.group_id}] 提交token失败: {response.status_code} - {response.text}")
            if self.error_callback:
                self.error_callback(self.group_id, email, "提交token失败")
        except Exception as e:
            logger.error(f"[{self.group_id}] 提交token时出错: {e}")
            if self.error_callback:
                self.error_callback(self.group_id, email, f"提交token异常: {str(e)}")

    def process_registration(self):
        """处理注册流程"""
        if self.current_attempt >= self.max_attempts:
            logger.warning(f"[{self.group_id}] 达到最大尝试次数 {self.max_attempts}，终止当前注册")
            # 记录失败的邮箱账号
            if self.email:
                self.write_failed_email(self.email, f"达到最大尝试次数 {self.max_attempts}")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, f"达到最大尝试次数 {self.max_attempts}")

            # 根据代理配置决定如何重置浏览器
            if USE_PROXY and USE_PROXY_FULL_PROCESS:
                # 全程代理模式：保持浏览器开启，只重置尝试次数
                logger.info(f"[{self.group_id}] 全程代理模式，保持浏览器状态，重置尝试次数")
                self.current_attempt = 0
            else:
                # 其他模式：重置浏览器以便下次注册
                try:
                    if self.page:
                        self.page.quit()
                        time.sleep(2)
                    self.setup_browser(use_proxy=False)
                    logger.info(f"[{self.group_id}] 尝试失败后已重置浏览器为无代理模式")
                except Exception as e:
                    logger.error(f"[{self.group_id}] 重置浏览器失败: {e}")
            return False

        self.current_attempt += 1
        logger.info(f"[{self.group_id}] 执行注册尝试 {self.current_attempt}/{self.max_attempts}")

        # 等待页面加载
        time.sleep(3)
        has_overlay = bool(self.page.ele('xpath://div[@inert and @aria-hidden="true" and not(@class)]',timeout=1))
        # 1. 查找并点击magic-code按钮

        if not has_overlay:
            magic_code_button = self.page.ele("xpath://button[@name='intent' and @type='submit' and @value='magic-code' and not(@data-disabled='true')]",timeout=1)
            if bool(magic_code_button):
                logger.info(f"[{self.group_id}] 找到magic-code按钮，点击")
                try:
                    magic_code_button.click()
                except ElementLostError:
                    logger.info(f"[{self.group_id}] 点击按钮后元素失效，属正常跳转，已忽略")
                time.sleep(3)

        # 2. 检查是否存在人机验证
        turnstile = self.page.ele("@id=cf-turnstile", timeout=1)
        if has_overlay :
            if bool(turnstile):
                logger.info(f"[{self.group_id}] 检测到人机验证")
                self.handle_turnstile()
                time.sleep(3)

        # 3. 检查是否存在验证码输入框
        code_input = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='0']", timeout=1)
        if bool(code_input):
            logger.info(f"[{self.group_id}] 检测到验证码输入框，尝试获取验证码")
            self.process_verification_code()
            return True

        # 等待一段时间后继续尝试
        time.sleep(2)
        return self.process_registration()

    def process_verification_code(self, attempt=0):
        """处理验证码获取和输入"""
        if attempt >= 30:
            logger.error(f"[{self.group_id}] 验证码获取失败，达到最大尝试次数")
            # 记录失败的邮箱账号
            if self.email:
                self.write_failed_email(self.email, "验证码获取失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "验证码获取失败")
            return

        code = self.get_email_code(self.email)

        if not code:
            logger.info(f"[{self.group_id}] 尚未获取到验证码，3秒后重试 ({attempt + 1}/30)")
            time.sleep(3)
            self.process_verification_code(attempt + 1)
            return

        logger.info(f"[{self.group_id}] 获取到验证码: {code}")

        # 根据代理配置决定处理方式
        if (USE_PROXY and USE_PROXY_FULL_PROCESS) or not USE_PROXY:
            # 全程使用代理模式或无代理模式：直接输入验证码，不重启浏览器
            if USE_PROXY and USE_PROXY_FULL_PROCESS:
                logger.info(f"[{self.group_id}] 全程代理模式，直接输入验证码，不重启浏览器")
            else:
                logger.info(f"[{self.group_id}] 无代理模式，直接输入验证码，不重启浏览器")

            # 直接输入验证码
            if not self.input_verification_code(code):
                logger.error(f"[{self.group_id}] 验证码输入失败")
                # 记录失败的邮箱账号
                if self.email:
                    self.write_failed_email(self.email, "验证码输入失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "验证码输入失败")
                return

            # 等待页面加载完成
            time.sleep(5)

            # 检查当前页面状态，判断下一步操作
            current_url = self.page.url
            logger.info(f"[{self.group_id}] 验证码输入后当前URL: {current_url}")

            # 检查是否有Continue按钮（需要绑定信用卡）
            continue_button = self.page.ele("xpath://button[contains(text(), 'Continue') or contains(text(), 'continue')]", timeout=3)
            if continue_button:
                logger.info(f"[{self.group_id}] 检测到Continue按钮，需要绑定信用卡，添加到绑卡队列")

                # 获取当前浏览器的session token
                session_token = None
                try:
                    cookies = self.page.cookies()
                    for cookie in cookies:
                        if cookie.get('name') == 'WorkosCursorSessionToken':
                            session_token = cookie.get('value')
                            logger.info(f"[{self.group_id}] 获取到session token用于绑卡")
                            break
                    if not session_token:
                        logger.warning(f"[{self.group_id}] 未找到session token")
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 获取session token失败: {e}")

                # 直接写入到绑卡队列文件
                try:
                    with open("card_account.txt", "a", encoding="utf-8") as f:
                        f.write(f"{self.email}|{session_token if session_token else ''}\n")
                    logger.info(f"[{self.group_id}] 需要绑卡的账号已添加到队列: {self.email}")
                except Exception as add_e:
                    logger.error(f"[{self.group_id}] 添加到绑卡队列失败: {add_e}")
                    # 如果添加到绑卡队列也失败，则记录到失败文件
                    if self.email:
                        self.write_failed_email(self.email, "需要绑卡但添加到队列失败")

                # 注册流程结束，直接返回
                logger.info(f"[{self.group_id}] 注册流程完成，绑卡任务已交给绑卡管理器处理")
                return
            # 如果没有Continue按钮，检查是否已经在登录确认页面
            elif "loginDeepControl" in current_url:
                logger.info(f"[{self.group_id}] 已在登录确认页面，查找登录按钮")
                # 查找并点击登录按钮
                login_button = self.page.ele("xpath://button[.//span[contains(text(), 'Log In')]]", timeout=10)
                if login_button:
                    logger.info(f"[{self.group_id}] 找到登录按钮，点击确认")
                    login_button.click()
                    time.sleep(5)
                else:
                    logger.warning(f"[{self.group_id}] 未找到登录按钮")

                # 获取并提交token
                self.get_and_submit_token()
            else:
                # 其他情况，尝试跳转到登录确认页面
                logger.info(f"[{self.group_id}] 未检测到Continue按钮或登录确认页面，尝试跳转到登录确认页面")
                self.navigate_to_login_deep_control()

        else:
            # 部分代理模式：重启浏览器并启用代理（原有逻辑）
            # 仅当USE_PROXY=True且USE_PROXY_FULL_PROCESS=False时
            logger.info(f"[{self.group_id}] 部分代理模式，准备重启浏览器并启用代理")
            use_proxy_for_restart = True

            # 保存当前URL和验证码
            current_url = self.page.url

            # 获取新代理（部分代理模式必须获取代理）
            # 检查当前代理是否过期或需要重置
            if self.is_proxy_expired():
                logger.info(f"[{self.group_id}] 当前代理已过期，正在获取新的代理IP...")
                if not self.reset_proxy_state():
                    logger.error(f"[{self.group_id}] 代理重置失败")
                    # 记录失败的邮箱账号
                    if self.email:
                        self.write_failed_email(self.email, "代理重置失败")
                    if self.error_callback:
                        self.error_callback(self.group_id, self.email, "代理重置失败")
                    return
                logger.info(f"[{self.group_id}] 已获取新代理: {self.proxy_host}:{self.proxy_port}")
            else:
                logger.info(f"[{self.group_id}] 当前代理仍在有效期内，但为了避免认证问题，重置代理状态")
                # 即使代理未过期，也重置一次以避免认证问题
                if not self.reset_proxy_state():
                    logger.warning(f"[{self.group_id}] 代理重置失败，尝试使用当前代理")
                else:
                    logger.info(f"[{self.group_id}] 代理状态已重置: {self.proxy_host}:{self.proxy_port}")

            # 关闭浏览器
            if self.page:
                self.page.quit()
                time.sleep(2)

            # 重启浏览器
            if not self.setup_browser(use_proxy=use_proxy_for_restart):
                logger.error(f"[{self.group_id}] 重启浏览器失败")
                # 记录失败的邮箱账号
                if self.email:
                    self.write_failed_email(self.email, "重启浏览器失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "重启浏览器失败")
                return

            # 重新访问页面
            self.page.get(current_url)
            time.sleep(3)

            # 输入验证码
            proxy_status = "启用代理后" if use_proxy_for_restart else "无代理模式下"
            logger.info(f"[{self.group_id}] {proxy_status}，开始输入验证码")
            if not self.input_verification_code(code):
                logger.error(f"[{self.group_id}] 验证码输入失败")
                # 记录失败的邮箱账号
                if self.email:
                    self.write_failed_email(self.email, "验证码输入失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "验证码输入失败")
                return

            # 等待页面加载完成
            time.sleep(5)

            # 检查是否跳转到登录确认页面
            current_url = self.page.url
            if "loginDeepControl" in current_url:
                logger.info(f"[{self.group_id}] 检测到登录确认页面，查找登录按钮")
                # 查找并点击登录按钮
                login_button = self.page.ele("xpath://button[.//span[contains(text(), 'Log In')]]", timeout=10)
                if login_button:
                    logger.info(f"[{self.group_id}] 找到登录按钮，点击确认")
                    login_button.click()
                    time.sleep(5)
                else:
                    logger.warning(f"[{self.group_id}] 未找到登录按钮")

                # 获取并提交token
                self.get_and_submit_token()
            else:
                # 检查是否需要绑定信用卡
                logger.info(f"[{self.group_id}] 未跳转到登录确认页面，检查是否需要绑定信用卡")

                # 检查是否有Continue按钮（需要绑定信用卡）
                continue_button = self.page.ele("xpath://button[contains(text(), 'Continue') or contains(text(), 'continue')]", timeout=3)
                if continue_button:
                    logger.info(f"[{self.group_id}] 检测到Continue按钮，需要绑定信用卡，添加到绑卡队列")

                    # 获取当前浏览器的session token
                    session_token = None
                    try:
                        cookies = self.page.cookies()
                        for cookie in cookies:
                            if cookie.get('name') == 'WorkosCursorSessionToken':
                                session_token = cookie.get('value')
                                logger.info(f"[{self.group_id}] 获取到session token用于绑卡")
                                break
                        if not session_token:
                            logger.warning(f"[{self.group_id}] 未找到session token")
                    except Exception as e:
                        logger.warning(f"[{self.group_id}] 获取session token失败: {e}")

                    # 直接写入到绑卡队列文件
                    try:
                        with open("card_account.txt", "a", encoding="utf-8") as f:
                            f.write(f"{self.email}|{session_token if session_token else ''}\n")
                        logger.info(f"[{self.group_id}] 需要绑卡的账号已添加到队列: {self.email}")
                    except Exception as add_e:
                        logger.error(f"[{self.group_id}] 添加到绑卡队列失败: {add_e}")
                        # 如果添加到绑卡队列也失败，则记录到失败文件
                        if self.email:
                            self.write_failed_email(self.email, "需要绑卡但添加到队列失败")

                    # 注册流程结束，直接返回
                    logger.info(f"[{self.group_id}] 注册流程完成，绑卡任务已交给绑卡管理器处理")
                    return
                else:
                    logger.warning(f"[{self.group_id}] 未检测到Continue按钮，可能页面状态异常")
                    # 记录异常情况
                    if self.email:
                        self.write_failed_email(self.email, "页面状态异常，未检测到登录确认页面或绑卡页面")
                    return

    def handle_credit_card_binding(self):
        """处理信用卡绑定流程，切换到无代理浏览器并完成后续所有流程"""
        try:
            logger.info(f"[{self.group_id}] 开始处理信用卡绑定流程")

            # 检查是否有Continue按钮
            continue_button = self.page.ele("xpath://button[text()='Continue']", timeout=5)
            if not continue_button:
                logger.info(f"[{self.group_id}] 未找到Continue按钮，可能已跳过")
                return False

            logger.info(f"[{self.group_id}] 发现需要绑定信用卡，立即切换到无代理浏览器")

            # 保存当前状态
            current_url = self.page.url
            session_token = self.get_session_token()

            if not session_token:
                logger.error(f"[{self.group_id}] 无法获取WorkosCursorSessionToken")
                return False

            logger.info(f"[{self.group_id}] 已保存URL: {current_url}")
            logger.info(f"[{self.group_id}] 已保存SessionToken: {session_token[:20]}...")

            # 保存原浏览器引用
            original_browser = self.page

            # 立即切换到无代理浏览器，后续所有流程都用这个浏览器
            no_proxy_browser = self.switch_to_no_proxy_browser(current_url, session_token)

            if not no_proxy_browser:
                logger.error(f"[{self.group_id}] 切换到无代理浏览器失败")
                return False

            # 关闭原代理浏览器
            try:
                logger.info(f"[{self.group_id}] 关闭原代理浏览器")
                original_browser.quit()
                time.sleep(2)
            except Exception as e:
                logger.warning(f"[{self.group_id}] 关闭原代理浏览器时出错: {e}")

            # 设置无代理浏览器为主浏览器
            self.page = no_proxy_browser
            logger.info(f"[{self.group_id}] 已切换到无代理浏览器，后续流程将使用此浏览器")

            # 在无代理浏览器中完成绑卡流程
            binding_result = self.complete_credit_card_binding_in_current_browser()

            if binding_result:
                logger.info(f"[{self.group_id}] 信用卡绑定成功，继续使用无代理浏览器完成后续流程")
                return True
            else:
                logger.error(f"[{self.group_id}] 信用卡绑定失败")
                return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 信用卡绑定流程异常: {e}")
            return False

    def switch_to_no_proxy_browser(self, url, session_token):
        """切换到无代理浏览器"""
        try:
            logger.info(f"[{self.group_id}] 创建无代理浏览器")

            # 创建无代理浏览器
            no_proxy_browser = self.create_no_proxy_browser()
            if not no_proxy_browser:
                logger.error(f"[{self.group_id}] 创建无代理浏览器失败")
                return None

            # 先访问cursor.com主页设置cookie
            logger.info(f"[{self.group_id}] 先访问cursor.com主页设置cookie")
            no_proxy_browser.get("https://cursor.com")
            time.sleep(3)

            # 设置SessionToken cookie到cursor.com域名下
            logger.info(f"[{self.group_id}] 设置WorkosCursorSessionToken到cursor.com域名")
            no_proxy_browser.set.cookies([{
                'name': 'WorkosCursorSessionToken',
                'value': session_token,
                'domain': 'cursor.com',
                'path': '/'
            }])

            # 现在访问保存的URL
            logger.info(f"[{self.group_id}] 访问保存的URL: {url}")
            no_proxy_browser.get(url)
            time.sleep(5)

            # 等待页面加载
            self.wait_for_page_load_in_browser(no_proxy_browser)

            return no_proxy_browser

        except Exception as e:
            logger.error(f"[{self.group_id}] 切换到无代理浏览器异常: {e}")
            return None

    def complete_credit_card_binding_in_current_browser(self):
        """在当前浏览器中完成信用卡绑定流程"""
        try:
            logger.info(f"[{self.group_id}] 在当前浏览器中开始信用卡绑定流程")

            # 点击Continue按钮
            continue_button = self.page.ele("xpath://button[text()='Continue']", timeout=10)
            if not continue_button:
                logger.error(f"[{self.group_id}] 未找到Continue按钮")
                return False

            logger.info(f"[{self.group_id}] 点击Continue按钮")
            continue_button.click()
            time.sleep(5)

            # 等待页面加载
            self.wait_for_page_load_in_browser(self.page)

            # 查找信用卡选项按钮
            card_button = self.page.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
            if not card_button:
                logger.error(f"[{self.group_id}] 未找到信用卡选项按钮")
                return False

            logger.info(f"[{self.group_id}] 找到信用卡选项按钮，点击")
            card_button.click()
            time.sleep(3)

            # 选择银行卡支付方式
            self.select_card_payment_method_in_browser(self.page)

            # 等待信用卡表单加载
            self.wait_for_credit_card_form_in_browser(self.page)

            # 最多重试2次
            max_retry_attempts = 2
            for attempt in range(max_retry_attempts):
                logger.info(f"[{self.group_id}] 信用卡绑定尝试 {attempt + 1}/{max_retry_attempts}")

                # 读取信用卡信息
                card_info = self.load_and_remove_card_info()
                if not card_info:
                    logger.error(f"[{self.group_id}] 无法获取信用卡信息")
                    return False

                # 每次尝试都填充完整的信用卡表单（刷新后需要重新填充所有信息）
                if not self.fill_credit_card_form_in_browser(self.page, card_info):
                    logger.error(f"[{self.group_id}] 填充信用卡信息失败")
                    if attempt < max_retry_attempts - 1:
                        logger.info(f"[{self.group_id}] 准备重试...")
                        continue
                    return False

                # 提交表单并检查结果
                binding_result = self.submit_credit_card_form_in_browser(self.page)

                if binding_result:
                    logger.info(f"[{self.group_id}] 信用卡绑定成功")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 信用卡绑定失败 (尝试 {attempt + 1}/{max_retry_attempts})")
                    if attempt < max_retry_attempts - 1:
                        logger.info(f"[{self.group_id}] 人机验证完成但未跳转说明绑卡失败，刷新页面重试...")
                        # 刷新页面重试
                        self.page.refresh()
                        time.sleep(5)

                        # 重新等待页面加载
                        self.wait_for_page_load_in_browser(self.page)

                        # 重新点击Continue按钮
                        continue_button = self.page.ele("xpath://button[text()='Continue']", timeout=10)
                        if continue_button:
                            logger.info(f"[{self.group_id}] 刷新后重新点击Continue按钮")
                            continue_button.click()
                            time.sleep(5)
                            self.wait_for_page_load_in_browser(self.page)

                            # 重新点击信用卡选项按钮
                            card_button = self.page.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
                            if card_button:
                                logger.info(f"[{self.group_id}] 刷新后重新点击信用卡选项按钮")
                                card_button.click()
                                time.sleep(3)

                                # 重新选择银行卡支付方式
                                self.select_card_payment_method_in_browser(self.page)

                                self.wait_for_credit_card_form_in_browser(self.page)

                        continue
                    else:
                        logger.error(f"[{self.group_id}] 信用卡绑定失败，已达到最大重试次数")
                        return False

            return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 在当前浏览器中完成信用卡绑定异常: {e}")
            return False

    def select_card_payment_method_in_browser(self, browser):
        """在指定浏览器中选择银行卡支付方式"""
        try:
            logger.info(f"[{self.group_id}] 尝试选择银行卡支付方式...")

            # 银行卡支付方式选择器
            card_option_selectors = [
                "xpath://input[@id='payment-method-accordion-item-title-card']",
                "xpath://label[contains(text(),'Card')]",
                "xpath://button[contains(text(),'Card')]",
                "xpath://*[@data-testid='payment-method-card']",
                "xpath://div[contains(@class,'payment-method')]//span[contains(text(),'Card')]",
                "xpath://div[contains(@class,'accordion')]//span[contains(text(),'Card')]"
            ]

            card_option_selected = False
            for selector in card_option_selectors:
                card_option = browser.ele(selector, timeout=3)
                if card_option:
                    logger.info(f"[{self.group_id}] 找到银行卡支付选项: {selector}")
                    card_option.click()
                    time.sleep(2)
                    card_option_selected = True
                    break

            if card_option_selected:
                logger.info(f"[{self.group_id}] 已选择银行卡支付方式")
            else:
                logger.warning(f"[{self.group_id}] 未找到银行卡支付方式选项，可能已经默认选中")

            return True

        except Exception as e:
            logger.warning(f"[{self.group_id}] 选择银行卡支付方式时出错: {e}")
            return True  # 即使失败也继续，因为可能已经默认选中

    def get_session_token(self):
        """获取WorkosCursorSessionToken"""
        try:
            # 获取所有cookies
            cookies = self.page.cookies()

            for cookie in cookies:
                if cookie.get('name') == 'WorkosCursorSessionToken':
                    logger.info(f"[{self.group_id}] 找到WorkosCursorSessionToken")
                    return cookie.get('value')

            logger.warning(f"[{self.group_id}] 未找到WorkosCursorSessionToken")
            return None

        except Exception as e:
            logger.error(f"[{self.group_id}] 获取SessionToken时出错: {e}")
            return None



    def create_no_proxy_browser(self):
        """创建无代理浏览器"""
        try:
            logger.info(f"[{self.group_id}] 创建无代理浏览器")

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 使用临时用户数据目录
            no_proxy_profile_path = f"chrome-data-no-proxy/group_{self.group_id}"
            os.makedirs(no_proxy_profile_path, exist_ok=True)
            co.set_user_data_path(no_proxy_profile_path)

            # 设置无痕模式
            co.set_argument("--incognito")
            logger.info(f"[{self.group_id}] 已启用无代理浏览器无痕模式")

            # 基本浏览器参数（无代理）
            co.set_argument("--disable-extensions")
            co.set_argument("--disable-gpu")
            co.set_argument("--disable-dev-shm-usage")
            co.set_argument("--disable-infobars")
            co.set_argument("--disable-notifications")
            co.set_argument("--no-first-run")
            co.set_argument("--no-default-browser-check")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动无代理浏览器
            no_proxy_browser = ChromiumPage(co)
            time.sleep(2)

            logger.info(f"[{self.group_id}] 无代理浏览器创建成功")
            return no_proxy_browser

        except Exception as e:
            logger.error(f"[{self.group_id}] 创建无代理浏览器失败: {e}")
            return None

    def wait_for_page_load_in_browser(self, browser):
        """在指定浏览器中等待页面完全加载"""
        try:
            logger.info(f"[{self.group_id}] 等待页面完全加载...")

            # 等待页面加载完成
            time.sleep(3)

            # 检查页面是否加载完成
            for i in range(10):  # 最多等待10秒
                try:
                    # 检查页面状态
                    ready_state = browser.run_js("return document.readyState")
                    if ready_state == "complete":
                        logger.info(f"[{self.group_id}] 页面加载完成")
                        break
                except:
                    pass
                time.sleep(1)

            # 额外等待确保所有元素都加载完成
            time.sleep(2)

        except Exception as e:
            logger.warning(f"[{self.group_id}] 等待页面加载时出错: {e}")

    def wait_for_credit_card_form_in_browser(self, browser):
        """在指定浏览器中等待信用卡表单完全加载"""
        try:
            logger.info(f"[{self.group_id}] 等待信用卡表单加载...")

            # 等待卡号输入框出现
            for i in range(15):  # 最多等待15秒
                card_number_input = browser.ele("xpath://input[@id='cardNumber']", timeout=1)
                if card_number_input:
                    logger.info(f"[{self.group_id}] 信用卡表单加载完成")
                    break
                time.sleep(1)
            else:
                logger.warning(f"[{self.group_id}] 信用卡表单加载超时")

            # 额外等待确保表单完全可用
            time.sleep(2)

        except Exception as e:
            logger.warning(f"[{self.group_id}] 等待信用卡表单加载时出错: {e}")

    def fill_credit_card_form_in_browser(self, browser, card_info):
        """在指定浏览器中填充信用卡表单"""
        try:
            logger.info(f"[{self.group_id}] 开始在无代理浏览器中填充信用卡表单")

            # 1. 填充卡号
            card_number_input = browser.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                card_number_input.clear()
                card_number_input.input(str(card_info.get('CardNumber', '')))
                logger.info(f"[{self.group_id}] 已填充卡号")
            else:
                logger.error(f"[{self.group_id}] 未找到卡号输入框")
                return False

            time.sleep(1)

            # 2. 填充有效期
            card_expiry_input = browser.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                card_expiry_input.clear()
                card_expiry_input.input(str(formatted_expiry))
                logger.info(f"[{self.group_id}] 已填充有效期")
            else:
                logger.error(f"[{self.group_id}] 未找到有效期输入框")
                return False

            time.sleep(1)

            # 3. 填充CVV
            card_cvc_input = browser.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                card_cvc_input.clear()
                card_cvc_input.input(str(card_info.get('CVV', '')))
                logger.info(f"[{self.group_id}] 已填充CVV")
            else:
                logger.error(f"[{self.group_id}] 未找到CVV输入框")
                return False

            time.sleep(1)

            # 4. 填充姓名
            billing_name_input = browser.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                billing_name_input.clear()
                billing_name_input.input(str(card_info.get('Name', '')))
                logger.info(f"[{self.group_id}] 已填充姓名")
            else:
                logger.error(f"[{self.group_id}] 未找到姓名输入框")
                return False

            time.sleep(1)

            # 继续填充其他字段
            return self.fill_remaining_fields_in_browser(browser, card_info)

        except Exception as e:
            logger.error(f"[{self.group_id}] 在无代理浏览器中填充信用卡表单异常: {e}")
            return False

    def fill_remaining_fields_in_browser(self, browser, card_info):
        """在指定浏览器中填充剩余的地址等字段"""
        try:
            # 5. 选择国家 CN
            billing_country_select = browser.ele("@id=billingCountry", timeout=10)
            if billing_country_select:
                try:
                    billing_country_select.select.by_value('CN')
                    logger.info(f"[{self.group_id}] 已选择国家 CN")
                    browser.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                except:
                    try:
                        billing_country_select.select.by_text('China')
                        logger.info(f"[{self.group_id}] 已选择国家 China")
                        browser.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                    except:
                        logger.warning(f"[{self.group_id}] 选择国家失败，继续其他字段")

            time.sleep(3)  # 等待省份下拉框加载

            # 6. 填充地址
            billing_address_input = browser.ele("xpath://input[@id='billingAddressLine1']", timeout=10)
            if billing_address_input:
                billing_address_input.clear()
                billing_address_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充地址")

            time.sleep(1)

            # 7. 填充邮政编码
            billing_postal_input = browser.ele("xpath://input[@id='billingPostalCode']", timeout=10)
            if billing_postal_input:
                billing_postal_input.clear()
                billing_postal_input.input("353000")
                logger.info(f"[{self.group_id}] 已填充邮政编码")

            time.sleep(1)

            # 8. 填充城市
            billing_locality_input = browser.ele("xpath://input[@id='billingLocality']", timeout=10)
            if billing_locality_input:
                billing_locality_input.clear()
                billing_locality_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充城市")

            time.sleep(1)

            # 9. 选择省份
            billing_admin_select = browser.ele("@id=billingAdministrativeArea", timeout=10)
            if billing_admin_select:
                try:
                    options = billing_admin_select.eles("tag:option")
                    for option in options:
                        if '福建' in option.text:
                            option.click()
                            logger.info(f"[{self.group_id}] 已选择省份: {option.text}")
                            break
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 选择省份时出错: {e}")

            time.sleep(1)

            # 10. 填充区域
            billing_dependent_input = browser.ele("xpath://input[@id='billingDependentLocality']", timeout=10)
            if billing_dependent_input:
                billing_dependent_input.clear()
                billing_dependent_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充区域")

            time.sleep(3)
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 在无代理浏览器中填充剩余字段异常: {e}")
            return False

    def update_credit_card_core_info_in_browser(self, browser, card_info):
        """在指定浏览器中只更新信用卡核心信息"""
        try:
            logger.info(f"[{self.group_id}] 开始在无代理浏览器中更新信用卡核心信息")

            # 1. 更新卡号
            card_number_input = browser.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                card_number_input.clear()
                card_number_input.input(str(card_info.get('CardNumber', '')))
                logger.info(f"[{self.group_id}] 已更新卡号")
            else:
                logger.error(f"[{self.group_id}] 未找到卡号输入框")
                return False

            time.sleep(1)

            # 2. 更新有效期
            card_expiry_input = browser.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                card_expiry_input.clear()
                card_expiry_input.input(str(formatted_expiry))
                logger.info(f"[{self.group_id}] 已更新有效期")
            else:
                logger.error(f"[{self.group_id}] 未找到有效期输入框")
                return False

            time.sleep(1)

            # 3. 更新CVV
            card_cvc_input = browser.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                card_cvc_input.clear()
                card_cvc_input.input(str(card_info.get('CVV', '')))
                logger.info(f"[{self.group_id}] 已更新CVV")
            else:
                logger.error(f"[{self.group_id}] 未找到CVV输入框")
                return False

            time.sleep(1)

            # 4. 更新姓名
            billing_name_input = browser.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                billing_name_input.clear()
                billing_name_input.input(str(card_info.get('Name', '')))
                logger.info(f"[{self.group_id}] 已更新姓名")
            else:
                logger.error(f"[{self.group_id}] 未找到姓名输入框")
                return False

            time.sleep(2)
            logger.info(f"[{self.group_id}] 无代理浏览器信用卡核心信息更新完成")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 在无代理浏览器中更新信用卡核心信息异常: {e}")
            return False

    def submit_credit_card_form_in_browser(self, browser):
        """在指定浏览器中提交信用卡表单"""
        try:
            logger.info(f"[{self.group_id}] 开始在无代理浏览器中提交信用卡表单")

            # 点击提交按钮
            submit_button = browser.ele("xpath://button[@data-testid='hosted-payment-submit-button']", timeout=10)
            if not submit_button:
                logger.error(f"[{self.group_id}] 在无代理浏览器中未找到提交按钮")
                return False

            logger.info(f"[{self.group_id}] 在无代理浏览器中找到提交按钮，点击")
            submit_button.click()
            time.sleep(5)

            # 检查是否出现人机验证 - 使用改进的检测方式
            hcaptcha_elements = [
                "xpath://div[@id='h-captcha']",
                "xpath://iframe[contains(@src,'hcaptcha')]",
                "xpath://div[contains(@class,'h-captcha')]",
                "xpath://div[contains(@class,'hcaptcha')]"
            ]

            hcaptcha_found = False
            hcaptcha_root = None
            for selector in hcaptcha_elements:
                hcaptcha_root = browser.ele(selector, timeout=3)
                if hcaptcha_root:
                    logger.info(f"[{self.group_id}] 在无代理浏览器中检测到人机验证: {selector}")
                    hcaptcha_found = True
                    break

            if hcaptcha_found:
                logger.info(f"[{self.group_id}] 在无代理浏览器中检测到人机验证，尝试处理")
                try:
                    # 直接使用Tab+空格方式，不依赖元素操作
                    logger.info(f"[{self.group_id}] 使用Tab+空格方式处理人机验证")

                    # 发送两次Tab键：第一次到关闭按钮，第二次到复选框，然后发送空格键进行点击
                    browser.actions.key_down('TAB')
                    browser.actions.key_up('TAB')
                    time.sleep(0.5)
                    browser.actions.key_down('TAB')
                    browser.actions.key_up('TAB')
                    time.sleep(1)
                    browser.actions.key_down('space')
                    browser.actions.key_up('space')
                    logger.info(f"[{self.group_id}] 已发送两次Tab和Space键")
                    time.sleep(2)

                    # 检测验证是否完成
                    def is_captcha_completed():
                        # 检查h-captcha-response是否有值
                        response_element = browser.ele("xpath://textarea[@name='h-captcha-response']", timeout=1)
                        if response_element:
                            response_value = response_element.attr('value') or browser.run_js("return arguments[0].value;", response_element)
                            if response_value and response_value.strip():
                                logger.info(f"[{self.group_id}] 检测到h-captcha-response有值，验证完成")
                                return True

                        # 检查g-recaptcha-response是否有值
                        recaptcha_element = browser.ele("xpath://textarea[@name='g-recaptcha-response']", timeout=1)
                        if recaptcha_element:
                            recaptcha_value = recaptcha_element.attr('value') or browser.run_js("return arguments[0].value;", recaptcha_element)
                            if recaptcha_value and recaptcha_value.strip():
                                logger.info(f"[{self.group_id}] 检测到g-recaptcha-response有值，验证完成")
                                return True

                        # 检查验证容器是否消失或状态改变
                        captcha_container = browser.ele("xpath://div[contains(@class,'HCaptcha-container')]", timeout=1)
                        if not captcha_container:
                            logger.info(f"[{self.group_id}] 验证容器消失，验证完成")
                            return True

                        return False

                    # 等待人机验证完成
                    logger.info(f"[{self.group_id}] 等待人机验证完成...")
                    for i in range(60):
                        time.sleep(1)

                        # 首先检查验证是否完成
                        if is_captcha_completed():
                            logger.info(f"[{self.group_id}] 人机验证已完成，等待页面跳转...")
                            # 验证完成后再等待一段时间让页面跳转
                            for j in range(15):
                                time.sleep(2)
                                current_url = browser.url
                                if "cursor.com" in current_url:
                                    logger.info(f"[{self.group_id}] 无代理浏览器人机验证完成，已跳转到cursor.com")
                                    return True
                            # 如果验证完成但没有跳转，说明绑卡失败
                            logger.warning(f"[{self.group_id}] 人机验证完成但未跳转，当前URL: {browser.url}，绑卡失败")
                            return False

                        # 检查是否直接跳转了
                        current_url = browser.url
                        if "cursor.com" in current_url:
                            logger.info(f"[{self.group_id}] 无代理浏览器人机验证完成，已跳转到cursor.com")
                            return True

                        # 检查是否还有验证框存在
                        if i % 10 == 0:  # 每10秒检查一次
                            still_has_captcha = False
                            for selector in hcaptcha_elements:
                                if browser.ele(selector, timeout=1):
                                    still_has_captcha = True
                                    break
                            if not still_has_captcha:
                                logger.info(f"[{self.group_id}] 验证框已消失，检查页面状态")
                                time.sleep(2)
                                current_url = browser.url
                                if "cursor.com" in current_url:
                                    logger.info(f"[{self.group_id}] 验证完成，已跳转到cursor.com")
                                    return True

                    logger.warning(f"[{self.group_id}] 无代理浏览器人机验证超时")
                    return False

                except Exception as e:
                    logger.error(f"[{self.group_id}] 在无代理浏览器中处理人机验证时出错: {e}")
                    # 确保切换回主页面
                    try:
                        browser.set.window.to_main()
                    except:
                        pass
                    return False
            else:
                logger.info(f"[{self.group_id}] 在无代理浏览器中未检测到人机验证，检查是否绑定成功")
                time.sleep(10)

                # 检查是否绑定成功
                current_url = browser.url
                if "cursor.com" in current_url:
                    logger.info(f"[{self.group_id}] 无代理浏览器信用卡绑定成功，已跳转到cursor.com")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 无代理浏览器信用卡绑定可能失败，当前URL: {current_url}")
                    return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 在无代理浏览器中提交信用卡表单异常: {e}")
            return False

    def bind_card_with_new_browser(self, binding_url):
        """使用新的无代理浏览器进行信用卡绑定"""
        card_browser = None
        try:
            logger.info(f"[{self.group_id}] 开始创建无代理绑卡浏览器")

            # 创建新的无代理浏览器
            card_browser = self.create_card_binding_browser()
            if not card_browser:
                logger.error(f"[{self.group_id}] 创建绑卡浏览器失败")
                return False

            # 访问绑卡页面
            logger.info(f"[{self.group_id}] 访问绑卡页面: {binding_url}")
            card_browser.get(binding_url)
            time.sleep(5)

            # 执行绑卡流程
            binding_result = self.execute_card_binding_in_new_browser(card_browser)

            return binding_result

        except Exception as e:
            logger.error(f"[{self.group_id}] 新浏览器绑卡异常: {e}")
            return False
        finally:
            # 确保关闭绑卡浏览器
            if card_browser:
                try:
                    logger.info(f"[{self.group_id}] 关闭绑卡浏览器")
                    card_browser.quit()
                    time.sleep(2)
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 关闭绑卡浏览器时出错: {e}")

    def create_card_binding_browser(self):
        """创建专用于信用卡绑定的无代理浏览器"""
        try:
            logger.info(f"[{self.group_id}] 创建信用卡绑定专用浏览器（无代理）")

            # 设置浏览器选项
            co = ChromiumOptions()

            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)

            # 使用临时用户数据目录
            card_profile_path = f"chrome-data-card-binding/group_{self.group_id}"
            os.makedirs(card_profile_path, exist_ok=True)
            co.set_user_data_path(card_profile_path)

            # 设置无痕模式
            co.set_argument("--incognito")
            logger.info(f"[{self.group_id}] 已启用绑卡浏览器无痕模式")

            # 基本浏览器参数（无代理）
            co.set_argument("--disable-extensions")
            co.set_argument("--disable-gpu")
            co.set_argument("--disable-dev-shm-usage")
            co.set_argument("--disable-infobars")
            co.set_argument("--disable-notifications")
            co.set_argument("--no-first-run")
            co.set_argument("--no-default-browser-check")

            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")

            # 设置随机端口
            co.auto_port()

            # 设置无头模式
            co.headless(self.headless)

            # 启动绑卡浏览器（无代理）
            card_browser = ChromiumPage(co)
            time.sleep(2)

            logger.info(f"[{self.group_id}] 绑卡浏览器创建成功（无代理模式）")
            return card_browser

        except Exception as e:
            logger.error(f"[{self.group_id}] 创建绑卡浏览器失败: {e}")
            return None

    def execute_card_binding_in_new_browser(self, card_browser):
        """在新浏览器中执行信用卡绑定流程"""
        max_retry_attempts = 2  # 最多重试2次

        try:
            logger.info(f"[{self.group_id}] 开始在新浏览器中执行绑卡流程")

            # 等待页面加载
            time.sleep(3)

            # 查找并点击信用卡选项按钮
            card_button = card_browser.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
            if not card_button:
                logger.error(f"[{self.group_id}] 在新浏览器中未找到信用卡选项按钮")
                return False

            logger.info(f"[{self.group_id}] 在新浏览器中找到信用卡选项按钮，点击")
            card_button.click()
            time.sleep(3)

            # 重试绑定信用卡
            for attempt in range(max_retry_attempts):
                logger.info(f"[{self.group_id}] 新浏览器信用卡绑定尝试 {attempt + 1}/{max_retry_attempts}")

                # 读取信用卡信息
                card_info = self.load_and_remove_card_info()
                if not card_info:
                    logger.error(f"[{self.group_id}] 无法获取信用卡信息")
                    return False

                # 根据是否是第一次尝试选择填充方式
                if attempt == 0:
                    # 第一次尝试：填充完整的信用卡表单
                    if not self.fill_credit_card_form_in_new_browser(card_browser, card_info):
                        logger.error(f"[{self.group_id}] 在新浏览器中填充信用卡信息失败")
                        if attempt < max_retry_attempts - 1:
                            logger.info(f"[{self.group_id}] 准备重试...")
                            continue
                        return False
                else:
                    # 重试时：只更新核心信息
                    if not self.update_credit_card_core_info_in_new_browser(card_browser, card_info):
                        logger.error(f"[{self.group_id}] 在新浏览器中更新信用卡核心信息失败")
                        if attempt < max_retry_attempts - 1:
                            logger.info(f"[{self.group_id}] 准备重试...")
                            continue
                        return False

                # 提交表单并检查结果
                binding_result = self.submit_credit_card_form_in_new_browser(card_browser)

                if binding_result:
                    logger.info(f"[{self.group_id}] 新浏览器信用卡绑定成功")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 新浏览器信用卡绑定失败 (尝试 {attempt + 1}/{max_retry_attempts})")
                    if attempt < max_retry_attempts - 1:
                        logger.info(f"[{self.group_id}] 准备使用新的信用卡信息重试...")
                        time.sleep(2)
                        continue
                    else:
                        logger.error(f"[{self.group_id}] 新浏览器信用卡绑定失败，已达到最大重试次数")
                        return False

            return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 新浏览器绑卡流程异常: {e}")
            return False

    def format_expiry_date(self, expiry_str):
        """格式化有效期，确保年份只有两位数"""
        try:
            if not expiry_str:
                return expiry_str

            # 如果包含斜杠，分割处理
            if '/' in expiry_str:
                parts = expiry_str.split('/')
                if len(parts) == 2:
                    month = parts[0].strip()
                    year = parts[1].strip()

                    # 确保月份是两位数
                    if len(month) == 1:
                        month = '0' + month

                    # 确保年份是两位数
                    if len(year) == 4:
                        year = year[-2:]  # 取后两位
                    elif len(year) == 1:
                        year = '0' + year

                    formatted = f"{month}/{year}"
                    logger.info(f"[{self.group_id}] 有效期格式化: {expiry_str} -> {formatted}")
                    return formatted

            # 如果不包含斜杠，直接返回
            return expiry_str

        except Exception as e:
            logger.warning(f"[{self.group_id}] 格式化有效期时出错: {e}，使用原始值: {expiry_str}")
            return expiry_str

    def human_like_input_in_new_browser(self, card_browser, element, text, field_name=""):
        """在新浏览器中模拟人类输入行为"""
        try:
            import random

            # 模拟鼠标移动到元素
            try:
                rect = element.rect
                if rect:
                    nearby_x = rect.center_x + random.randint(-50, 50)
                    nearby_y = rect.center_y + random.randint(-30, 30)
                    card_browser.actions.move_to(nearby_x, nearby_y)
                    time.sleep(random.uniform(0.1, 0.3))

                    card_browser.actions.move_to(rect.center_x, rect.center_y)
                    time.sleep(random.uniform(0.2, 0.5))
            except:
                pass

            # 点击元素获得焦点
            element.click()
            time.sleep(random.uniform(0.3, 0.8))

            # 清空字段
            card_browser.actions.key_down('ctrl').key_down('a').key_up('a').key_up('ctrl')
            time.sleep(random.uniform(0.1, 0.3))
            card_browser.actions.key_down('Delete').key_up('Delete')
            time.sleep(random.uniform(0.2, 0.5))

            # 逐字符输入
            text_str = str(text)
            for i, char in enumerate(text_str):
                element.input(char)

                if char.isdigit():
                    delay = random.uniform(0.05, 0.12)
                else:
                    delay = random.uniform(0.08, 0.18)

                if random.random() < 0.1:
                    delay += random.uniform(0.3, 0.8)

                time.sleep(delay)

            # 输入完成后的停顿
            time.sleep(random.uniform(0.5, 1.2))

            # 触发验证事件
            card_browser.run_js("""
                arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                arguments[0].blur();
            """, element)
            time.sleep(random.uniform(0.3, 0.6))

            logger.info(f"[{self.group_id}] 新浏览器已人性化输入{field_name}: {text}")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 新浏览器人性化输入{field_name}失败: {e}")
            return False

    def fill_credit_card_form_in_new_browser(self, card_browser, card_info):
        """在新浏览器中填充信用卡表单"""
        try:
            logger.info(f"[{self.group_id}] 开始在新浏览器中填充信用卡表单")

            # 1. 填充卡号
            card_number_input = card_browser.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                if not self.human_like_input_in_new_browser(card_browser, card_number_input, card_info.get('CardNumber', ''), "卡号"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 新浏览器未找到卡号输入框")
                return False

            time.sleep(random.uniform(2.0, 4.0))

            # 2. 填充有效期
            card_expiry_input = card_browser.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                if not self.human_like_input_in_new_browser(card_browser, card_expiry_input, formatted_expiry, "有效期"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 新浏览器未找到有效期输入框")
                return False

            time.sleep(random.uniform(1.5, 3.0))

            # 3. 填充CVV
            card_cvc_input = card_browser.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                if not self.human_like_input_in_new_browser(card_browser, card_cvc_input, card_info.get('CVV', ''), "CVV"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 新浏览器未找到CVV输入框")
                return False

            time.sleep(random.uniform(1.5, 3.0))

            # 4. 填充姓名
            billing_name_input = card_browser.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                if not self.human_like_input_in_new_browser(card_browser, billing_name_input, card_info.get('Name', ''), "姓名"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 新浏览器未找到姓名输入框")
                return False

            time.sleep(random.uniform(2.0, 3.5))

            # 继续填充其他字段（地址等）
            return self.fill_remaining_fields_in_new_browser(card_browser, card_info)

        except Exception as e:
            logger.error(f"[{self.group_id}] 新浏览器填充信用卡表单异常: {e}")
            return False

    def fill_remaining_fields_in_new_browser(self, card_browser, card_info):
        """在新浏览器中填充剩余的地址等字段"""
        try:
            # 5. 选择国家 CN
            billing_country_select = card_browser.ele("@id=billingCountry", timeout=10)
            if billing_country_select:
                try:
                    billing_country_select.select.by_value('CN')
                    logger.info(f"[{self.group_id}] 新浏览器已选择国家 CN")
                    card_browser.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                except:
                    try:
                        billing_country_select.select.by_text('China')
                        logger.info(f"[{self.group_id}] 新浏览器已选择国家 China")
                        card_browser.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                    except:
                        logger.warning(f"[{self.group_id}] 新浏览器选择国家失败，继续其他字段")

            time.sleep(3)  # 等待省份下拉框加载

            # 6. 填充地址
            billing_address_input = card_browser.ele("xpath://input[@id='billingAddressLine1']", timeout=10)
            if billing_address_input:
                billing_address_input.clear()
                billing_address_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 新浏览器已填充地址")

            time.sleep(1)

            # 7. 填充邮政编码
            billing_postal_input = card_browser.ele("xpath://input[@id='billingPostalCode']", timeout=10)
            if billing_postal_input:
                billing_postal_input.clear()
                billing_postal_input.input("353000")
                logger.info(f"[{self.group_id}] 新浏览器已填充邮政编码")

            time.sleep(1)

            # 8. 填充城市
            billing_locality_input = card_browser.ele("xpath://input[@id='billingLocality']", timeout=10)
            if billing_locality_input:
                billing_locality_input.clear()
                billing_locality_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 新浏览器已填充城市")

            time.sleep(1)

            # 9. 选择省份
            billing_admin_select = card_browser.ele("@id=billingAdministrativeArea", timeout=10)
            if billing_admin_select:
                try:
                    options = billing_admin_select.eles("tag:option")
                    for option in options:
                        if '福建' in option.text:
                            option.click()
                            logger.info(f"[{self.group_id}] 新浏览器已选择省份: {option.text}")
                            break
                except Exception as e:
                    logger.warning(f"[{self.group_id}] 新浏览器选择省份时出错: {e}")

            time.sleep(1)

            # 10. 填充区域
            billing_dependent_input = card_browser.ele("xpath://input[@id='billingDependentLocality']", timeout=10)
            if billing_dependent_input:
                billing_dependent_input.clear()
                billing_dependent_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 新浏览器已填充区域")

            time.sleep(3)
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 新浏览器填充剩余字段异常: {e}")
            return False

    def update_credit_card_core_info_in_new_browser(self, card_browser, card_info):
        """在新浏览器中只更新信用卡核心信息"""
        try:
            logger.info(f"[{self.group_id}] 开始在新浏览器中更新信用卡核心信息")

            # 1. 更新卡号
            card_number_input = card_browser.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                if not self.human_like_input_in_new_browser(card_browser, card_number_input, card_info.get('CardNumber', ''), "卡号"):
                    return False

            time.sleep(random.uniform(1.5, 3.0))

            # 2. 更新有效期
            card_expiry_input = card_browser.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                if not self.human_like_input_in_new_browser(card_browser, card_expiry_input, formatted_expiry, "有效期"):
                    return False

            time.sleep(random.uniform(1.0, 2.5))

            # 3. 更新CVV
            card_cvc_input = card_browser.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                if not self.human_like_input_in_new_browser(card_browser, card_cvc_input, card_info.get('CVV', ''), "CVV"):
                    return False

            time.sleep(random.uniform(1.0, 2.0))

            # 4. 更新姓名
            billing_name_input = card_browser.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                if not self.human_like_input_in_new_browser(card_browser, billing_name_input, card_info.get('Name', ''), "姓名"):
                    return False

            time.sleep(random.uniform(2.0, 3.5))
            logger.info(f"[{self.group_id}] 新浏览器信用卡核心信息更新完成")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 新浏览器更新信用卡核心信息异常: {e}")
            return False

    def submit_credit_card_form_in_new_browser(self, card_browser):
        """在新浏览器中提交信用卡表单"""
        try:
            logger.info(f"[{self.group_id}] 开始在新浏览器中提交信用卡表单")

            # 点击提交按钮
            submit_button = card_browser.ele("xpath://button[@data-testid='hosted-payment-submit-button']", timeout=10)
            if not submit_button:
                logger.error(f"[{self.group_id}] 新浏览器未找到提交按钮")
                return False

            logger.info(f"[{self.group_id}] 新浏览器找到提交按钮，点击")
            submit_button.click()
            time.sleep(5)

            # 检查是否出现人机验证 - 使用改进的检测方式
            hcaptcha_elements = [
                "xpath://div[@id='h-captcha']",
                "xpath://iframe[contains(@src,'hcaptcha')]",
                "xpath://div[contains(@class,'h-captcha')]",
                "xpath://div[contains(@class,'hcaptcha')]"
            ]

            hcaptcha_found = False
            hcaptcha_root = None
            for selector in hcaptcha_elements:
                hcaptcha_root = card_browser.ele(selector, timeout=3)
                if hcaptcha_root:
                    logger.info(f"[{self.group_id}] 新浏览器检测到人机验证: {selector}")
                    hcaptcha_found = True
                    break

            if hcaptcha_found:
                logger.info(f"[{self.group_id}] 新浏览器检测到人机验证，尝试处理")
                try:
                    # 直接使用Tab+空格方式，不依赖元素操作
                    logger.info(f"[{self.group_id}] 使用Tab+空格方式处理人机验证")

                    # 发送两次Tab键：第一次到关闭按钮，第二次到复选框，然后发送空格键进行点击
                    card_browser.actions.key_down('TAB')
                    card_browser.actions.key_up('TAB')
                    time.sleep(0.5)
                    card_browser.actions.key_down('TAB')
                    card_browser.actions.key_up('TAB')
                    time.sleep(1)
                    card_browser.actions.key_down('space')
                    card_browser.actions.key_up('space')
                    logger.info(f"[{self.group_id}] 已发送两次Tab和Space键")
                    time.sleep(2)

                    # 检测验证是否完成
                    def is_captcha_completed():
                        # 检查h-captcha-response是否有值
                        response_element = card_browser.ele("xpath://textarea[@name='h-captcha-response']", timeout=1)
                        if response_element:
                            response_value = response_element.attr('value') or card_browser.run_js("return arguments[0].value;", response_element)
                            if response_value and response_value.strip():
                                logger.info(f"[{self.group_id}] 检测到h-captcha-response有值，验证完成")
                                return True

                        # 检查g-recaptcha-response是否有值
                        recaptcha_element = card_browser.ele("xpath://textarea[@name='g-recaptcha-response']", timeout=1)
                        if recaptcha_element:
                            recaptcha_value = recaptcha_element.attr('value') or card_browser.run_js("return arguments[0].value;", recaptcha_element)
                            if recaptcha_value and recaptcha_value.strip():
                                logger.info(f"[{self.group_id}] 检测到g-recaptcha-response有值，验证完成")
                                return True

                        # 检查验证容器是否消失或状态改变
                        captcha_container = card_browser.ele("xpath://div[contains(@class,'HCaptcha-container')]", timeout=1)
                        if not captcha_container:
                            logger.info(f"[{self.group_id}] 验证容器消失，验证完成")
                            return True

                        return False

                    # 等待人机验证完成
                    logger.info(f"[{self.group_id}] 等待人机验证完成...")
                    for i in range(60):
                        time.sleep(1)

                        # 首先检查验证是否完成
                        if is_captcha_completed():
                            logger.info(f"[{self.group_id}] 人机验证已完成，等待页面跳转...")
                            # 验证完成后再等待一段时间让页面跳转
                            for j in range(15):
                                time.sleep(2)
                                current_url = card_browser.url
                                if "cursor.com" in current_url:
                                    logger.info(f"[{self.group_id}] 新浏览器人机验证完成，已跳转到cursor.com")
                                    return True
                            # 如果验证完成但没有跳转，说明绑卡失败
                            logger.warning(f"[{self.group_id}] 人机验证完成但未跳转，当前URL: {card_browser.url}，绑卡失败")
                            return False

                        # 检查是否直接跳转了
                        current_url = card_browser.url
                        if "cursor.com" in current_url:
                            logger.info(f"[{self.group_id}] 新浏览器人机验证完成，已跳转到cursor.com")
                            return True

                    logger.warning(f"[{self.group_id}] 新浏览器人机验证超时")
                    return False

                except Exception as e:
                    logger.error(f"[{self.group_id}] 新浏览器处理人机验证时出错: {e}")
                    return False
            else:
                logger.info(f"[{self.group_id}] 新浏览器未检测到人机验证，检查是否绑定成功")
                time.sleep(3)

                # 检查是否绑定成功
                current_url = card_browser.url
                if "cursor.com" in current_url:
                    logger.info(f"[{self.group_id}] 新浏览器信用卡绑定成功，已跳转到cursor.com")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 新浏览器信用卡绑定可能失败，当前URL: {current_url}")
                    return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 新浏览器提交信用卡表单异常: {e}")
            return False

    def load_and_remove_card_info(self):
        """从JSON文件加载信用卡信息，根据USE_FIXED_FIRST_CARD控制是否删除已使用的记录"""
        try:
            import json

            # 读取JSON文件
            with open("ChinaUnionPay.json", "r", encoding="utf-8") as f:
                card_list = json.load(f)

            if not card_list:
                logger.error(f"[{self.group_id}] ChinaUnionPay.json文件为空")
                return None

            # 取第一个信用卡信息
            card_info = card_list[0]
            logger.info(f"[{self.group_id}] 获取信用卡信息: {card_info.get('Name', 'Unknown')}")

            # 根据USE_FIXED_FIRST_CARD控制是否删除记录
            if USE_FIXED_FIRST_CARD:
                logger.info(f"[{self.group_id}] 固定读取模式：使用第一条信用卡数据，不删除文件中的记录")
            else:
                # 删除已使用的记录
                card_list.pop(0)

                # 写回文件
                with open("ChinaUnionPay.json", "w", encoding="utf-8") as f:
                    json.dump(card_list, f, indent=4, ensure_ascii=False)

                logger.info(f"[{self.group_id}] 已从文件中删除使用的信用卡信息，剩余 {len(card_list)} 张卡")

            return card_info

        except Exception as e:
            logger.error(f"[{self.group_id}] 读取信用卡信息失败: {e}")
            return None

    def fill_credit_card_form(self, card_info):
        """填充信用卡表单"""
        try:
            logger.info(f"[{self.group_id}] 开始填充信用卡表单")

            # 1. 填充卡号（使用人性化输入）
            card_number_input = self.page.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                if not self.human_like_input(card_number_input, card_info.get('CardNumber', ''), "卡号"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 未找到卡号输入框")
                return False

            # 模拟人类思考时间
            time.sleep(random.uniform(2.0, 4.0))

            # 2. 填充有效期（使用人性化输入）
            card_expiry_input = self.page.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                if not self.human_like_input(card_expiry_input, formatted_expiry, "有效期"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 未找到有效期输入框")
                return False

            # 模拟人类思考时间
            time.sleep(random.uniform(1.5, 3.0))

            # 3. 填充CVV（使用人性化输入）
            card_cvc_input = self.page.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                if not self.human_like_input(card_cvc_input, card_info.get('CVV', ''), "CVV"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 未找到CVV输入框")
                return False

            # 模拟人类思考时间
            time.sleep(random.uniform(1.5, 3.0))

            # 4. 填充姓名（使用人性化输入）
            billing_name_input = self.page.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                if not self.human_like_input(billing_name_input, card_info.get('Name', ''), "姓名"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 未找到姓名输入框")
                return False

            # 填充完核心信息后的等待
            time.sleep(random.uniform(2.0, 3.5))

            # 5. 选择国家 CN
            billing_country_select = self.page.ele("@id=billingCountry", timeout=10)
            if billing_country_select:
                # 尝试选择CN选项
                try:
                    billing_country_select.select.by_value('CN')
                    logger.info(f"[{self.group_id}] 已选择国家 CN")

                    # 触发change事件，确保省份下拉框更新
                    self.page.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                    logger.info(f"[{self.group_id}] 已触发国家选择change事件")

                except:
                    # 如果by_value失败，尝试by_text
                    try:
                        billing_country_select.select.by_text('China')
                        logger.info(f"[{self.group_id}] 已选择国家 China")

                        # 触发change事件
                        self.page.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                        logger.info(f"[{self.group_id}] 已触发国家选择change事件")

                    except:
                        logger.warning(f"[{self.group_id}] 选择国家失败，继续其他字段")
            else:
                logger.error(f"[{self.group_id}] 未找到国家选择框")
                return False

            # 等待省份下拉框加载
            time.sleep(3)

            # 6. 填充地址
            billing_address_input = self.page.ele("xpath://input[@id='billingAddressLine1']", timeout=10)
            if billing_address_input:
                billing_address_input.clear()
                billing_address_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充地址")
            else:
                logger.error(f"[{self.group_id}] 未找到地址输入框")
                return False

            time.sleep(1)

            # 7. 填充邮政编码
            billing_postal_input = self.page.ele("xpath://input[@id='billingPostalCode']", timeout=10)
            if billing_postal_input:
                billing_postal_input.clear()
                billing_postal_input.input("353000")
                logger.info(f"[{self.group_id}] 已填充邮政编码")
            else:
                logger.error(f"[{self.group_id}] 未找到邮政编码输入框")
                return False

            time.sleep(1)

            # 8. 填充城市
            billing_locality_input = self.page.ele("xpath://input[@id='billingLocality']", timeout=10)
            if billing_locality_input:
                billing_locality_input.clear()
                billing_locality_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充城市")
            else:
                logger.error(f"[{self.group_id}] 未找到城市输入框")
                return False

            time.sleep(1)

           

            # 10. 选择省份
            billing_admin_select = self.page.ele("@id=billingAdministrativeArea", timeout=10)
            if billing_admin_select:
                # 检查省份选项是否已加载
                logger.info(f"[{self.group_id}] 检查省份下拉框选项")
                try:
                    # 获取所有选项
                    options = billing_admin_select.eles("tag:option")
                    logger.info(f"[{self.group_id}] 省份下拉框共有 {len(options)} 个选项")

                    # 打印所有选项用于调试
                    for i, option in enumerate(options):
                        option_text = option.text.strip()
                        option_value = option.attr('value') or ''
                        logger.info(f"[{self.group_id}] 选项 {i}: text='{option_text}', value='{option_value}'")

                    # 尝试选择福建省
                    fujian_selected = False

                    # 方法1: 尝试按文本选择
                    try:
                        billing_admin_select.select.by_text('福建省')
                        logger.info(f"[{self.group_id}] 已选择省份 福建省 (按文本)")
                        fujian_selected = True
                    except Exception as e1:
                        logger.warning(f"[{self.group_id}] 按文本选择福建省失败: {e1}")

                        # 方法2: 尝试按部分文本匹配
                        try:
                            for option in options:
                                if '福建' in option.text:
                                    option.click()
                                    logger.info(f"[{self.group_id}] 已选择省份: {option.text} (按部分匹配)")
                                    fujian_selected = True
                                    break
                        except Exception as e2:
                            logger.warning(f"[{self.group_id}] 按部分匹配选择福建省失败: {e2}")

                    if not fujian_selected:
                        logger.warning(f"[{self.group_id}] 未能选择福建省，但继续执行")

                except Exception as e:
                    logger.warning(f"[{self.group_id}] 处理省份选择时出错: {e}")
            else:
                logger.error(f"[{self.group_id}] 未找到省份选择框")
                return False
             # 9. 填充区域
            time.sleep(1)
            billing_dependent_input = self.page.ele("xpath://input[@id='billingDependentLocality']", timeout=10)
            if billing_dependent_input:
                billing_dependent_input.clear()
                billing_dependent_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info(f"[{self.group_id}] 已填充区域")
            else:
                logger.error(f"[{self.group_id}] 未找到区域输入框")
                return False

            
            time.sleep(3)

            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 填充信用卡表单异常: {e}")
            return False

    def human_like_input(self, element, text, field_name=""):
        """模拟人类输入行为"""
        try:
            import random

            # 模拟鼠标移动到元素（先移动到附近，再移动到元素）
            try:
                # 获取元素位置
                rect = element.rect
                if rect:
                    # 先移动到元素附近的随机位置
                    nearby_x = rect.center_x + random.randint(-50, 50)
                    nearby_y = rect.center_y + random.randint(-30, 30)
                    self.page.actions.move_to(nearby_x, nearby_y)
                    time.sleep(random.uniform(0.1, 0.3))

                    # 再移动到元素中心
                    self.page.actions.move_to(rect.center_x, rect.center_y)
                    time.sleep(random.uniform(0.2, 0.5))
            except:
                # 如果鼠标移动失败，继续执行点击
                pass

            # 点击元素获得焦点
            element.click()
            time.sleep(random.uniform(0.3, 0.8))

            # 模拟选择所有内容并删除（更自然的清空方式）
            self.page.actions.key_down('ctrl').key_down('a').key_up('a').key_up('ctrl')
            time.sleep(random.uniform(0.1, 0.3))
            self.page.actions.key_down('Delete').key_up('Delete')
            time.sleep(random.uniform(0.2, 0.5))

            # 模拟逐字符输入
            text_str = str(text)
            for i, char in enumerate(text_str):
                element.input(char)

                # 模拟真实打字速度：数字较快，字母稍慢
                if char.isdigit():
                    delay = random.uniform(0.05, 0.12)
                else:
                    delay = random.uniform(0.08, 0.18)

                # 偶尔有较长的停顿（模拟思考或查看）
                if random.random() < 0.1:  # 10%概率
                    delay += random.uniform(0.3, 0.8)

                time.sleep(delay)

            # 输入完成后的短暂停顿
            time.sleep(random.uniform(0.5, 1.2))

            # 触发多个事件确保验证
            self.page.run_js("""
                arguments[0].dispatchEvent(new Event('input', { bubbles: true }));
                arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                arguments[0].blur();
            """, element)
            time.sleep(random.uniform(0.3, 0.6))

            logger.info(f"[{self.group_id}] 已人性化输入{field_name}: {text}")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 人性化输入{field_name}失败: {e}")
            return False

    def update_credit_card_core_info(self, card_info):
        """只更新信用卡的核心信息（卡号、有效期、CVV、姓名），使用人性化输入"""
        try:
            logger.info(f"[{self.group_id}] 开始更新信用卡核心信息（人性化模式）")

            # 1. 更新卡号
            card_number_input = self.page.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                if not self.human_like_input(card_number_input, card_info.get('CardNumber', ''), "卡号"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 未找到卡号输入框")
                return False

            # 随机等待时间，模拟人类思考
            time.sleep(random.uniform(1.5, 3.0))

            # 2. 更新有效期
            card_expiry_input = self.page.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self.format_expiry_date(card_info.get('Expiry', ''))
                if not self.human_like_input(card_expiry_input, formatted_expiry, "有效期"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 未找到有效期输入框")
                return False

            # 随机等待时间
            time.sleep(random.uniform(1.0, 2.5))

            # 3. 更新CVV
            card_cvc_input = self.page.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                if not self.human_like_input(card_cvc_input, card_info.get('CVV', ''), "CVV"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 未找到CVV输入框")
                return False

            # 随机等待时间
            time.sleep(random.uniform(1.0, 2.0))

            # 4. 更新姓名
            billing_name_input = self.page.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                if not self.human_like_input(billing_name_input, card_info.get('Name', ''), "姓名"):
                    return False
            else:
                logger.error(f"[{self.group_id}] 未找到姓名输入框")
                return False

            # 最后等待一下，让页面稳定
            time.sleep(random.uniform(2.0, 3.5))
            logger.info(f"[{self.group_id}] 信用卡核心信息更新完成（人性化模式）")
            return True

        except Exception as e:
            logger.error(f"[{self.group_id}] 更新信用卡核心信息异常: {e}")
            return False

    def submit_credit_card_form(self):
        """提交信用卡表单并处理人机验证"""
        try:
            logger.info(f"[{self.group_id}] 开始提交信用卡表单")

            # 点击提交按钮
            submit_button = self.page.ele("xpath://button[@data-testid='hosted-payment-submit-button']", timeout=10)
            if not submit_button:
                logger.error(f"[{self.group_id}] 未找到提交按钮")
                return False

            logger.info(f"[{self.group_id}] 找到提交按钮，点击")
            submit_button.click()
            time.sleep(5)

            # 检查是否出现人机验证 - 使用改进的检测方式
            hcaptcha_elements = [
                "@id=HCaptcha-root",
                "xpath://div[@id='h-captcha']",
                "xpath://iframe[contains(@src,'hcaptcha')]",
                "xpath://div[contains(@class,'h-captcha')]",
                "xpath://div[contains(@class,'hcaptcha')]"
            ]

            hcaptcha_found = False
            hcaptcha_root = None
            for selector in hcaptcha_elements:
                hcaptcha_root = self.page.ele(selector, timeout=3)
                if hcaptcha_root:
                    logger.info(f"[{self.group_id}] 检测到人机验证: {selector}")
                    hcaptcha_found = True
                    break

            if hcaptcha_found:
                logger.info(f"[{self.group_id}] 检测到人机验证，尝试处理")
                try:
                    # 直接使用Tab+空格方式，不依赖元素操作
                    logger.info(f"[{self.group_id}] 使用Tab+空格方式处理人机验证")

                    # 发送两次Tab键：第一次到关闭按钮，第二次到复选框，然后发送空格键进行点击
                    self.page.actions.key_down('TAB')
                    self.page.actions.key_up('TAB')
                    time.sleep(0.5)
                    self.page.actions.key_down('TAB')
                    self.page.actions.key_up('TAB')
                    time.sleep(1)
                    self.page.actions.key_down('space')
                    self.page.actions.key_up('space')
                    logger.info(f"[{self.group_id}] 已发送两次Tab和Space键")
                    time.sleep(2)

                    # 检测验证是否完成
                    def is_captcha_completed():
                        # 检查h-captcha-response是否有值
                        response_element = self.page.ele("xpath://textarea[@name='h-captcha-response']", timeout=1)
                        if response_element:
                            response_value = response_element.attr('value') or self.page.run_js("return arguments[0].value;", response_element)
                            if response_value and response_value.strip():
                                logger.info(f"[{self.group_id}] 检测到h-captcha-response有值，验证完成")
                                return True

                        # 检查g-recaptcha-response是否有值
                        recaptcha_element = self.page.ele("xpath://textarea[@name='g-recaptcha-response']", timeout=1)
                        if recaptcha_element:
                            recaptcha_value = recaptcha_element.attr('value') or self.page.run_js("return arguments[0].value;", recaptcha_element)
                            if recaptcha_value and recaptcha_value.strip():
                                logger.info(f"[{self.group_id}] 检测到g-recaptcha-response有值，验证完成")
                                return True

                        # 检查验证容器是否消失或状态改变
                        captcha_container = self.page.ele("xpath://div[contains(@class,'HCaptcha-container')]", timeout=1)
                        if not captcha_container:
                            logger.info(f"[{self.group_id}] 验证容器消失，验证完成")
                            return True

                        return False

                    # 等待人机验证完成（最多等待60秒）
                    logger.info(f"[{self.group_id}] 等待人机验证完成...")
                    for i in range(60):
                        time.sleep(1)

                        # 首先检查验证是否完成
                        if is_captcha_completed():
                            logger.info(f"[{self.group_id}] 人机验证已完成，等待页面跳转...")
                            # 验证完成后再等待一段时间让页面跳转
                            for j in range(15):
                                time.sleep(2)
                                current_url = self.page.url
                                if self.is_credit_card_binding_successful(current_url):
                                    logger.info(f"[{self.group_id}] 人机验证完成，信用卡绑定成功，已跳转到: {current_url}")
                                    return True
                            # 如果验证完成但没有跳转，说明绑卡失败
                            logger.warning(f"[{self.group_id}] 人机验证完成但未跳转，当前URL: {self.page.url}，绑卡失败")
                            return False

                        # 检查是否直接跳转了
                        current_url = self.page.url
                        if self.is_credit_card_binding_successful(current_url):
                            logger.info(f"[{self.group_id}] 人机验证完成，信用卡绑定成功，已跳转到: {current_url}")
                            return True

                    logger.warning(f"[{self.group_id}] 人机验证等待超时")
                    return False

                except Exception as e:
                    logger.error(f"[{self.group_id}] 处理人机验证异常: {e}")
                    return False
            else:
                logger.info(f"[{self.group_id}] 未检测到人机验证，检查是否绑定成功")
                time.sleep(3)

                # 检查是否绑定成功
                current_url = self.page.url
                if self.is_credit_card_binding_successful(current_url):
                    logger.info(f"[{self.group_id}] 信用卡绑定成功，已跳转到: {current_url}")
                    return True
                else:
                    logger.warning(f"[{self.group_id}] 信用卡绑定可能失败，当前URL: {current_url}")
                    return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 提交信用卡表单异常: {e}")
            return False

    def submit_credit_card_form_with_timeout(self):
        """提交信用卡表单并处理人机验证，带20秒超时检测"""
        try:
            logger.info(f"[{self.group_id}] 开始提交信用卡表单（带超时检测）")

            # 点击提交按钮
            submit_button = self.page.ele("xpath://button[@data-testid='hosted-payment-submit-button']", timeout=10)
            if not submit_button:
                logger.error(f"[{self.group_id}] 未找到提交按钮")
                return False

            logger.info(f"[{self.group_id}] 找到提交按钮，点击")
            submit_button.click()

            # 记录提交时间
            submit_time = time.time()
            time.sleep(5)

            # 检查是否出现人机验证 - 使用改进的检测方式
            hcaptcha_elements = [
                "xpath://div[@id='h-captcha']",
                "xpath://iframe[contains(@src,'hcaptcha')]",
                "xpath://div[contains(@class,'h-captcha')]",
                "xpath://div[contains(@class,'hcaptcha')]",
                "@id=HCaptcha-root"  # 保留原有的检测方式
            ]

            hcaptcha_found = False
            hcaptcha_root = None
            for selector in hcaptcha_elements:
                hcaptcha_root = self.page.ele(selector, timeout=3)
                if hcaptcha_root:
                    logger.info(f"[{self.group_id}] 检测到人机验证: {selector}")
                    hcaptcha_found = True
                    break

            if hcaptcha_found:
                logger.info(f"[{self.group_id}] 检测到人机验证，尝试处理")
                try:
                    # 直接使用Tab+空格方式，不依赖元素操作
                    logger.info(f"[{self.group_id}] 使用Tab+空格方式处理人机验证")

                    # 发送两次Tab键：第一次到关闭按钮，第二次到复选框，然后发送空格键进行点击
                    self.page.actions.key_down('TAB')
                    self.page.actions.key_up('TAB')
                    time.sleep(0.5)
                    self.page.actions.key_down('TAB')
                    self.page.actions.key_up('TAB')
                    time.sleep(1)
                    self.page.actions.key_down('space')
                    self.page.actions.key_up('space')
                    logger.info(f"[{self.group_id}] 已发送两次Tab和Space键")
                    time.sleep(2)

                    # 检测验证是否完成
                    def is_captcha_completed():
                        # 检查h-captcha-response是否有值
                        response_element = self.page.ele("xpath://textarea[@name='h-captcha-response']", timeout=1)
                        if response_element:
                            response_value = response_element.attr('value') or self.page.run_js("return arguments[0].value;", response_element)
                            if response_value and response_value.strip():
                                logger.info(f"[{self.group_id}] 检测到h-captcha-response有值，验证完成")
                                return True

                        # 检查g-recaptcha-response是否有值
                        recaptcha_element = self.page.ele("xpath://textarea[@name='g-recaptcha-response']", timeout=1)
                        if recaptcha_element:
                            recaptcha_value = recaptcha_element.attr('value') or self.page.run_js("return arguments[0].value;", recaptcha_element)
                            if recaptcha_value and recaptcha_value.strip():
                                logger.info(f"[{self.group_id}] 检测到g-recaptcha-response有值，验证完成")
                                return True

                        # 检查验证容器是否消失或状态改变
                        captcha_container = self.page.ele("xpath://div[contains(@class,'HCaptcha-container')]", timeout=1)
                        if not captcha_container:
                            logger.info(f"[{self.group_id}] 验证容器消失，验证完成")
                            return True

                        return False

                    # 等待人机验证完成
                    logger.info(f"[{self.group_id}] 等待人机验证完成...")
                    for i in range(60):
                        time.sleep(1)
                        current_time = time.time()
                        elapsed_time = current_time - submit_time

                        # 首先检查验证是否完成
                        if is_captcha_completed():
                            logger.info(f"[{self.group_id}] 人机验证已完成，等待页面跳转...")
                            # 验证完成后再等待一段时间让页面跳转
                            for j in range(15):
                                time.sleep(2)
                                current_url = self.page.url
                                if self.is_credit_card_binding_successful(current_url):
                                    logger.info(f"[{self.group_id}] 人机验证完成，信用卡绑定成功，已跳转到: {current_url}")
                                    return True
                            # 如果验证完成但没有跳转，说明绑卡失败
                            logger.warning(f"[{self.group_id}] 人机验证完成但未跳转，当前URL: {self.page.url}，绑卡失败")
                            return False

                        # 检查是否已跳转到cursor.com页面
                        current_url = self.page.url
                        if self.is_credit_card_binding_successful(current_url):
                            logger.info(f"[{self.group_id}] 人机验证完成，信用卡绑定成功，已跳转到: {current_url}")
                            return True

                        # 检查是否超过20秒还在支付页面
                        if elapsed_time > 20 and self.is_still_on_payment_page(current_url):
                            logger.warning(f"[{self.group_id}] 提交后超过20秒仍在支付页面，判断为绑卡失败")
                            return False

                    logger.warning(f"[{self.group_id}] 人机验证等待超时")
                    return False

                except Exception as e:
                    logger.error(f"[{self.group_id}] 处理人机验证异常: {e}")
                    return False
            else:
                logger.info(f"[{self.group_id}] 未检测到人机验证，等待20秒检查绑定结果")

                # 等待20秒检查结果
                for i in range(20):
                    time.sleep(1)
                    current_url = self.page.url

                    # 检查是否绑定成功
                    if self.is_credit_card_binding_successful(current_url):
                        logger.info(f"[{self.group_id}] 信用卡绑定成功，已跳转到: {current_url}")
                        return True

                # 20秒后仍在支付页面，判断为失败
                current_url = self.page.url
                if self.is_still_on_payment_page(current_url):
                    logger.warning(f"[{self.group_id}] 20秒后仍在支付页面，判断为绑卡失败: {current_url}")
                    return False
                else:
                    logger.info(f"[{self.group_id}] 页面已跳转，但未确认绑定成功: {current_url}")
                    return False

        except Exception as e:
            logger.error(f"[{self.group_id}] 提交信用卡表单异常: {e}")
            return False

    def is_credit_card_binding_successful(self, current_url):
        """判断信用卡绑定是否成功"""
        try:
            # 检查URL是否包含cursor.com且不包含payment相关字段
            if "cursor.com" in current_url and all(keyword not in current_url.lower() for keyword in ["payment", "checkout", "stripe"]):
                logger.info(f"[{self.group_id}] URL检查通过，信用卡绑定成功")
                return True
            else:
                logger.info(f"[{self.group_id}] URL检查未通过: {current_url}")
                return False
        except Exception as e:
            logger.error(f"[{self.group_id}] 检查绑定状态时出错: {e}")
            return False

    def is_still_on_payment_page(self, current_url):
        """判断是否仍在支付页面"""
        try:
            # 检查URL是否包含支付相关关键词
            payment_keywords = ["payment", "checkout", "stripe"]
            is_payment_page = any(keyword in current_url.lower() for keyword in payment_keywords)

            if is_payment_page:
                logger.info(f"[{self.group_id}] 仍在支付页面: {current_url}")
                return True
            else:
                logger.info(f"[{self.group_id}] 已离开支付页面: {current_url}")
                return False
        except Exception as e:
            logger.error(f"[{self.group_id}] 检查支付页面状态时出错: {e}")
            return True  # 出错时保守判断为仍在支付页面

    def navigate_to_login_deep_control(self):
        """跳转到登录确认页面并完成token获取"""
        try:
            logger.info(f"[{self.group_id}] 开始跳转到登录确认页面")

            # 构建登录确认页面URL
            login_url = f"https://cursor.com/cn/loginDeepControl?challenge={self.challenge}&uuid={self.uuid}&mode=login"
            logger.info(f"[{self.group_id}] 跳转到登录确认页面: {login_url}")

            self.page.get(login_url)
            time.sleep(5)

            # 查找并点击登录按钮
            login_button = self.page.ele("xpath://button[.//span[contains(text(), 'Log In')]]", timeout=10)
            if login_button:
                logger.info(f"[{self.group_id}] 找到登录按钮，点击确认")
                login_button.click()
                time.sleep(5)
            else:
                logger.warning(f"[{self.group_id}] 未找到登录按钮")

            # 获取并提交token
            self.get_and_submit_token()

        except Exception as e:
            logger.error(f"[{self.group_id}] 跳转到登录确认页面异常: {e}")
            # 记录失败的邮箱账号
            if self.email:
                self.write_failed_email(self.email, f"跳转登录确认页面异常: {str(e)}")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, f"跳转登录确认页面异常: {str(e)}")

    def get_and_submit_token(self):
        """获取并提交token"""
        try:
            # 检查是否跳转到cursor.com
            current_url = self.page.url

            # 获取token
            time.sleep(5)
            token = self.get_token()
            if not token:
                logger.error(f"[{self.group_id}] 获取token失败")
                # 记录失败的邮箱账号
                if self.email:
                    self.write_failed_email(self.email, "获取token失败")
                if self.error_callback:
                    self.error_callback(self.group_id, self.email, "获取token失败")
                return

            # 获取accessToken和refreshToken
            access_token, refresh_token = self.get_access_token_and_refresh_token(token)

            if access_token and refresh_token:
                logger.info(f"[{self.group_id}] 成功获取accessToken和refreshToken")
                # 提交token和tokens到API (异步)
                self.submit_token(self.email, token, access_token, refresh_token)
                logger.info(f"[{self.group_id}] 注册流程完成: {self.email} (token和tokens提交已触发异步处理)")
            else:
                logger.warning(f"[{self.group_id}] 未能获取accessToken和refreshToken，仅提交原token")
                # 仅提交原token到API (异步)
                self.submit_token(self.email, token)
                logger.info(f"[{self.group_id}] 注册流程完成: {self.email} (仅token提交已触发异步处理)")

        except Exception as e:
            logger.error(f"[{self.group_id}] token处理过程中出错: {e}")
        finally:
            # 根据代理配置决定是否重启浏览器
            if USE_PROXY and USE_PROXY_FULL_PROCESS:
                # 全程代理模式：不关闭浏览器，保持当前状态继续下一轮注册
                logger.info(f"[{self.group_id}] 全程代理模式，保持浏览器开启状态，准备下一轮注册")
                # 重置当前尝试次数，为下一轮注册做准备
                self.current_attempt = 0
            else:
                # 其他模式：清理资源并重启浏览器，为下一轮注册做准备
                try:
                    if USE_PROXY and not USE_PROXY_FULL_PROCESS:
                        logger.info(f"[{self.group_id}] 部分代理模式，清理资源并准备下一轮（无代理启动）")
                        restart_with_proxy = False
                    else:
                        logger.info(f"[{self.group_id}] 无代理模式，清理资源并准备下一轮")
                        restart_with_proxy = False

                    # 使用cleanup彻底清理当前浏览器
                    self.cleanup()

                    # 重启浏览器
                    time.sleep(1)  # 等待资源释放
                    if self.setup_browser(use_proxy=restart_with_proxy):
                        proxy_status = "代理模式" if restart_with_proxy else "无代理模式"
                        logger.info(f"[{self.group_id}] 已重启浏览器为{proxy_status}，准备下一轮注册")
                    else:
                        logger.error(f"[{self.group_id}] 重启浏览器失败")
                except Exception as e:
                    logger.error(f"[{self.group_id}] 重置浏览器过程中出错: {e}")
                    # 即使出错也尝试再次启动
                    try:
                        time.sleep(2)  # 多等待一会儿
                        self.setup_browser(use_proxy=False)
                        logger.info(f"[{self.group_id}] 二次尝试重启浏览器成功")
                    except:
                        logger.error(f"[{self.group_id}] 二次尝试重启浏览器也失败，将在下一轮注册时重试")
        
    def run(self):
        """运行注册流程"""
        # 根据代理配置初始化浏览器
        if USE_PROXY and USE_PROXY_FULL_PROCESS:
            # 全程代理模式：先获取代理，然后启用代理初始化浏览器
            logger.info(f"[{self.group_id}] 全程代理模式，正在获取代理...")
            if not self.get_new_proxy():
                logger.error(f"[{self.group_id}] 获取代理失败，线程终止")
                if self.error_callback:
                    self.error_callback(self.group_id, "None", "获取代理失败")
                return

            if not self.setup_browser(use_proxy=True):
                logger.error(f"[{self.group_id}] 浏览器设置失败，线程终止")
                if self.error_callback:
                    self.error_callback(self.group_id, "None", "浏览器初始化失败")
                return
            logger.info(f"[{self.group_id}] 浏览器初始化成功（全程代理模式），准备开始注册流程")
        else:
            # 其他模式：无代理初始化浏览器
            # 包括：USE_PROXY=False 或 USE_PROXY=True但USE_PROXY_FULL_PROCESS=False
            if not self.setup_browser(use_proxy=False):
                logger.error(f"[{self.group_id}] 浏览器设置失败，线程终止")
                if self.error_callback:
                    self.error_callback(self.group_id, "None", "浏览器初始化失败")
                return

            if USE_PROXY and not USE_PROXY_FULL_PROCESS:
                logger.info(f"[{self.group_id}] 浏览器初始化成功（部分代理模式），准备开始注册流程")
            else:
                logger.info(f"[{self.group_id}] 浏览器初始化成功（无代理模式），准备开始注册流程")
        
        # 运行指定次数的注册流程
        registration_count = 0
        
        try:
            while registration_count < DEFAULT_REGISTRATION_COUNT:
                registration_count += 1
                self.registration_count = registration_count  # 更新实例计数器

                # 记录开始时间
                registration_start_time = time.time()
                logger.info(f"[{self.group_id}] 开始第 {registration_count}/{DEFAULT_REGISTRATION_COUNT} 次注册流程")

                # 检查是否需要重启浏览器（防止资源累积）
                if not self.restart_browser_if_needed():
                    logger.error(f"[{self.group_id}] 浏览器重启失败，跳过本次注册")
                    continue

                # 重置当前尝试次数
                self.current_attempt = 0

                # 如果是第二次及以后的注册，且使用代理，重置代理状态以避免认证问题
                if registration_count > 1 and USE_PROXY:
                    logger.info(f"[{self.group_id}] 第{registration_count}次注册，重置代理状态以避免认证问题")
                    if not self.reset_proxy_state():
                        logger.warning(f"[{self.group_id}] 代理状态重置失败，继续使用当前代理")
                    else:
                        logger.info(f"[{self.group_id}] 代理状态重置成功")

                # 生成新邮箱
                pool_size = self.get_pool_size()
                logger.info(f"[{self.group_id}] 当前邮箱池大小: {pool_size}")

                self.email = self.generate_email()
                if not self.email:
                    logger.error(f"[{self.group_id}] 获取邮箱失败，跳过本次注册")
                    continue

                logger.info(f"[{self.group_id}] 准备注册新账号: {self.email}")
                logger.info(f"[{self.group_id}] 注册后邮箱池大小: {self.get_pool_size()}")

                # 生成OAuth参数
                if not self.generate_oauth_params():
                    logger.error(f"[{self.group_id}] 生成OAuth参数失败，跳过本次注册")
                    continue

                # 构建state参数
                state_data = {
                    "returnTo": f"https://cursor.com/cn/loginDeepControl?challenge={self.challenge}&uuid={self.uuid}&mode=login"
                }
                state_json = json.dumps(state_data)
                encoded_state = quote(state_json)

                # 加载注册页面
                encoded_email = quote(self.email)
                url = SIGNUP_URL.format(encoded_email, encoded_state)
                logger.info(f"[{self.group_id}] 访问注册URL: {url}")
                self.page.get(url)
                
                # 开始处理注册
                time.sleep(2)
                self.process_registration()
                
                # 记录结束时间和耗时
                registration_end_time = time.time()
                elapsed_time = registration_end_time - registration_start_time
                logger.info(f"[{self.group_id}] 完成注册流程: {self.email} - 耗时: {elapsed_time:.2f} 秒")
                
                # 根据注册耗时动态调整休息时间
                # if elapsed_time >= 90:
                #     rest_time = 2
                # else:
                #     rest_time = 92 - elapsed_time
                    
                # logger.info(f"[{self.group_id}] 注册耗时 {elapsed_time:.2f} 秒，休息 {rest_time:.2f} 秒")
                time.sleep(2)
            
            logger.info(f"[{self.group_id}] 完成全部 {DEFAULT_REGISTRATION_COUNT} 次注册流程")
        finally:
            # 所有注册完成后，清理资源
            self.cleanup()
            logger.info(f"[{self.group_id}] 线程资源已清理")

    def is_proxy_expired(self):
        """检查当前代理是否已过期"""
        if not self.proxy_start_time:
            return True  # 如果没有开始时间，视为已过期
            
        current_time = time.time()
        elapsed_minutes = (current_time - self.proxy_start_time) / 60
        
        if elapsed_minutes >= PROXY_LIFETIME_MINUTES:
            logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，超过设定的 {PROXY_LIFETIME_MINUTES} 分钟有效期")
            return True
            
        logger.info(f"[{self.group_id}] 当前代理已使用 {elapsed_minutes:.2f} 分钟，还在有效期内 ({PROXY_LIFETIME_MINUTES} 分钟)")
        return False


class RegistrationManager:
    def __init__(self, num_threads=DEFAULT_THREADS, headless=False):
        self.num_threads = min(num_threads, MAX_THREADS)
        self.headless = headless
        self.registers = []
        self.successful = []
        self.failed = []
        
    def on_registration_finished(self, group_id, email, token):
        self.successful.append({
            "group_id": group_id,
            "email": email,
            "token": token,
            "timestamp": datetime.now().isoformat()
        })
        logger.info(f"注册成功: {email} (组: {group_id})")
            
#         # 将成功信息保存到文件
#         with open("successful_registrations.txt", "a", encoding="utf-8") as f:
#             f.write(f"{datetime.now().isoformat()} | {email} | {token}\n")
            
    def on_registration_error(self, group_id, email, error):
        self.failed.append({
            "group_id": group_id,
            "email": email,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        logger.error(f"注册失败: {email} (组: {group_id}): {error}")

        # 注意：失败邮箱的记录已经在DrissionCursorRegister类的write_failed_email方法中处理
        # 这里不再重复记录，避免重复写入
        
    def start(self):
        """开始注册进程"""
        logger.info(f"启动 {self.num_threads} 个注册线程")
        
        # 清理浏览器进程
        cleanup_chrome_processes()
        
        for i in range(self.num_threads):
            group_id = f"{i}"  # 简化为数字索引
            register = DrissionCursorRegister(group_id, self.headless)
            
            # 设置回调
            register.set_callbacks(self.on_registration_finished, self.on_registration_error)
            
            # 创建线程运行注册实例
            thread = threading.Thread(target=register.run, daemon=True)
            
            # 保存实例
            self.registers.append(register)
            
            # 启动线程
            thread.start()
            
        logger.info("所有注册线程已启动")


def cleanup_chrome_processes():
    """清理Chrome进程"""
    global _chrome_process_ids
    
    if not _chrome_process_ids:
        logger.info("没有需要清理的Chrome进程...")
        return
        
    logger.info(f"清理 {len(_chrome_process_ids)} 个Chrome进程...")
    try:
        if os.name == 'nt':
            for pid in _chrome_process_ids:
                try:
                    os.system(f'taskkill /F /PID {pid} /T 2>nul')
                except:
                    pass
        else:
            for pid in _chrome_process_ids:
                try:
                    os.kill(pid, signal.SIGTERM)
                except:
                    pass
        _chrome_process_ids = []
    except Exception as e:
        logger.error(f"清理进程时出错: {e}")


def cleanup_async_tasks():
    """清理异步信用卡绑定任务"""
    global _async_credit_card_tasks, _async_task_lock

    with _async_task_lock:
        if _async_credit_card_tasks:
            logger.info(f"清理 {len(_async_credit_card_tasks)} 个异步信用卡绑定任务")
            _async_credit_card_tasks.clear()
        else:
            logger.info("没有需要清理的异步任务")

def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info("接收到退出信号，正在清理资源...")
    cleanup_chrome_processes()
    cleanup_async_tasks()
    sys.exit(0)


def main():
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 全局变量声明
    global USE_PROXY
    global DEFAULT_REGISTRATION_COUNT
    global PROXY_LIFETIME_MINUTES
    global EMAIL_SOURCE_TYPE

    # 命令行参数
    parser = argparse.ArgumentParser(description="Cursor自动注册工具")
    parser.add_argument("-t", "--threads", type=int, default=DEFAULT_THREADS,
                        help=f"并行注册线程数量 (最大 {MAX_THREADS})")
    parser.add_argument("--headless", action="store_true", help="使用无头模式运行")
    parser.add_argument("--proxy", action="store_true", help="启用代理")
    parser.add_argument("-n", "--count", type=int, default=DEFAULT_REGISTRATION_COUNT,
                        help="每个线程的注册次数")
    parser.add_argument("-p", "--proxy-lifetime", type=int, default=PROXY_LIFETIME_MINUTES,
                        help="每个代理的有效时间（分钟）")
    parser.add_argument("--email-source", choices=[EMAIL_SOURCE_HUMKT, EMAIL_SOURCE_TXT],
                        default=EMAIL_SOURCE_HUMKT,
                        help=f"邮箱获取来源: {EMAIL_SOURCE_HUMKT}=从humkt API获取, {EMAIL_SOURCE_TXT}=从TXT文件读取")
    parser.add_argument("--email-file", type=str, default="gmail_accounts.txt",
                        help="当使用TXT文件读取时的邮箱文件路径")
    args = parser.parse_args()
    
    # 设置代理参数
    USE_PROXY = True

    # 设置邮箱获取来源
    EMAIL_SOURCE_TYPE = args.email_source
    logger.info(f"邮箱获取来源已设置为: {EMAIL_SOURCE_TYPE}")

    # 注意：现在邮箱获取逻辑已改为直接从TXT文件读取，不再预加载到邮箱池
    logger.info(f"邮箱获取策略：优先从gmail_accounts.txt读取，然后从API获取")

    # 设置每个代理的有效时间
    if args.proxy_lifetime and args.proxy_lifetime > 0:
        PROXY_LIFETIME_MINUTES = args.proxy_lifetime
    logger.info(f"已设置每个代理最多使用 {PROXY_LIFETIME_MINUTES} 分钟")

    # 设置注册次数
    if args.count and args.count > 0:
        DEFAULT_REGISTRATION_COUNT = args.count
        logger.info(f"每个线程的注册次数已设置为: {DEFAULT_REGISTRATION_COUNT}")
        
    # 启动注册管理器
    manager = RegistrationManager(args.threads, args.headless)
    manager.start()

    # 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到用户中断，正在退出...")
        cleanup_chrome_processes()


if __name__ == "__main__":
    main() 