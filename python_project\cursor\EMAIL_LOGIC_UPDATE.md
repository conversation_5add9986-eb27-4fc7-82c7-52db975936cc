# 邮箱获取逻辑调整说明

## 调整需求
根据用户要求，调整邮箱获取逻辑：
1. 优先从 `gmail_accounts.txt` 获取邮箱
2. 使用后从TXT文件中移除
3. 没有邮箱时才从API获取
4. API获取到的未使用邮箱加入到TXT文件

## 实现的功能

### 1. 智能邮箱获取策略
- **优先级顺序**: TXT文件 → 邮箱池 → API获取
- **自动管理**: 使用后自动从TXT文件移除
- **资源复用**: API获取的多余邮箱自动保存到TXT文件

### 2. 新增方法

#### `get_email_from_txt_file()`
- 从TXT文件读取第一个可用邮箱
- 自动从文件中移除已使用的邮箱
- 保留注释行和格式错误的行
- 支持自定义文件路径

#### `save_emails_to_txt_file()`
- 将邮箱列表追加到TXT文件
- 保留现有文件内容
- 自动过滤格式错误的邮箱
- 详细的日志记录

### 3. 修改的方法

#### `generate_email()`
- 重新设计获取逻辑
- 优先从TXT文件获取
- 备选从邮箱池获取
- 最后从API获取

#### `buy_new_emails()`
- 取第一个邮箱用于当前注册
- 剩余邮箱保存到TXT文件
- 不再使用邮箱池存储

#### `delayed_order_check()`
- 延迟获取的新邮箱保存到TXT文件
- 不再添加到邮箱池

### 4. 移除的功能
- 移除了main函数中的邮箱预加载逻辑
- 移除了 `load_emails_from_txt_source()` 方法
- 简化了EMAIL_SOURCE相关的配置

## 工作流程

### 邮箱获取流程
```
开始注册
    ↓
检查 gmail_accounts.txt
    ↓
有邮箱? → 是 → 使用并从文件移除
    ↓ 否
检查邮箱池
    ↓
有邮箱? → 是 → 使用邮箱
    ↓ 否
从API获取邮箱
    ↓
使用第一个邮箱
    ↓
剩余邮箱保存到TXT文件
```

### 延迟邮箱处理
```
API获取邮箱后
    ↓
启动30秒延迟线程
    ↓
再次查询订单
    ↓
发现新邮箱? → 是 → 保存到TXT文件
    ↓ 否
结束
```

## 文件操作

### TXT文件格式
```
# Gmail账号文件
# 格式：邮箱|密码
<EMAIL>|password123
<EMAIL>|password456
```

### 文件操作特点
- **读取**: 逐行解析，支持注释行
- **写入**: 追加模式，保留现有内容
- **移除**: 重写文件，移除已使用的邮箱
- **错误处理**: 格式错误的行会被保留

## 优势

1. **资源利用最大化**: 避免重复获取邮箱
2. **成本控制**: 减少API调用次数
3. **灵活性**: 支持手动添加邮箱到TXT文件
4. **可靠性**: 多层备选机制确保总能获取到邮箱
5. **可维护性**: 清晰的文件操作和日志记录

## 注意事项

1. **文件权限**: 确保程序对TXT文件有读写权限
2. **并发安全**: 多线程环境下的文件操作已考虑线程安全
3. **格式要求**: TXT文件中的邮箱必须是 `邮箱|密码` 格式
4. **备份建议**: 建议定期备份邮箱文件

## 兼容性

- 保留了原有的邮箱池机制作为备选
- 保留了API获取机制
- 向后兼容现有的配置参数
- 不影响其他功能模块
