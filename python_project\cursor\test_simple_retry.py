#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import logging
from DrissionPage import ChromiumPage, ChromiumOptions

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_card_selection():
    """测试银行卡选择逻辑"""
    try:
        # 创建浏览器
        co = ChromiumOptions()
        co.headless(False)
        page = ChromiumPage(co)
        
        # 使用一个新的Stripe测试URL
        test_url = "https://checkout.stripe.com/c/pay/cs_test_a1example"
        
        logger.info("访问Stripe测试页面...")
        page.get("https://stripe.com/docs/testing")
        time.sleep(3)
        
        logger.info("请手动导航到一个Stripe支付页面进行测试...")
        logger.info("或者提供一个有效的Stripe支付URL")
        
        # 等待用户手动导航
        input("请在浏览器中导航到Stripe支付页面，然后按Enter继续...")
        
        # 测试银行卡选择逻辑
        logger.info("开始测试银行卡选择逻辑...")
        
        # 查找Continue按钮
        continue_button = page.ele("xpath://button[text()='Continue']", timeout=5)
        if continue_button:
            logger.info("找到Continue按钮，点击")
            continue_button.click()
            time.sleep(3)
        
        # 查找信用卡选项按钮
        card_button = page.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
        if card_button:
            logger.info("找到信用卡选项按钮，点击")
            card_button.click()
            time.sleep(3)
        else:
            logger.warning("未找到信用卡选项按钮")
        
        # 测试银行卡支付方式选择
        logger.info("测试银行卡支付方式选择...")
        card_option_selectors = [
            "xpath://input[@id='payment-method-accordion-item-title-card']",
            "xpath://label[contains(text(),'Card')]",
            "xpath://button[contains(text(),'Card')]",
            "xpath://*[@data-testid='payment-method-card']",
            "xpath://div[contains(@class,'payment-method')]//span[contains(text(),'Card')]",
            "xpath://div[contains(@class,'accordion')]//span[contains(text(),'Card')]"
        ]

        card_option_found = False
        for i, selector in enumerate(card_option_selectors):
            logger.info(f"尝试选择器 {i+1}: {selector}")
            card_option = page.ele(selector, timeout=3)
            if card_option:
                logger.info(f"找到银行卡支付选项: {selector}")
                try:
                    card_option.click()
                    logger.info("成功点击银行卡支付选项")
                    card_option_found = True
                    time.sleep(2)
                    break
                except Exception as e:
                    logger.warning(f"点击银行卡支付选项失败: {e}")
            else:
                logger.info(f"选择器未找到元素: {selector}")

        if not card_option_found:
            logger.warning("未找到银行卡支付方式选项，可能已经默认选中")
        
        # 检查信用卡输入框
        logger.info("检查信用卡输入框...")
        card_selectors = [
            "xpath://input[@id='cardNumber']",
            "xpath://input[@name='cardnumber']",
            "xpath://input[@placeholder*='card number']",
            "xpath://input[@placeholder*='Card number']"
        ]
        
        card_input_found = False
        for i, selector in enumerate(card_selectors):
            logger.info(f"尝试输入框选择器 {i+1}: {selector}")
            card_input = page.ele(selector, timeout=3)
            if card_input:
                logger.info(f"找到信用卡输入框: {selector}")
                card_input_found = True
                break
            else:
                logger.info(f"输入框选择器未找到元素: {selector}")
        
        if not card_input_found:
            logger.error("未找到信用卡输入框！")
            
            # 尝试查找iframe
            logger.info("尝试查找Stripe iframe...")
            iframes = page.eles("xpath://iframe")
            logger.info(f"找到 {len(iframes)} 个iframe")
            
            for i, iframe in enumerate(iframes):
                src = iframe.attr('src') or ''
                name = iframe.attr('name') or ''
                logger.info(f"iframe {i+1}: src='{src}', name='{name}'")
        else:
            logger.info("成功找到信用卡输入框！")
        
        # 模拟刷新重试
        logger.info("模拟刷新重试...")
        page.refresh()
        time.sleep(5)
        
        logger.info("刷新后重新测试...")
        
        # 重新查找Continue按钮
        continue_button = page.ele("xpath://button[text()='Continue']", timeout=5)
        if continue_button:
            logger.info("刷新后找到Continue按钮，点击")
            continue_button.click()
            time.sleep(3)
        
        # 重新查找信用卡选项按钮
        card_button = page.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
        if card_button:
            logger.info("刷新后找到信用卡选项按钮，点击")
            card_button.click()
            time.sleep(3)
            
            # 重新选择银行卡支付方式
            logger.info("刷新后重新选择银行卡支付方式...")
            for selector in card_option_selectors:
                card_option = page.ele(selector, timeout=3)
                if card_option:
                    logger.info(f"刷新后找到银行卡支付选项: {selector}")
                    try:
                        card_option.click()
                        logger.info("刷新后成功点击银行卡支付选项")
                        time.sleep(2)
                        break
                    except Exception as e:
                        logger.warning(f"刷新后点击银行卡支付选项失败: {e}")
            
            # 重新检查信用卡输入框
            logger.info("刷新后检查信用卡输入框...")
            for selector in card_selectors:
                card_input = page.ele(selector, timeout=3)
                if card_input:
                    logger.info(f"刷新后找到信用卡输入框: {selector}")
                    logger.info("刷新重试测试成功！")
                    break
            else:
                logger.error("刷新后仍未找到信用卡输入框！")
        
        # 保持浏览器打开
        logger.info("测试完成，浏览器将保持打开状态...")
        input("按Enter键关闭浏览器...")
        
        page.quit()
        
    except Exception as e:
        logger.error(f"测试异常: {e}")

if __name__ == "__main__":
    test_card_selection()
