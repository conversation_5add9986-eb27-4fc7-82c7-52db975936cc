#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新架构的脚本
验证API请求和Token检测分离的效果
"""

import sys
import time
import logging
from drission_cursor_reg import Email2925Monitor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_api_request_only():
    """测试纯API请求（不依赖token状态）"""
    print("🧪 测试1: 纯API请求")
    
    monitor = Email2925Monitor(headless=False)
    
    try:
        # 启动浏览器并登录
        if not monitor.start_monitoring():
            print("❌ 启动失败")
            return False
        
        print("✅ 登录成功，开始测试API请求...")
        
        # 测试多次API请求
        for i in range(3):
            print(f"\n--- API请求 {i+1} ---")
            success = monitor.get_email_code_new()
            print(f"API请求结果: {'成功' if success else '失败'}")
            time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        monitor.cleanup()

def test_token_maintenance():
    """测试Token维护功能"""
    print("\n🧪 测试2: Token维护")
    
    monitor = Email2925Monitor(headless=False)
    
    try:
        # 启动浏览器并登录
        if not monitor.start_monitoring():
            print("❌ 启动失败")
            return False
        
        print("✅ 登录成功，开始测试Token维护...")
        
        # 显示当前token状态
        print(f"当前token: {monitor.jwt_token[:20] if monitor.jwt_token else 'None'}...")
        print(f"token有效性: {monitor.is_token_valid()}")
        
        # 测试从页面刷新token
        print("\n--- 测试从页面刷新token ---")
        success = monitor.refresh_token_from_page()
        print(f"刷新结果: {'成功' if success else '失败'}")
        print(f"刷新后token: {monitor.jwt_token[:20] if monitor.jwt_token else 'None'}...")
        
        # 测试token维护任务
        print("\n--- 测试token维护任务 ---")
        monitor.token_maintenance_task()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        monitor.cleanup()

def test_separated_architecture():
    """测试分离架构（短时间运行）"""
    print("\n🧪 测试3: 分离架构（30秒测试）")
    
    monitor = Email2925Monitor(headless=False)
    
    try:
        # 启动浏览器并登录
        if not monitor.start_monitoring():
            print("❌ 启动失败")
            return False
        
        print("✅ 登录成功，开始30秒分离架构测试...")
        
        # 模拟分离运行30秒
        start_time = time.time()
        api_count = 0
        token_check_count = 0
        last_token_check = 0
        
        while time.time() - start_time < 30:  # 运行30秒
            current_time = time.time()
            
            # API请求（每5秒一次）
            if api_count == 0 or current_time - start_time >= api_count * 5:
                api_count += 1
                print(f"\n[{api_count}] 执行API请求...")
                success = monitor.get_email_code_new()
                print(f"API结果: {'成功' if success else '失败'}")
            
            # Token检测（每10秒一次）
            if current_time - last_token_check >= 10:
                token_check_count += 1
                print(f"\n[{token_check_count}] 执行Token检测...")
                monitor.token_maintenance_task()
                last_token_check = current_time
            
            time.sleep(1)
        
        print(f"\n✅ 测试完成: {api_count}次API请求, {token_check_count}次Token检测")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        monitor.cleanup()

def main():
    """主测试函数"""
    print("=" * 60)
    print("           新架构测试程序")
    print("=" * 60)
    print("测试内容:")
    print("1. 纯API请求（不依赖token状态）")
    print("2. Token维护功能")
    print("3. 分离架构运行（30秒）")
    print("=" * 60)
    
    # 执行测试
    results = []
    
    # 测试1: 纯API请求
    results.append(test_api_request_only())
    
    # 测试2: Token维护
    results.append(test_token_maintenance())
    
    # 测试3: 分离架构
    results.append(test_separated_architecture())
    
    # 显示结果
    print("\n" + "=" * 60)
    print("           测试结果")
    print("=" * 60)
    test_names = ["纯API请求", "Token维护", "分离架构"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    print(f"\n总结: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！新架构工作正常")
    else:
        print("⚠️  部分测试失败，需要检查问题")

if __name__ == "__main__":
    main()
