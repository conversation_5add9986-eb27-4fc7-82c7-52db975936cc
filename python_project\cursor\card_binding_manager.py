from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import threading
import logging
import sys
import random
import psutil
import signal
import uuid
import base64
import hashlib
import secrets
import json
import requests
from urllib.parse import quote

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("card_binding.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# API配置（与主程序保持一致）
API_BASE_URL = "http://119.29.20.123:8080/admin-api"
TOKEN_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/saveRecord?token=fe84864d7d354b53ae65e9fee25b0067"

class CardBindingManager:
    """独立的绑卡任务管理器"""

    def __init__(self):
        self.running = False
        self.worker_thread = None
        self.card_accounts_file = "card_account.txt"
        self.scan_interval = 5  # 扫描间隔（秒）
        self.process_interval = 5  # 处理完成后间隔（秒）
        
    def start(self):
        """启动绑卡任务管理器"""
        if self.running:
            logger.warning("绑卡任务管理器已在运行")
            return
            
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("绑卡任务管理器已启动")
        
    def stop(self):
        """停止绑卡任务管理器"""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=10)
        logger.info("绑卡任务管理器已停止")
        
    def add_card_account(self, email, token):
        """添加需要绑卡的账号到文件"""
        try:
            with open(self.card_accounts_file, "a", encoding="utf-8") as f:
                f.write(f"{email}|{token}\n")
            logger.info(f"已添加绑卡账号到队列: {email}")
        except Exception as e:
            logger.error(f"添加绑卡账号失败: {e}")
            
    def _read_first_account(self):
        """读取第一行账号并从文件中删除"""
        try:
            if not os.path.exists(self.card_accounts_file):
                logger.debug(f"绑卡队列文件不存在: {self.card_accounts_file}")
                return None
                
            with open(self.card_accounts_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
                
            if not lines:
                return None
                
            # 获取第一行有效数据
            first_line = None
            remaining_lines = []
            
            for line in lines:
                line = line.strip()
                if line and "|" in line:
                    if first_line is None:
                        first_line = line
                    else:
                        remaining_lines.append(line)
                elif line:  # 保留非空但格式错误的行
                    remaining_lines.append(line)
                    
            if first_line is None:
                return None
                
            # 重写文件，移除第一行
            with open(self.card_accounts_file, "w", encoding="utf-8") as f:
                for line in remaining_lines:
                    f.write(line + "\n")
                    
            # 解析账号信息
            parts = first_line.split("|")
            if len(parts) >= 2:
                email, token = parts[0].strip(), parts[1].strip()
                logger.info(f"读取到绑卡账号: {email}")
                return {"email": email, "token": token}
            else:
                logger.error(f"账号格式错误: {first_line}")
                return None
                
        except Exception as e:
            logger.error(f"读取绑卡账号失败: {e}")
            return None
            
    def _worker_loop(self):
        """工作线程主循环"""
        logger.info("绑卡任务工作线程已启动")
        
        while self.running:
            try:
                # 读取第一个账号
                account = self._read_first_account()
                
                if account:
                    logger.info(f"开始处理绑卡任务: {account['email']}")
                    success = self._process_card_binding(account)
                    
                    if not success:
                        # 绑卡失败，重新添加到文件末尾
                        logger.warning(f"绑卡失败，重新添加到队列: {account['email']}")
                        self.add_card_account(account['email'], account['token'])
                    
                    # 处理完成后等待
                    logger.info(f"绑卡任务处理完成，等待 {self.process_interval} 秒")
                    time.sleep(self.process_interval)
                else:
                    # 没有任务，等待扫描间隔
                    logger.info(f"队列为空，等待 {self.scan_interval} 秒后再次扫描...")
                    time.sleep(self.scan_interval)
                    
            except Exception as e:
                logger.error(f"绑卡任务工作线程异常: {e}")
                time.sleep(self.scan_interval)
                
        logger.info("绑卡任务工作线程已退出")
        
    def _process_card_binding(self, account):
        """处理单个绑卡任务"""
        email = account['email']
        token = account['token']
        browser = None
        
        try:
            logger.info(f"开始绑卡流程: {email}")
            
            # 创建独立的浏览器实例
            browser = self._create_card_browser()
            if not browser:
                logger.error(f"创建绑卡浏览器失败: {email}")
                return False
                
            # 设置cursor.com的cookie
            if not self._setup_cursor_cookies(browser, token):
                logger.error(f"设置cookie失败: {email}")
                return False
                
            # 打开绑卡页面
            if not self._navigate_to_trial_page(browser):
                logger.error(f"导航到试用页面失败: {email}")
                return False
                
            # 点击Continue按钮
            if not self._click_continue_button(browser):
                logger.error(f"点击Continue按钮失败: {email}")
                return False
                
            # 执行绑卡流程
            if not self._execute_card_binding_flow(browser, email):
                logger.error(f"执行绑卡流程失败: {email}")
                return False
                
            # 获取并提交accessToken
            if not self._get_and_submit_access_token(browser, email):
                logger.error(f"获取并提交accessToken失败: {email}")
                return False
                
            logger.info(f"绑卡流程完成: {email}")
            return True
            
        except Exception as e:
            logger.error(f"绑卡流程异常: {email} - {e}")
            return False
        finally:
            # 清理浏览器资源
            self._cleanup_card_browser(browser)
            
    def _create_card_browser(self):
        """创建独立的绑卡浏览器实例"""
        try:
            # 设置浏览器选项
            co = ChromiumOptions()
            
            # 设置浏览器路径
            if sys.platform == "win32":
                browser_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
            elif sys.platform == "darwin":
                browser_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            else:
                browser_path = "/usr/bin/google-chrome"
            co.set_browser_path(browser_path)
            
            # 使用固定的用户数据目录
            profile_path = "chrome-data-card-binding"

            # 如果目录存在，尝试清理（但不强制）
            if os.path.exists(profile_path):
                try:
                    # 先强制结束可能的Chrome进程
                    self._force_kill_chrome_processes()
                    time.sleep(2)  # 等待进程完全结束

                    import shutil
                    shutil.rmtree(profile_path)
                    logger.info("已清理旧的绑卡浏览器数据目录")
                except Exception as e:
                    logger.warning(f"清理旧目录失败: {e}")
                    # 如果清理失败，尝试重命名旧目录
                    try:
                        backup_path = f"{profile_path}_backup_{int(time.time())}"
                        os.rename(profile_path, backup_path)
                        logger.info(f"无法删除旧目录，已重命名为: {backup_path}")
                    except Exception as rename_e:
                        logger.warning(f"重命名旧目录也失败: {rename_e}")
                        # 如果重命名也失败，就使用带时间戳的新目录
                        profile_path = f"chrome-data-card-binding-{int(time.time())}"
                        logger.info(f"使用新的目录名: {profile_path}")

            os.makedirs(profile_path, exist_ok=True)
            co.set_user_data_path(profile_path)
            
            # 设置浏览器参数（与注册流程使用不同的端口范围避免冲突）
            co.set_argument("--incognito")  # 无痕模式
            co.set_argument("--disable-extensions")
            co.set_argument("--disable-gpu")
            co.set_argument("--disable-dev-shm-usage")
            co.set_argument("--disable-infobars")
            co.set_argument("--disable-notifications")
            co.set_argument("--no-first-run")
            co.set_argument("--no-default-browser-check")
            co.set_argument("--disable-webrtc")
            co.set_argument("--disable-sync")
            co.set_argument("--disable-translate")
            
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")
                
            # 使用独立端口范围（30000-35000）避免与注册流程冲突
            import socket
            def find_free_port(start_port=30000, end_port=35000):
                for port in range(start_port, end_port):
                    try:
                        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                            s.bind(('localhost', port))
                            return port
                    except OSError:
                        continue
                return None
                
            card_port = find_free_port()
            if card_port:
                co.set_argument(f"--remote-debugging-port={card_port}")
                logger.info(f"绑卡浏览器使用端口: {card_port}")
            else:
                co.auto_port()
                logger.warning("未找到可用端口，使用自动端口")
                
            co.headless(False)  # 使用有头模式方便观察
            
            # 启动浏览器
            browser = ChromiumPage(co)
            time.sleep(2)
            
            logger.info("绑卡浏览器创建成功")
            return browser

        except Exception as e:
            logger.error(f"创建绑卡浏览器失败: {e}")
            return None

    def _force_kill_chrome_processes(self):
        """强制结束绑卡相关的Chrome进程"""
        try:
            current_processes = [p.pid for p in psutil.process_iter() if 'chrome' in p.name().lower()]
            for pid in current_processes:
                try:
                    process = psutil.Process(pid)
                    # 检查进程命令行是否包含绑卡浏览器的用户数据目录
                    cmdline = ' '.join(process.cmdline())
                    if 'chrome-data-card-binding' in cmdline:
                        if sys.platform == "win32":
                            os.system(f'taskkill /F /PID {pid} /T >nul 2>&1')
                        else:
                            os.kill(pid, signal.SIGKILL)
                        logger.info(f"强制结束绑卡Chrome进程 PID: {pid}")
                except:
                    pass  # 忽略进程操作错误
        except Exception as e:
            logger.warning(f"强制结束Chrome进程时出错: {e}")

    def _setup_cursor_cookies(self, browser, token):
        """设置cursor.com的cookie"""
        try:
            # 先导航到cursor.com域名
            browser.get("https://cursor.com")
            time.sleep(2)

            # 设置登录状态cookie
            browser.set.cookies([{
                'name': 'WorkosCursorSessionToken',
                'value': token,
                'domain': '.cursor.com',
                'path': '/',
                'secure': True,
                'httpOnly': True
            }])

            logger.info("已设置cursor.com登录cookie")
            return True

        except Exception as e:
            logger.error(f"设置cursor.com cookie失败: {e}")
            return False

    def _navigate_to_trial_page(self, browser):
        """导航到试用页面"""
        try:
            trial_url = "https://cursor.com/cn/trial"
            logger.info(f"导航到试用页面: {trial_url}")
            browser.get(trial_url)
            time.sleep(3)
            return True

        except Exception as e:
            logger.error(f"导航到试用页面失败: {e}")
            return False

    def _click_continue_button(self, browser):
        """点击Continue按钮"""
        try:
            # 查找并点击Continue按钮
            continue_button = browser.ele("xpath://button[contains(text(), 'Continue') or contains(text(), 'continue')]", timeout=10)
            if continue_button:
                logger.info("找到Continue按钮，点击进入绑卡页面")
                continue_button.click()
                time.sleep(5)  # 等待页面跳转
                return True
            else:
                logger.error("未找到Continue按钮")
                return False

        except Exception as e:
            logger.error(f"点击Continue按钮失败: {e}")
            return False

    def _execute_card_binding_flow(self, browser, email):
        """执行绑卡流程"""
        try:
            # 等待页面加载
            time.sleep(3)

            # 查找信用卡选项按钮
            card_button = browser.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
            if not card_button:
                logger.error("未找到信用卡选项按钮")
                return False

            logger.info("找到信用卡选项按钮，点击")
            card_button.click()
            time.sleep(3)

            # 选择银行卡支付方式
            self._select_card_payment_method(browser)

            # 加载信用卡信息（使用与主程序相同的方法）
            card_info = self._load_and_remove_card_info()
            if not card_info:
                logger.error("加载信用卡信息失败")
                return False

            # 等待信用卡表单加载
            self._wait_for_credit_card_form(browser)

            # 填充信用卡表单
            if not self._fill_credit_card_form(browser, card_info):
                logger.error("填充信用卡表单失败")
                return False

            # 提交表单
            if not self._submit_credit_card_form(browser):
                logger.error("提交信用卡表单失败")
                return False

            logger.info(f"绑卡流程执行成功: {email}")
            return True

        except Exception as e:
            logger.error(f"执行绑卡流程异常: {e}")
            return False

    def _select_card_payment_method(self, browser):
        """选择银行卡支付方式（参考主程序的方法）"""
        try:
            # 查找并点击银行卡支付方式
            card_payment_button = browser.ele("xpath://button[contains(@class, 'payment-method') or contains(text(), 'Card') or contains(text(), 'card')]", timeout=10)
            if card_payment_button:
                card_payment_button.click()
                time.sleep(2)
                logger.info("已选择银行卡支付方式")
            else:
                logger.warning("未找到银行卡支付方式按钮，可能已默认选择")

        except Exception as e:
            logger.error(f"选择银行卡支付方式失败: {e}")

    def _load_and_remove_card_info(self):
        """从JSON文件加载信用卡信息（与主程序保持一致）"""
        try:
            # 读取JSON文件
            with open("ChinaUnionPay.json", "r", encoding="utf-8") as f:
                card_list = json.load(f)

            if not card_list:
                logger.error("ChinaUnionPay.json文件为空")
                return None

            # 取第一个信用卡信息（固定读取模式，不删除）
            card_info = card_list[0]
            logger.info(f"获取信用卡信息: {card_info.get('Name', 'Unknown')}")
            logger.info("绑卡任务使用固定读取模式：使用第一条信用卡数据，不删除文件中的记录")

            return card_info

        except Exception as e:
            logger.error(f"读取信用卡信息失败: {e}")
            return None

    def _wait_for_credit_card_form(self, browser):
        """等待信用卡表单加载"""
        try:
            # 等待卡号输入框出现
            card_number_input = browser.ele("xpath://input[@id='cardNumber']", timeout=15)
            if card_number_input:
                logger.info("信用卡表单已加载")
                return True
            else:
                logger.error("信用卡表单加载超时")
                return False
        except Exception as e:
            logger.error(f"等待信用卡表单加载异常: {e}")
            return False

    def _fill_credit_card_form(self, browser, card_info):
        """填充信用卡表单（参考主程序的方法）"""
        try:
            logger.info("开始填充信用卡表单")

            # 1. 填充卡号
            card_number_input = browser.ele("xpath://input[@id='cardNumber']", timeout=10)
            if card_number_input:
                card_number_input.clear()
                card_number_input.input(str(card_info.get('CardNumber', '')))
                logger.info("已填充卡号")
            else:
                logger.error("未找到卡号输入框")
                return False

            time.sleep(1)

            # 2. 填充有效期
            card_expiry_input = browser.ele("xpath://input[@id='cardExpiry']", timeout=10)
            if card_expiry_input:
                # 格式化有效期，确保年份只有两位数
                formatted_expiry = self._format_expiry_date(card_info.get('Expiry', ''))
                card_expiry_input.clear()
                card_expiry_input.input(str(formatted_expiry))
                logger.info("已填充有效期")
            else:
                logger.error("未找到有效期输入框")
                return False

            time.sleep(1)

            # 3. 填充CVV
            card_cvc_input = browser.ele("xpath://input[@id='cardCvc']", timeout=10)
            if card_cvc_input:
                card_cvc_input.clear()
                card_cvc_input.input(str(card_info.get('CVV', '')))
                logger.info("已填充CVV")
            else:
                logger.error("未找到CVV输入框")
                return False

            time.sleep(1)

            # 4. 填充姓名
            billing_name_input = browser.ele("xpath://input[@id='billingName']", timeout=10)
            if billing_name_input:
                billing_name_input.clear()
                billing_name_input.input(str(card_info.get('Name', '')))
                logger.info("已填充姓名")
            else:
                logger.error("未找到姓名输入框")
                return False

            time.sleep(2)

            # 5. 选择国家 CN
            billing_country_select = browser.ele("@id=billingCountry", timeout=10)
            if billing_country_select:
                try:
                    billing_country_select.select.by_value('CN')
                    logger.info("已选择国家 CN")
                    browser.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                except:
                    try:
                        billing_country_select.select.by_text('China')
                        logger.info("已选择国家 China")
                        browser.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                    except:
                        logger.warning("选择国家失败，继续其他字段")

            time.sleep(3)  # 等待省份下拉框加载

            # 6. 填充地址
            billing_address_input = browser.ele("xpath://input[@id='billingAddressLine1']", timeout=10)
            if billing_address_input:
                billing_address_input.clear()
                billing_address_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info("已填充地址")

            time.sleep(1)

            # 7. 填充邮政编码
            billing_postal_input = browser.ele("xpath://input[@id='billingPostalCode']", timeout=10)
            if billing_postal_input:
                billing_postal_input.clear()
                billing_postal_input.input("353000")
                logger.info("已填充邮政编码")

            time.sleep(1)

            # 8. 填充城市
            billing_locality_input = browser.ele("xpath://input[@id='billingLocality']", timeout=10)
            if billing_locality_input:
                billing_locality_input.clear()
                billing_locality_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info("已填充城市")

            time.sleep(1)

            # 9. 选择省份
            billing_admin_select = browser.ele("@id=billingAdministrativeArea", timeout=10)
            if billing_admin_select:
                try:
                    options = billing_admin_select.eles("tag:option")
                    for option in options:
                        if '福建' in option.text:
                            option.click()
                            logger.info(f"已选择省份: {option.text}")
                            break
                except Exception as e:
                    logger.warning(f"选择省份时出错: {e}")

            time.sleep(1)

            # 10. 填充区域
            billing_dependent_input = browser.ele("xpath://input[@id='billingDependentLocality']", timeout=10)
            if billing_dependent_input:
                billing_dependent_input.clear()
                billing_dependent_input.input(str(card_info.get('Address', '福建省三明市')))
                logger.info("已填充区域")

            time.sleep(3)
            logger.info("信用卡表单填充完成")
            return True

        except Exception as e:
            logger.error(f"填充信用卡表单异常: {e}")
            return False

    def _format_expiry_date(self, expiry):
        """格式化有效期，确保年份只有两位数"""
        try:
            if not expiry:
                return ""

            # 如果包含斜杠，分割处理
            if "/" in expiry:
                parts = expiry.split("/")
                if len(parts) == 2:
                    month, year = parts[0].strip(), parts[1].strip()
                    # 确保年份只有两位数
                    if len(year) == 4:
                        year = year[-2:]
                    return f"{month.zfill(2)}/{year}"

            return expiry
        except Exception as e:
            logger.error(f"格式化有效期失败: {e}")
            return expiry

    def _submit_credit_card_form(self, browser):
        """提交信用卡表单并处理人机验证"""
        try:
            # 查找并点击提交按钮
            submit_button = browser.ele("xpath://button[@data-testid='hosted-payment-submit-button']", timeout=10)
            if not submit_button:
                logger.error("未找到提交按钮")
                return False

            logger.info("找到提交按钮，点击提交")
            submit_button.click()
            time.sleep(5)

            # 检查是否出现人机验证
            hcaptcha_elements = [
                "xpath://div[@id='h-captcha']",
                "xpath://iframe[contains(@src,'hcaptcha')]",
                "xpath://div[contains(@class,'h-captcha')]",
                "xpath://div[contains(@class,'hcaptcha')]"
            ]

            hcaptcha_found = False
            for selector in hcaptcha_elements:
                hcaptcha_root = browser.ele(selector, timeout=3)
                if hcaptcha_root:
                    logger.info(f"检测到人机验证: {selector}")
                    hcaptcha_found = True
                    break

            if hcaptcha_found:
                logger.info("检测到人机验证，尝试处理")
                try:
                    # 使用Tab+空格方式处理人机验证
                    logger.info("使用Tab+空格方式处理人机验证")

                    # 发送两次Tab键：第一次到关闭按钮，第二次到复选框，然后发送空格键
                    browser.actions.key_down('TAB')
                    browser.actions.key_up('TAB')
                    time.sleep(0.5)
                    browser.actions.key_down('TAB')
                    browser.actions.key_up('TAB')
                    time.sleep(1)
                    browser.actions.key_down('space')
                    browser.actions.key_up('space')
                    logger.info("已发送两次Tab和Space键")
                    time.sleep(2)

                    # 检测验证是否完成
                    def is_captcha_completed():
                        # 检查h-captcha-response是否有值
                        response_element = browser.ele("xpath://textarea[@name='h-captcha-response']", timeout=1)
                        if response_element:
                            response_value = response_element.attr('value') or browser.run_js("return arguments[0].value;", response_element)
                            if response_value and response_value.strip():
                                logger.info("检测到h-captcha-response有值，验证完成")
                                return True

                        # 检查g-recaptcha-response是否有值
                        recaptcha_element = browser.ele("xpath://textarea[@name='g-recaptcha-response']", timeout=1)
                        if recaptcha_element:
                            recaptcha_value = recaptcha_element.attr('value') or browser.run_js("return arguments[0].value;", recaptcha_element)
                            if recaptcha_value and recaptcha_value.strip():
                                logger.info("检测到g-recaptcha-response有值，验证完成")
                                return True

                        # 检查验证容器是否消失
                        captcha_container = browser.ele("xpath://div[contains(@class,'HCaptcha-container')]", timeout=1)
                        if not captcha_container:
                            logger.info("验证容器消失，验证完成")
                            return True

                        return False

                    # 等待人机验证完成
                    logger.info("等待人机验证完成...")
                    for i in range(60):
                        time.sleep(1)

                        # 检查验证是否完成
                        if is_captcha_completed():
                            logger.info("人机验证已完成，等待页面跳转...")
                            # 验证完成后等待页面跳转
                            for j in range(30):  # 增加等待时间到60秒
                                time.sleep(2)
                                current_url = browser.url
                                if "cursor.com" in current_url and not any(keyword in current_url.lower() for keyword in ["payment", "checkout", "stripe"]):
                                    logger.info(f"人机验证完成，绑卡成功，已跳转到: {current_url}")
                                    # 等待页面完全加载
                                    logger.info("等待页面完全加载...")
                                    time.sleep(5)
                                    return True
                            # 如果验证完成但没有跳转，说明绑卡失败
                            logger.warning(f"人机验证完成但未跳转，当前URL: {browser.url}，绑卡失败")
                            return False

                        # 检查是否直接跳转了
                        current_url = browser.url
                        if "cursor.com" in current_url and not any(keyword in current_url.lower() for keyword in ["payment", "checkout", "stripe"]):
                            logger.info(f"绑卡成功，已跳转到: {current_url}")
                            # 等待页面完全加载
                            logger.info("等待页面完全加载...")
                            time.sleep(5)
                            return True

                    logger.warning("人机验证等待超时")
                    return False

                except Exception as captcha_e:
                    logger.error(f"处理人机验证时出错: {captcha_e}")
                    return False
            else:
                # 没有人机验证，直接检查是否绑定成功
                logger.info("未检测到人机验证，检查是否绑定成功")
                time.sleep(3)

                # 检查是否绑定成功
                current_url = browser.url
                if "cursor.com" in current_url and not any(keyword in current_url.lower() for keyword in ["payment", "checkout", "stripe"]):
                    logger.info(f"绑卡成功，已跳转到: {current_url}")
                    # 等待页面完全加载
                    logger.info("等待页面完全加载...")
                    time.sleep(5)
                    return True
                else:
                    logger.warning(f"绑卡可能失败，当前URL: {current_url}")
                    return False

        except Exception as e:
            logger.error(f"提交信用卡表单失败: {e}")
            return False

    def _get_and_submit_access_token(self, browser, email):
        """获取并提交accessToken"""
        try:
            # 绑卡成功后，需要跳转到loginDeepControl页面获取accessToken
            logger.info("绑卡成功，开始获取accessToken...")

            # 等待页面完全加载和稳定
            logger.info("等待页面完全稳定...")
            time.sleep(5)

            # 重新生成OAuth参数
            uuid_val = str(uuid.uuid4())

            # 生成code_verifier (32字节随机数，Base64编码)
            random_bytes = secrets.token_bytes(32)
            verifier = base64.urlsafe_b64encode(random_bytes).decode('utf-8').rstrip('=')

            # 生成code_challenge (verifier的SHA256哈希，Base64编码)
            verifier_bytes = verifier.encode('utf-8')
            challenge_bytes = hashlib.sha256(verifier_bytes).digest()
            challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')

            logger.info(f"生成OAuth参数 - UUID: {uuid_val}, Challenge: {challenge[:20]}..., Verifier: {verifier[:20]}...")

            # 构造loginDeepControl URL
            login_deep_control_url = f"https://cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={uuid_val}&mode=login"
            logger.info(f"跳转到登录确认页面: {login_deep_control_url}")
            browser.get(login_deep_control_url)
            time.sleep(3)

            # 查找并点击登录按钮
            login_button = browser.ele("xpath://button[.//span[contains(text(), 'Log In')]]", timeout=10)
            if login_button:
                logger.info("找到登录按钮，点击确认")
                login_button.click()
                time.sleep(5)
            else:
                logger.warning("未找到登录按钮")

            # 获取token
            time.sleep(5)
            token = self._get_token(browser)
            if not token:
                logger.error("获取token失败")
                return False

            logger.info(f"成功获取token: {token[:50]}...")

            # 获取accessToken和refreshToken
            access_token, refresh_token = self._get_access_token_and_refresh_token(token, uuid_val, verifier)

            if access_token and refresh_token:
                logger.info("成功获取accessToken和refreshToken")
                # 提交token和tokens到API
                self._submit_token(email, token, access_token, refresh_token)
                logger.info(f"绑卡流程完成: {email} (token和accessToken提交已触发)")
                return True
            else:
                logger.warning("未能获取accessToken和refreshToken，仅提交原token")
                # 仅提交原token到API
                self._submit_token(email, token)
                logger.info(f"绑卡流程完成: {email} (仅token提交已触发)")
                return True

        except Exception as e:
            logger.error(f"获取并提交accessToken失败: {e}")
            return False

    def _get_token(self, browser):
        """获取token"""
        try:
            # 检查是否跳转到cursor.com
            current_url = browser.url
            logger.info(f"当前URL: {current_url}")

            if "cursor.com" in current_url:
                # 尝试从cookie中获取token
                try:
                    cookies = browser.cookies()
                    for cookie in cookies:
                        if cookie.get('name') == 'WorkosCursorSessionToken':
                            token = cookie.get('value')
                            if token:
                                logger.info(f"从cookie中获取到token: {token[:50]}...")
                                return token
                except Exception as e:
                    logger.warning(f"从cookie获取token失败: {e}")

                # 尝试从localStorage获取token
                try:
                    token = browser.run_js("return localStorage.getItem('WorkosCursorSessionToken');")
                    if token:
                        logger.info(f"从localStorage获取到token: {token[:50]}...")
                        return token
                except Exception as e:
                    logger.warning(f"从localStorage获取token失败: {e}")

                # 尝试从sessionStorage获取token
                try:
                    token = browser.run_js("return sessionStorage.getItem('WorkosCursorSessionToken');")
                    if token:
                        logger.info(f"从sessionStorage获取到token: {token[:50]}...")
                        return token
                except Exception as e:
                    logger.warning(f"从sessionStorage获取token失败: {e}")

                # 尝试从URL中提取token
                if "token=" in current_url:
                    token_start = current_url.find("token=") + 6
                    token_end = current_url.find("&", token_start)
                    if token_end == -1:
                        token = current_url[token_start:]
                    else:
                        token = current_url[token_start:token_end]

                    if token:
                        logger.info(f"从URL中提取到token: {token[:50]}...")
                        return token

            logger.error("未能获取到token")
            return None

        except Exception as e:
            logger.error(f"获取token异常: {e}")
            return None

    def _get_access_token_and_refresh_token(self, token, uuid_val, verifier):
        """根据token获取accessToken和refreshToken

        重要：uuid_val和verifier必须与loginDeepControl页面使用的参数完全一致
        """
        try:
            logger.info("开始获取accessToken和refreshToken")
            logger.info(f"使用参数 - UUID: {uuid_val}, Verifier: {verifier[:20]}...")

            # 处理token格式（与cursor_auto_reg_simple.py保持一致）
            if not "user_01" in token:
                processed_token = f"user_01::{token}"
                logger.info("token格式已调整，添加user_01前缀")
            else:
                processed_token = token
                logger.info("token格式正确，无需调整")

            # URL编码处理
            from urllib.parse import quote
            encoded_token = quote(processed_token, safe='')

            # 调用API获取tokens（使用与loginDeepControl相同的uuid和verifier）
            api_url = f"https://api2.cursor.sh/auth/poll?uuid={uuid_val}&verifier={verifier}"
            logger.info(f"调用API获取accessToken: {api_url}")

            response = requests.get(api_url, timeout=30)
            logger.info(f"API响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                logger.info(f"API响应内容: {result}")

                # 提取accessToken和refreshToken
                access_token = result.get('accessToken')
                refresh_token = result.get('refreshToken')

                if access_token and refresh_token:
                    logger.info("[SUCCESS] 成功获取accessToken和refreshToken")
                    logger.info(f"AccessToken: {access_token[:50]}...")
                    logger.info(f"RefreshToken: {refresh_token[:50]}...")
                    return access_token, refresh_token
                else:
                    logger.error("[ERROR] API响应中未找到accessToken和refreshToken")
                    logger.error(f"响应内容: {result}")
                    return None, None
            else:
                logger.error(f"[ERROR] API请求失败: HTTP {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return None, None

        except Exception as e:
            logger.error(f"[ERROR] 获取accessToken和refreshToken时出错: {e}")
            return None, None

    def _get_access_token_and_refresh_token(self, token, uuid_val, verifier):
        """根据token获取accessToken和refreshToken"""
        try:
            logger.info("开始获取accessToken和refreshToken")

            # 处理token格式
            if not "user_01" in token:
                processed_token = f"user_01::{token}"
            else:
                processed_token = token

            # URL编码处理
            encoded_token = quote(processed_token, safe='')

            # 调用API获取tokens
            api_url = f"https://api2.cursor.sh/auth/poll?uuid={uuid_val}&verifier={verifier}"
            logger.info(f"调用API: {api_url}")

            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"API响应: {result}")

                # 提取accessToken和refreshToken
                access_token = result.get('accessToken')
                refresh_token = result.get('refreshToken')

                if access_token and refresh_token:
                    logger.info("成功获取tokens")
                    return access_token, refresh_token
                else:
                    logger.error("API响应中未找到tokens")
                    return None, None
            else:
                logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return None, None

        except Exception as e:
            logger.error(f"获取tokens时出错: {e}")
            return None, None

    def _submit_token(self, email, token, access_token=None, refresh_token=None):
        """提交token到API（异步）"""
        def submit_async():
            try:
                logger.info(f"开始提交token到API: {email}")

                # 构建请求数据
                data = {
                    "email": email,
                    "token": token
                }

                # 添加accessToken和refreshToken（如果有）
                if access_token:
                    data["accessToken"] = access_token
                    logger.info(f"包含accessToken: {access_token[:50]}...")
                if refresh_token:
                    data["refreshToken"] = refresh_token
                    logger.info(f"包含refreshToken: {refresh_token[:50]}...")

                # 发送POST请求
                url = API_BASE_URL + TOKEN_SUBMIT_ENDPOINT
                logger.info(f"提交到API: {url}")
                logger.info(f"请求数据: email={email}, token={token[:50]}..., 包含accessToken={bool(access_token)}, 包含refreshToken={bool(refresh_token)}")

                response = requests.post(url, json=data, timeout=30)
                logger.info(f"API响应状态码: {response.status_code}")

                if response.status_code == 200:
                    try:
                        response_data = response.json()
                        logger.info(f"API响应内容: {response_data}")

                        if response_data.get("code") == 0:
                            logger.info(f"[SUCCESS] token提交成功: {email}")
                            logger.info(f"[SUCCESS] 绑卡任务完全成功: {email}")
                        else:
                            logger.error(f"[ERROR] token提交失败: {email} - API返回错误码: {response_data.get('code')}")
                            logger.error(f"[ERROR] 错误信息: {response_data.get('msg', '未知错误')}")
                    except Exception as json_e:
                        logger.error(f"[ERROR] 解析API响应JSON失败: {json_e}")
                        logger.error(f"[ERROR] 原始响应: {response.text}")
                else:
                    logger.error(f"[ERROR] token提交失败: {email} - HTTP {response.status_code}")
                    logger.error(f"[ERROR] 响应内容: {response.text}")

            except Exception as e:
                logger.error(f"[ERROR] 提交token异常: {email} - {e}")

        # 异步提交
        submit_thread = threading.Thread(target=submit_async, daemon=True)
        submit_thread.start()

    def _cleanup_card_browser(self, browser):
        """清理绑卡浏览器资源"""
        if browser:
            try:
                # 先尝试清除浏览器缓存
                try:
                    browser.run_cdp('Network.clearBrowserCache', {})
                    browser.run_cdp('Network.clearBrowserCookies', {})
                    logger.info("绑卡浏览器缓存已清理")
                except:
                    pass  # 忽略清理缓存的错误

                # 关闭浏览器
                browser.quit()
                time.sleep(2)  # 等待进程完全关闭
                logger.info("绑卡浏览器已关闭")
            except Exception as e:
                logger.warning(f"关闭绑卡浏览器时出错: {e}")

            # 强制清理可能的残留进程
            try:
                current_processes = [p.pid for p in psutil.process_iter() if 'chrome' in p.name().lower()]
                for pid in current_processes:
                    try:
                        process = psutil.Process(pid)
                        # 检查进程命令行是否包含绑卡浏览器的用户数据目录
                        if 'chrome-data-card-binding' in ' '.join(process.cmdline()):
                            if sys.platform == "win32":
                                os.system(f'taskkill /F /PID {pid} /T >nul 2>&1')
                            else:
                                os.kill(pid, signal.SIGKILL)
                            logger.info(f"强制清理绑卡浏览器残留进程 PID: {pid}")
                    except:
                        pass  # 忽略进程清理错误
            except Exception as e:
                logger.warning(f"清理绑卡浏览器残留进程时出错: {e}")

            # 清理浏览器数据目录（温和清理，不强制）
            try:
                # 查找所有可能的绑卡数据目录
                import glob
                pattern = "chrome-data-card-binding*"
                directories = glob.glob(pattern)

                for profile_path in directories:
                    if os.path.exists(profile_path):
                        try:
                            import shutil
                            time.sleep(1)  # 等待进程完全关闭
                            shutil.rmtree(profile_path)
                            logger.info(f"绑卡浏览器数据目录已清理: {profile_path}")
                        except Exception as e:
                            logger.warning(f"清理目录 {profile_path} 失败: {e}")
                            # 如果清理失败，尝试重命名
                            try:
                                backup_path = f"{profile_path}_old_{int(time.time())}"
                                os.rename(profile_path, backup_path)
                                logger.info(f"无法删除，已重命名为: {backup_path}")
                            except:
                                logger.warning(f"重命名目录 {profile_path} 也失败")
            except Exception as e:
                logger.warning(f"清理绑卡浏览器数据目录时出错: {e}")

# 全局绑卡管理器实例
_card_binding_manager = None

def get_card_binding_manager():
    """获取全局绑卡管理器实例"""
    global _card_binding_manager
    if _card_binding_manager is None:
        _card_binding_manager = CardBindingManager()
    return _card_binding_manager

def start_card_binding_manager():
    """启动绑卡管理器"""
    manager = get_card_binding_manager()
    manager.start()

def stop_card_binding_manager():
    """停止绑卡管理器"""
    global _card_binding_manager
    if _card_binding_manager:
        _card_binding_manager.stop()
        _card_binding_manager = None

def add_card_account(email, token):
    """添加需要绑卡的账号"""
    manager = get_card_binding_manager()
    manager.add_card_account(email, token)

def main():
    """主函数 - 直接运行绑卡管理器"""
    logger.info("=== 启动独立绑卡任务管理器 ===")

    try:
        # 启动绑卡管理器
        logger.info("启动绑卡任务管理器...")
        start_card_binding_manager()

        logger.info("绑卡管理器已启动")
        logger.info("将自动处理 card_account.txt 文件中的绑卡任务")
        logger.info("每5秒扫描一次队列，有任务时立即处理")
        logger.info("即使队列为空也会持续扫描等待新任务")
        logger.info("按 Ctrl+C 退出程序")

        # 保持程序运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("接收到退出信号...")
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        # 停止绑卡管理器
        logger.info("停止绑卡任务管理器...")
        stop_card_binding_manager()
        logger.info("绑卡任务管理器已停止")

if __name__ == "__main__":
    main()
