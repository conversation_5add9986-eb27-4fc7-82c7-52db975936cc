#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import logging
import json
import random
import sys
import os

# 设置日志输出到文件和控制台
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_binding.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def load_card_info():
    """加载信用卡信息"""
    try:
        with open('ChinaUnionPay.json', 'r', encoding='utf-8') as f:
            cards = json.load(f)
        
        if not cards:
            logger.error("ChinaUnionPay.json文件为空")
            return None
            
        # 随机选择一张卡
        card = random.choice(cards)
        
        card_info = {
            'Name': card['Name'],
            'CardNumber': str(card['CardNumber']),
            'Expiry': card['Expiry'],
            'CVV': str(card['CVV']),
            'Address': card['Address'],
            'Country': card['Country']
        }
        
        logger.info(f"加载信用卡信息: {card_info['Name']} - {card_info['CardNumber']}")
        return card_info
        
    except Exception as e:
        logger.error(f"加载信用卡信息时出错: {e}")
        return None

def format_expiry_date(expiry):
    """格式化有效期"""
    try:
        if '/' in expiry:
            month, year = expiry.split('/')
            if len(year) == 4:
                year = year[-2:]  # 只取后两位
            return f"{month.zfill(2)}/{year}"
        return expiry
    except:
        return expiry

def test_working_binding():
    """基于工作代码的绑卡测试"""
    logger.info("=" * 50)
    logger.info("开始绑卡流程测试")
    logger.info("=" * 50)
    
    try:
        # 导入DrissionPage
        logger.info("导入DrissionPage...")
        from DrissionPage import ChromiumPage, ChromiumOptions
        logger.info("✅ DrissionPage导入成功")
        
        # 创建浏览器
        logger.info("创建浏览器...")
        co = ChromiumOptions()
        co.headless(False)
        page = ChromiumPage(co)
        logger.info("✅ 浏览器创建成功")
        
        # 设置cursor.com的cookie
        logger.info("设置cursor.com的cookie...")
        page.get("https://cursor.com")
        time.sleep(3)
        
        cookie_value = "user_01K130TGCZZVKV8TK55VHEHYA6%3A%3AeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzEzMFRHQ1paVktWOFRLNTVWSEVIWUE2IiwidGltZSI6IjE3NTM1MjE5NjYiLCJyYW5kb21uZXNzIjoiMjA2N2M5MTktYzg3Yy00ZmUxIiwiZXhwIjoxNzU4NzA1OTY2LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoid2ViIn0.os3g0uPBe5fAZjrsYFE2WaNvAMs8UAuinaY-ft3RV40"
        
        page.set.cookies([{
            'name': 'WorkosCursorSessionToken',
            'value': cookie_value,
            'domain': '.cursor.com',
            'path': '/',
            'secure': True,
            'httpOnly': True
        }])
        logger.info("✅ Cookie设置完成")
        
        # 访问trial页面
        logger.info("访问trial页面...")
        page.get("https://cursor.com/cn/trial")
        time.sleep(5)
        
        logger.info(f"当前URL: {page.url}")
        logger.info(f"页面标题: {page.title}")
        
        # 检查是否有Continue按钮（表示需要绑卡）
        logger.info("检查是否需要绑卡...")
        continue_button = page.ele("xpath://button[text()='Continue']", timeout=10)
        
        if continue_button:
            logger.info("✅ 发现Continue按钮，需要绑定信用卡")

            # 保存原始URL用于重试
            original_url = page.url
            logger.info(f"保存原始URL: {original_url}")

            # 最多重试3张卡
            max_retry_attempts = 3
            for attempt in range(max_retry_attempts):
                logger.info(f"开始第 {attempt + 1}/{max_retry_attempts} 次绑卡尝试...")

                # 如果是重试，先回到原始URL
                if attempt > 0:
                    logger.info("绑卡失败，回到原始URL重试...")
                    page.get(original_url)
                    time.sleep(5)

                    # 重新查找Continue按钮
                    continue_button = page.ele("xpath://button[text()='Continue']", timeout=10)
                    if not continue_button:
                        logger.error("重试时未找到Continue按钮")
                        continue

                # 点击Continue按钮
                logger.info("点击Continue按钮")
                continue_button.click()
                time.sleep(5)
            
                # 等待页面加载
                logger.info("等待页面加载...")
                time.sleep(3)

                # 查找信用卡选项按钮
                logger.info("查找信用卡选项按钮...")
                card_button = page.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
                if not card_button:
                    logger.error("未找到信用卡选项按钮")
                    if attempt < max_retry_attempts - 1:
                        continue
                    return False
            
                logger.info("找到信用卡选项按钮，点击")
                card_button.click()
                time.sleep(3)

                # 选择银行卡支付方式
                logger.info("选择银行卡支付方式...")
                card_option_selectors = [
                    "xpath://input[@id='payment-method-accordion-item-title-card']",
                    "xpath://label[contains(text(),'Card')]",
                    "xpath://button[contains(text(),'Card')]",
                    "xpath://*[@data-testid='payment-method-card']",
                    "xpath://div[contains(@class,'payment-method')]//span[contains(text(),'Card')]",
                    "xpath://div[contains(@class,'accordion')]//span[contains(text(),'Card')]"
                ]

                card_option_selected = False
                for selector in card_option_selectors:
                    card_option = page.ele(selector, timeout=3)
                    if card_option:
                        logger.info(f"找到银行卡支付选项: {selector}")
                        card_option.click()
                        time.sleep(2)
                        card_option_selected = True
                        break

                if card_option_selected:
                    logger.info("✅ 已选择银行卡支付方式")
                else:
                    logger.warning("未找到银行卡支付方式选项，可能已经默认选中")
            
                # 等待信用卡表单加载
                logger.info("等待信用卡表单加载...")
                card_form_loaded = False
                for i in range(15):
                    card_number_input = page.ele("xpath://input[@id='cardNumber']", timeout=1)
                    if card_number_input:
                        logger.info("✅ 信用卡表单加载完成")
                        card_form_loaded = True
                        break
                    time.sleep(1)

                if not card_form_loaded:
                    logger.error("信用卡表单加载失败")
                    if attempt < max_retry_attempts - 1:
                        continue
                    return False

                # 加载信用卡信息
                card_info = load_card_info()
                if not card_info:
                    logger.error("无法加载信用卡信息")
                    if attempt < max_retry_attempts - 1:
                        continue
                    return False
            
                # 填充信用卡表单
                logger.info("开始填充信用卡表单...")

                # 1. 填充卡号
                card_number_input = page.ele("xpath://input[@id='cardNumber']", timeout=10)
                if card_number_input:
                    card_number_input.clear()
                    card_number_input.input(str(card_info.get('CardNumber', '')))
                    logger.info("✅ 已填充卡号")
                else:
                    logger.error("未找到卡号输入框")
                    if attempt < max_retry_attempts - 1:
                        continue
                    return False

                time.sleep(1)

                # 2. 填充有效期
                card_expiry_input = page.ele("xpath://input[@id='cardExpiry']", timeout=10)
                if card_expiry_input:
                    formatted_expiry = format_expiry_date(card_info.get('Expiry', ''))
                    card_expiry_input.clear()
                    card_expiry_input.input(str(formatted_expiry))
                    logger.info("✅ 已填充有效期")
                else:
                    logger.error("未找到有效期输入框")
                    if attempt < max_retry_attempts - 1:
                        continue
                    return False

                time.sleep(1)

                # 3. 填充CVV
                card_cvc_input = page.ele("xpath://input[@id='cardCvc']", timeout=10)
                if card_cvc_input:
                    card_cvc_input.clear()
                    card_cvc_input.input(str(card_info.get('CVV', '')))
                    logger.info("✅ 已填充CVV")
                else:
                    logger.error("未找到CVV输入框")
                    if attempt < max_retry_attempts - 1:
                        continue
                    return False

                time.sleep(1)

                # 4. 填充姓名
                billing_name_input = page.ele("xpath://input[@id='billingName']", timeout=10)
                if billing_name_input:
                    billing_name_input.clear()
                    billing_name_input.input(str(card_info.get('Name', '')))
                    logger.info("✅ 已填充姓名")
                else:
                    logger.error("未找到姓名输入框")
                    if attempt < max_retry_attempts - 1:
                        continue
                    return False

                time.sleep(1)

                # 5. 选择国家 CN
                billing_country_select = page.ele("@id=billingCountry", timeout=10)
                if billing_country_select:
                    try:
                        billing_country_select.select.by_value('CN')
                        logger.info("✅ 已选择国家 CN")
                        page.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                    except:
                        try:
                            billing_country_select.select.by_text('China')
                            logger.info("✅ 已选择国家 China")
                            page.run_js("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", billing_country_select)
                        except:
                            logger.warning("选择国家失败，继续其他字段")

                time.sleep(3)

                # 6. 填充地址
                billing_address_input = page.ele("xpath://input[@id='billingAddressLine1']", timeout=10)
                if billing_address_input:
                    billing_address_input.clear()
                    billing_address_input.input(card_info.get('Address', '福建省三明市'))
                    logger.info("✅ 已填充地址")

                time.sleep(1)

                # 7. 填充邮政编码
                billing_postal_input = page.ele("xpath://input[@id='billingPostalCode']", timeout=10)
                if billing_postal_input:
                    billing_postal_input.clear()
                    billing_postal_input.input("353000")
                    logger.info("✅ 已填充邮政编码")

                time.sleep(1)

                # 8. 填充城市
                billing_locality_input = page.ele("xpath://input[@id='billingLocality']", timeout=10)
                if billing_locality_input:
                    billing_locality_input.clear()
                    billing_locality_input.input("福建省三明市")
                    logger.info("✅ 已填充城市")

                time.sleep(1)

                # 9. 选择省份
                billing_admin_select = page.ele("@id=billingAdministrativeArea", timeout=10)
                if billing_admin_select:
                    try:
                        options = billing_admin_select.eles("tag:option")
                        for option in options:
                            if '福建' in option.text:
                                option.click()
                                logger.info(f"✅ 已选择省份: {option.text}")
                                break
                        else:
                            # 如果没找到福建，选择第一个非空选项
                            for option in options[1:]:  # 跳过第一个空选项
                                if option.text.strip():
                                    option.click()
                                    logger.info(f"✅ 已选择省份: {option.text}")
                                    break
                    except Exception as e:
                        logger.warning(f"选择省份时出错: {e}")

                time.sleep(1)

                # 10. 填充区域
                billing_dependent_input = page.ele("xpath://input[@id='billingDependentLocality']", timeout=10)
                if billing_dependent_input:
                    billing_dependent_input.clear()
                    billing_dependent_input.input("福建省三明市")
                    logger.info("✅ 已填充区域")

                time.sleep(2)
                logger.info("✅ 信用卡表单填充完成！")
            
                # 提交表单
                logger.info("查找并点击提交按钮...")
                submit_selectors = [
                    "xpath://button[@type='submit']",
                    "xpath://button[contains(text(),'Submit')]",
                    "xpath://button[contains(text(),'Pay')]",
                    "xpath://button[contains(text(),'Complete')]",
                    "xpath://input[@type='submit']"
                ]

                submit_button = None
                for selector in submit_selectors:
                    submit_button = page.ele(selector, timeout=3)
                    if submit_button:
                        logger.info(f"找到提交按钮: {selector}")
                        break
            
            if submit_button:
                logger.info("点击提交按钮")
                submit_button.click()
                time.sleep(3)

                # 检测人机验证
                logger.info("检测人机验证...")
                hcaptcha_elements = [
                    "xpath://div[contains(@class,'h-captcha')]",
                    "xpath://iframe[contains(@src,'hcaptcha')]",
                    "xpath://div[@id='h-captcha']"
                ]

                hcaptcha_found = False
                for selector in hcaptcha_elements:
                    hcaptcha_root = page.ele(selector, timeout=3)
                    if hcaptcha_root:
                        logger.info(f"✅ 检测到人机验证: {selector}")
                        hcaptcha_found = True
                        break

                if hcaptcha_found:
                    logger.info("发现人机验证，使用Tab+Space方式处理...")

                    # 发送两次Tab键：第一次到关闭按钮，第二次到复选框，然后发送空格键进行点击
                    page.actions.key_down('TAB')
                    page.actions.key_up('TAB')
                    time.sleep(0.5)
                    page.actions.key_down('TAB')
                    page.actions.key_up('TAB')
                    time.sleep(1)
                    page.actions.key_down('space')
                    page.actions.key_up('space')
                    logger.info("✅ 已发送两次Tab和Space键")
                    time.sleep(2)

                    # 等待验证完成
                    logger.info("等待人机验证完成...")
                    verification_completed = False
                    for i in range(120):  # 最多等待2分钟
                        time.sleep(1)

                        # 检查验证是否完成
                        response_element = page.ele("xpath://textarea[@name='h-captcha-response']", timeout=1)
                        if response_element:
                            response_value = response_element.attr('value') or page.run_js("return arguments[0].value;", response_element)
                            if response_value and response_value.strip():
                                logger.info("✅ 检测到h-captcha-response有值，验证完成")
                                verification_completed = True
                                break

                        # 检查是否直接跳转了
                        current_url = page.url
                        if "cursor.com" in current_url and "stripe.com" not in current_url:
                            logger.info(f"✅ 人机验证完成，已跳转到cursor.com: {current_url}")
                            verification_completed = True
                            break

                        if i % 10 == 0:  # 每10秒输出一次状态
                            logger.info(f"等待验证完成中... ({i+1}/120秒)")

                    if verification_completed:
                        # 验证完成后等待跳转
                        logger.info("人机验证已完成，等待页面跳转...")
                        for j in range(10):
                            time.sleep(1)
                            current_url = page.url
                            if "cursor.com" in current_url and "stripe.com" not in current_url:
                                logger.info(f"🎉 绑卡成功！已跳转到cursor.com: {current_url}")
                                return True

                        # 如果验证完成但没有跳转，说明绑卡失败
                        logger.warning(f"⚠️ 人机验证完成但未跳转，当前URL: {page.url}，绑卡失败，尝试下一张卡")
                        if attempt < max_retry_attempts - 1:
                            continue
                        return False
                    else:
                        logger.error(f"❌ 人机验证超时，尝试下一张卡 ({attempt + 1}/{max_retry_attempts})")
                        if attempt < max_retry_attempts - 1:
                            continue
                        return False
                else:
                    logger.info("未检测到人机验证，检查是否直接成功...")
                    time.sleep(3)
                    current_url = page.url
                    if "cursor.com" in current_url and "stripe.com" not in current_url:
                        logger.info(f"🎉 绑卡成功，已跳转到cursor.com: {current_url}")
                        return True
                    else:
                        logger.warning(f"⚠️ 绑卡失败，当前URL: {current_url}，尝试下一张卡")
                        if attempt < max_retry_attempts - 1:
                            continue
                        return False
            else:
                logger.error(f"❌ 未找到提交按钮，尝试下一张卡 ({attempt + 1}/{max_retry_attempts})")
                if attempt < max_retry_attempts - 1:
                    continue
                return False

            # 如果所有重试都失败了
            logger.error(f"❌ 所有 {max_retry_attempts} 次绑卡尝试都失败了")
            return False
                
        else:
            logger.info("未发现Continue按钮，可能不需要绑卡或页面结构已变化")
            
            # 查找其他可能的试用按钮
            logger.info("查找其他试用相关按钮...")
            buttons = page.eles("tag:button")
            for i, button in enumerate(buttons):
                try:
                    text = button.text.strip()
                    if text and any(keyword in text.lower() for keyword in ['试用', 'trial', 'start']):
                        logger.info(f"找到可能的试用按钮: '{text}'")
                        button.click()
                        time.sleep(5)
                        
                        new_url = page.url
                        logger.info(f"点击后URL: {new_url}")
                        
                        if "stripe.com" in new_url or "checkout.stripe.com" in new_url:
                            logger.info("✅ 成功跳转到Stripe绑卡页面！")
                            break
                except:
                    pass
        
        # 保持浏览器打开
        logger.info("=" * 50)
        logger.info("测试完成，浏览器将保持打开状态...")
        logger.info("请检查页面状态，如有人机验证请手动完成")
        logger.info("=" * 50)
        input("按Enter键关闭浏览器...")
        page.quit()
        
    except Exception as e:
        logger.error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_working_binding()
