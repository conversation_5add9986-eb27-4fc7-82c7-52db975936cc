#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import logging
import json
import random

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_card_info():
    """加载信用卡信息"""
    try:
        with open('ChinaUnionPay.json', 'r', encoding='utf-8') as f:
            cards = json.load(f)
        
        if not cards:
            logger.error("ChinaUnionPay.json文件为空")
            return None
            
        # 随机选择一张卡
        card = random.choice(cards)
        
        card_info = {
            'Name': card['Name'],
            'CardNumber': str(card['CardNumber']),
            'Expiry': card['Expiry'],
            'CVV': str(card['CVV']),
            'Address': card['Address'],
            'Country': card['Country']
        }
        
        logger.info(f"加载信用卡信息: {card_info['Name']} - {card_info['CardNumber']}")
        return card_info
        
    except Exception as e:
        logger.error(f"加载信用卡信息时出错: {e}")
        return None

def test_direct_binding():
    """直接测试绑卡流程"""
    try:
        logger.info("开始直接绑卡测试...")
        
        # 导入DrissionPage
        from DrissionPage import ChromiumPage, ChromiumOptions
        
        # 创建浏览器
        co = ChromiumOptions()
        co.headless(False)
        page = ChromiumPage(co)
        
        logger.info("浏览器启动成功")
        
        # 设置cursor.com的cookie
        logger.info("设置cursor.com的cookie...")
        page.get("https://cursor.com")
        time.sleep(2)
        
        cookie_value = "user_01K130TGCZZVKV8TK55VHEHYA6%3A%3AeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSzEzMFRHQ1paVktWOFRLNTVWSEVIWUE2IiwidGltZSI6IjE3NTM1MjE5NjYiLCJyYW5kb21uZXNzIjoiMjA2N2M5MTktYzg3Yy00ZmUxIiwiZXhwIjoxNzU4NzA1OTY2LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoid2ViIn0.os3g0uPBe5fAZjrsYFE2WaNvAMs8UAuinaY-ft3RV40"
        
        page.set.cookies([{
            'name': 'WorkosCursorSessionToken',
            'value': cookie_value,
            'domain': '.cursor.com',
            'path': '/',
            'secure': True,
            'httpOnly': True
        }])
        
        logger.info("Cookie设置完成")
        
        # 访问trial页面
        logger.info("访问trial页面...")
        page.get("https://cursor.com/cn/trial")
        time.sleep(5)
        
        logger.info(f"当前URL: {page.url}")
        logger.info(f"页面标题: {page.title}")

        # 查找页面上的所有文本内容，看看页面状态
        try:
            body_element = page.ele("tag:body")
            if body_element:
                page_text = body_element.text
                logger.info(f"页面文本内容（前500字符）: {page_text[:500]}")
            else:
                logger.info("无法获取页面文本内容")
        except Exception as e:
            logger.warning(f"获取页面文本时出错: {e}")
        
        # 查找所有按钮
        buttons = page.eles("tag:button")
        logger.info(f"找到 {len(buttons)} 个按钮")
        
        for i, button in enumerate(buttons):
            try:
                text = button.text.strip()
                if text:
                    logger.info(f"按钮 {i+1}: '{text}'")
                    
                    # 如果找到包含试用、Continue等关键词的按钮，尝试点击
                    if any(keyword in text.lower() for keyword in ['trial', '试用', 'continue', '继续', 'start']):
                        logger.info(f"尝试点击按钮: '{text}'")
                        button.click()
                        time.sleep(5)
                        
                        new_url = page.url
                        logger.info(f"点击后URL: {new_url}")
                        
                        # 如果跳转到Stripe页面，说明成功
                        if "stripe.com" in new_url or "checkout.stripe.com" in new_url:
                            logger.info("✅ 成功跳转到Stripe绑卡页面！")
                            
                            # 开始绑卡流程测试
                            logger.info("开始测试绑卡流程...")
                            
                            # 加载信用卡信息
                            card_info = load_card_info()
                            if not card_info:
                                logger.error("无法加载信用卡信息")
                                return
                            
                            # 点击Continue按钮（如果存在）
                            continue_button = page.ele("xpath://button[text()='Continue']", timeout=10)
                            if continue_button:
                                logger.info("点击Continue按钮")
                                continue_button.click()
                                time.sleep(5)
                            
                            # 点击信用卡选项
                            card_button = page.ele("xpath://button[@data-testid='card-accordion-item-button']", timeout=10)
                            if card_button:
                                logger.info("点击信用卡选项")
                                card_button.click()
                                time.sleep(3)
                            
                            # 等待表单加载
                            logger.info("等待信用卡表单加载...")
                            for i in range(15):
                                card_number_input = page.ele("xpath://input[@id='cardNumber']", timeout=1)
                                if card_number_input:
                                    logger.info("✅ 信用卡表单加载成功！")
                                    
                                    # 填充表单
                                    logger.info("开始填充信用卡表单...")
                                    
                                    # 卡号
                                    card_number_input.clear()
                                    card_number_input.input(card_info['CardNumber'])
                                    logger.info("已填充卡号")
                                    time.sleep(1)
                                    
                                    # 有效期
                                    expiry_input = page.ele("xpath://input[@id='cardExpiry']", timeout=5)
                                    if expiry_input:
                                        expiry_input.clear()
                                        expiry_input.input(card_info['Expiry'])
                                        logger.info("已填充有效期")
                                        time.sleep(1)
                                    
                                    # CVV
                                    cvc_input = page.ele("xpath://input[@id='cardCvc']", timeout=5)
                                    if cvc_input:
                                        cvc_input.clear()
                                        cvc_input.input(card_info['CVV'])
                                        logger.info("已填充CVV")
                                        time.sleep(1)
                                    
                                    # 姓名
                                    name_input = page.ele("xpath://input[@id='billingName']", timeout=5)
                                    if name_input:
                                        name_input.clear()
                                        name_input.input(card_info['Name'])
                                        logger.info("已填充姓名")
                                        time.sleep(1)
                                    
                                    # 选择国家
                                    country_select = page.ele("@id=billingCountry", timeout=5)
                                    if country_select:
                                        try:
                                            country_select.select.by_value('CN')
                                            logger.info("已选择国家 CN")
                                        except:
                                            logger.warning("选择国家失败")
                                        time.sleep(2)
                                    
                                    # 地址
                                    address_input = page.ele("xpath://input[@id='billingAddressLine1']", timeout=5)
                                    if address_input:
                                        address_input.clear()
                                        address_input.input("福建省三明市")
                                        logger.info("已填充地址")
                                        time.sleep(1)
                                    
                                    # 邮政编码
                                    postal_input = page.ele("xpath://input[@id='billingPostalCode']", timeout=5)
                                    if postal_input:
                                        postal_input.clear()
                                        postal_input.input("353000")
                                        logger.info("已填充邮政编码")
                                        time.sleep(1)
                                    
                                    # 城市
                                    city_input = page.ele("xpath://input[@id='billingLocality']", timeout=5)
                                    if city_input:
                                        city_input.clear()
                                        city_input.input("福建省三明市")
                                        logger.info("已填充城市")
                                        time.sleep(1)
                                    
                                    logger.info("✅ 信用卡表单填充完成！")
                                    
                                    # 提交表单
                                    submit_button = page.ele("xpath://button[@type='submit']", timeout=10)
                                    if submit_button:
                                        logger.info("点击提交按钮...")
                                        submit_button.click()
                                        time.sleep(3)
                                        
                                        logger.info("✅ 绑卡流程测试完成！")
                                        logger.info("如果出现人机验证，请手动完成...")
                                    else:
                                        logger.warning("未找到提交按钮")
                                    
                                    break
                                time.sleep(1)
                            else:
                                logger.error("信用卡表单加载失败")
                            
                            # 保持浏览器打开供观察
                            input("按Enter键关闭浏览器...")
                            page.quit()
                            return
                        
                        # 检查是否有Continue按钮出现
                        continue_button = page.ele("xpath://button[text()='Continue']", timeout=3)
                        if continue_button:
                            logger.info("发现Continue按钮，继续点击...")
                            continue_button.click()
                            time.sleep(5)
                            
                            final_url = page.url
                            logger.info(f"最终URL: {final_url}")
                            
                            if "stripe.com" in final_url or "checkout.stripe.com" in final_url:
                                logger.info("✅ 通过Continue按钮成功跳转到Stripe页面！")
                                # 这里可以继续绑卡流程...
                                break
            except Exception as e:
                logger.warning(f"处理按钮时出错: {e}")
        
        # 保持浏览器打开
        logger.info("测试完成，浏览器保持打开状态...")
        input("按Enter键关闭浏览器...")
        page.quit()
        
    except Exception as e:
        logger.error(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_binding()
