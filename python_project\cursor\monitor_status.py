#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控程序状态的辅助脚本
实时显示程序运行状态和统计信息
"""

import os
import time
import re
from datetime import datetime

def parse_log_file(log_file="2925_email_monitor.log"):
    """解析日志文件，提取关键信息"""
    if not os.path.exists(log_file):
        return None
    
    stats = {
        'api_requests': 0,
        'api_success': 0,
        'token_refresh': 0,
        'errors': 0,
        'warnings': 0,
        'last_activity': None,
        'browser_status': 'unknown',
        'token_status': 'unknown',
        'network_errors': 0,
        'cursor_emails_found': 0
    }
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 分析最近的日志行
        recent_lines = lines[-500:] if len(lines) > 500 else lines
        
        for line in recent_lines:
            # API请求统计
            if "--- API请求第" in line:
                match = re.search(r"API请求第 (\d+) 次", line)
                if match:
                    stats['api_requests'] = int(match.group(1))
            
            # API成功
            if "获取到" in line and "封邮件" in line:
                stats['api_success'] += 1
            
            # Token相关
            if "token" in line.lower() and ("刷新" in line or "更新" in line):
                stats['token_refresh'] += 1
            
            # 错误和警告
            if " - ERROR - " in line:
                stats['errors'] += 1
            if " - WARNING - " in line:
                stats['warnings'] += 1
            
            # 网络错误
            if "网络连接错误" in line or "连接超时" in line:
                stats['network_errors'] += 1
            
            # Cursor邮件
            if "找到未读Cursor邮件" in line:
                stats['cursor_emails_found'] += 1
            
            # 浏览器状态
            if "浏览器" in line:
                if "正常" in line or "成功" in line:
                    stats['browser_status'] = 'normal'
                elif "异常" in line or "失败" in line:
                    stats['browser_status'] = 'error'
            
            # Token状态
            if "token" in line.lower():
                if "有效" in line:
                    stats['token_status'] = 'valid'
                elif "无效" in line or "过期" in line:
                    stats['token_status'] = 'invalid'
            
            # 最后活动时间
            if any(keyword in line for keyword in ["API请求", "获取到", "处理"]):
                # 提取时间戳
                time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if time_match:
                    stats['last_activity'] = time_match.group(1)
    
    except Exception as e:
        print(f"解析日志文件失败: {e}")
        return None
    
    return stats

def format_status_display(stats):
    """格式化状态显示"""
    if not stats:
        return "❌ 无法读取日志文件"
    
    # 计算成功率
    success_rate = (stats['api_success'] / stats['api_requests'] * 100) if stats['api_requests'] > 0 else 0
    
    # 状态图标
    browser_icon = "🟢" if stats['browser_status'] == 'normal' else "🔴" if stats['browser_status'] == 'error' else "🟡"
    token_icon = "🟢" if stats['token_status'] == 'valid' else "🔴" if stats['token_status'] == 'invalid' else "🟡"
    
    display = f"""
╔══════════════════════════════════════════════════════════════╗
║                    2925邮箱监控状态                          ║
╠══════════════════════════════════════════════════════════════╣
║ 📊 API统计                                                   ║
║   • 总请求数: {stats['api_requests']:>6} 次                        ║
║   • 成功次数: {stats['api_success']:>6} 次                        ║
║   • 成功率:   {success_rate:>6.1f} %                           ║
║                                                              ║
║ 🔧 系统状态                                                   ║
║   • 浏览器:   {browser_icon} {stats['browser_status']:>8}                    ║
║   • Token:    {token_icon} {stats['token_status']:>8}                    ║
║   • Token刷新: {stats['token_refresh']:>5} 次                        ║
║                                                              ║
║ 📧 邮件处理                                                   ║
║   • Cursor邮件: {stats['cursor_emails_found']:>4} 封                        ║
║                                                              ║
║ ⚠️  问题统计                                                   ║
║   • 错误:     {stats['errors']:>6} 次                           ║
║   • 警告:     {stats['warnings']:>6} 次                           ║
║   • 网络错误: {stats['network_errors']:>6} 次                           ║
║                                                              ║
║ 🕐 最后活动                                                   ║
║   • 时间: {stats['last_activity'] or '未知':>20}                    ║
╚══════════════════════════════════════════════════════════════╝
"""
    return display

def main():
    """主监控循环"""
    print("🔍 2925邮箱监控状态查看器")
    print("按 Ctrl+C 退出")
    print("=" * 60)
    
    try:
        while True:
            # 清屏
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # 显示当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"📅 当前时间: {current_time}")
            
            # 解析并显示状态
            stats = parse_log_file()
            status_display = format_status_display(stats)
            print(status_display)
            
            # 显示实时提示
            print("💡 提示:")
            if stats:
                if stats['errors'] > 10:
                    print("   ⚠️  错误次数较多，建议检查网络连接")
                if stats['network_errors'] > 5:
                    print("   🌐 网络错误频繁，可能是网络不稳定")
                if stats['api_requests'] > 0 and stats['api_success'] == 0:
                    print("   🔴 所有API请求都失败，请检查认证状态")
                if stats['token_status'] == 'invalid':
                    print("   🔑 Token无效，程序应该会自动刷新")
                if stats['cursor_emails_found'] > 0:
                    print(f"   📧 发现 {stats['cursor_emails_found']} 封Cursor邮件")
            
            print("\n🔄 5秒后自动刷新... (按 Ctrl+C 退出)")
            
            # 等待5秒
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n👋 监控已停止")
    except Exception as e:
        print(f"\n❌ 监控出错: {e}")

if __name__ == "__main__":
    main()
