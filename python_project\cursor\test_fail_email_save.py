#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试失败邮箱保存功能
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from drission_cursor_reg_outlook_fromtxt import RegistrationManager

def test_failed_email_save():
    """测试失败邮箱保存功能"""
    print("开始测试失败邮箱保存功能...")
    
    # 创建临时目录进行测试
    test_dir = tempfile.mkdtemp()
    original_dir = os.getcwd()
    
    try:
        # 切换到测试目录
        os.chdir(test_dir)
        
        # 创建测试用的outlookemail.txt文件
        test_email_content = """# 测试邮箱文件
<EMAIL>----password123----client-id-1----refresh-token-1
<EMAIL>----password456----client-id-2----refresh-token-2
<EMAIL>----password789----client-id-3----refresh-token-3
"""
        
        with open("outlookemail.txt", "w", encoding="utf-8") as f:
            f.write(test_email_content)
        
        print(f"已创建测试邮箱文件: {os.path.join(test_dir, 'outlookemail.txt')}")
        
        # 创建RegistrationManager实例
        manager = RegistrationManager(num_threads=1, headless=True)
        
        # 测试保存失败邮箱信息
        test_cases = [
            ("<EMAIL>", "OAuth认证失败"),
            ("<EMAIL>", "验证码获取超时"),
            ("<EMAIL>", "浏览器启动失败"),
            ("<EMAIL>", "邮箱不存在于源文件"),
        ]
        
        for email, error in test_cases:
            print(f"\n测试保存失败邮箱: {email}, 错误: {error}")
            manager.save_failed_email_info(email, error)
        
        # 检查失败文件是否创建并包含正确内容
        fail_file = "outlookemail_fail.txt"
        if os.path.exists(fail_file):
            print(f"\n✓ 失败邮箱文件已创建: {fail_file}")
            
            with open(fail_file, "r", encoding="utf-8") as f:
                content = f.read()
                print("\n失败邮箱文件内容:")
                print("-" * 50)
                print(content)
                print("-" * 50)
                
            # 验证内容
            expected_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
            for email in expected_emails:
                if email in content:
                    print(f"✓ 找到失败邮箱: {email}")
                else:
                    print(f"✗ 未找到失败邮箱: {email}")
                    
            if "<EMAIL>" not in content:
                print("✓ 正确处理了不存在的邮箱")
            else:
                print("✗ 错误地保存了不存在的邮箱")
                
        else:
            print(f"✗ 失败邮箱文件未创建: {fail_file}")
            
        print("\n测试完成!")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 恢复原始目录
        os.chdir(original_dir)
        
        # 清理测试目录
        try:
            shutil.rmtree(test_dir)
            print(f"已清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")

def test_find_email_info():
    """测试查找邮箱信息功能"""
    print("\n开始测试查找邮箱信息功能...")
    
    # 创建临时目录进行测试
    test_dir = tempfile.mkdtemp()
    original_dir = os.getcwd()
    
    try:
        # 切换到测试目录
        os.chdir(test_dir)
        
        # 创建测试用的outlookemail.txt文件
        test_email_content = """# 测试邮箱文件
<EMAIL>----password123----client-id-1----refresh-token-1
<EMAIL>----password456----client-id-2----refresh-token-2
# 注释行
<EMAIL>----password789----client-id-3----refresh-token-3
"""
        
        with open("outlookemail.txt", "w", encoding="utf-8") as f:
            f.write(test_email_content)
        
        # 创建RegistrationManager实例
        manager = RegistrationManager(num_threads=1, headless=True)
        
        # 测试查找功能
        test_cases = [
            ("<EMAIL>", True),
            ("<EMAIL>", True),
            ("<EMAIL>", True),
            ("<EMAIL>", False),
        ]
        
        for email, should_exist in test_cases:
            result = manager.find_email_info_from_source(email)
            if should_exist:
                if result:
                    print(f"✓ 成功找到邮箱 {email}: {result}")
                else:
                    print(f"✗ 未找到应该存在的邮箱: {email}")
            else:
                if not result:
                    print(f"✓ 正确处理不存在的邮箱: {email}")
                else:
                    print(f"✗ 错误地找到了不应该存在的邮箱: {email}")
                    
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 恢复原始目录
        os.chdir(original_dir)
        
        # 清理测试目录
        try:
            shutil.rmtree(test_dir)
            print(f"已清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {e}")

if __name__ == "__main__":
    test_find_email_info()
    test_failed_email_save()
