# 邮箱来源配置说明

## 新增功能

### 1. 邮箱获取策略优化
现在采用智能邮箱获取策略：
1. **优先从TXT文件读取**: 首先从 `gmail_accounts.txt` 文件获取邮箱
2. **使用后自动移除**: 使用的邮箱会从TXT文件中自动删除
3. **API作为备选**: TXT文件没有邮箱时，自动从humkt API获取
4. **自动保存未使用邮箱**: API获取的多余邮箱会自动保存到TXT文件

### 2. 延迟邮箱检查
正常获取订单详情后，会启动一个后台线程，30秒后再次获取订单详情，如果发现新的邮箱（延迟发货），会自动加入到邮箱池中。

### 3. 失败邮箱记录优化
注册失败的邮箱会记录到 `gmail_humkt_fail.txt` 文件中，格式为：`邮箱|密码`

### 4. 信用卡绑定功能
当登录后需要绑定信用卡时，程序会自动处理：
- 自动点击Continue按钮
- 选择信用卡支付选项
- 从`ChinaUnionPay.json`文件读取信用卡信息并自动填充
- 处理HCaptcha人机验证
- 完成绑定后跳转到登录确认页面获取token

## 使用方法

### 智能邮箱获取（推荐）
```bash
python drission_cursor_reg_gamail_humkt.py
```
程序会自动：
1. 优先从 `gmail_accounts.txt` 获取邮箱
2. TXT文件没有邮箱时从API获取
3. API获取的多余邮箱自动保存到TXT文件

### 指定TXT文件路径
```bash
python drission_cursor_reg_gamail_humkt.py --email-file my_emails.txt
```

## TXT文件格式

创建一个文本文件（如 `gmail_accounts.txt`），格式如下：
```
# Gmail账号文件
# 格式：邮箱|密码
# 每行一个账号，支持#开头的注释行

<EMAIL>|password123
<EMAIL>|mypassword456
<EMAIL>|secretpass789
```

## 命令行参数

- `--email-file`: TXT文件路径（默认：gmail_accounts.txt）
- `-t, --threads`: 并行线程数
- `--headless`: 无头模式
- `-n, --count`: 每个线程的注册次数
- `-p, --proxy-lifetime`: 代理有效时间（分钟）

## 示例命令

```bash
# 使用默认设置（智能邮箱获取）
python drission_cursor_reg_gamail_humkt.py

# 指定邮箱文件，2个线程，每个线程注册10次
python drission_cursor_reg_gamail_humkt.py --email-file my_emails.txt -t 2 -n 10

# 无头模式运行
python drission_cursor_reg_gamail_humkt.py --headless -t 1 -n 5
```

## 信用卡信息文件格式

`ChinaUnionPay.json` 文件格式示例：
```json
[
  {
    "CardNumber": "****************",
    "Expiry": "12/25",
    "CVV": "123",
    "Name": "张三",
    "Address": "福建省三明市梅列区列东街123号"
  }
]
```

**注意**: 程序会自动使用第一张卡的信息，使用后会从文件中删除该记录。

## 文件说明

- `gmail_accounts.txt`: 邮箱账号文件（需要自己创建）
- `gmail_accounts_example.txt`: 邮箱文件格式示例
- `ChinaUnionPay.json`: 信用卡信息文件（需要自己创建）
- `gmail_humkt_fail.txt`: 失败邮箱记录文件（自动生成）
- `cursor_registration.log`: 运行日志文件
