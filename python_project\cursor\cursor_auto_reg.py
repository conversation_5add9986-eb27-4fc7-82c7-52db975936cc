import json  # 用于安全转义 JS 字符串
import logging
import os
import random
import sys
import time
from datetime import datetime
from urllib.parse import quote

import requests
from PyQt6.QtCore import QUrl, QTimer, QEventLoop, pyqtSignal, QObject
from PyQt6.QtGui import QAction, QShortcut, QKeySequence
from PyQt6.QtNetwork import QNetworkProxy
from PyQt6.QtWebEngineCore import QWebEngineProfile, QWebEnginePage, QWebEngineUrlRequestInterceptor
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWidgets import QApplication, QMainWindow, QToolBar, QLineEdit, QVBoxLayout, QWidget, QStatusBar, QLabel

# 可选导入pyautogui用于模拟键盘操作
try:
    import pyautogui
    HAS_PYAUTOGUI = True
except ImportError:
    HAS_PYAUTOGUI = False
    print("警告：未安装pyautogui模块，无法使用键盘模拟功能")

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cursor_registration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
SIGNUP_URL = "https://authenticator.cursor.sh/password?email={}&redirect_uri=https%3A%2F%2Fcursor.com%2Fapi%2Fauth%2Fcallback"
#SIGNUP_URL = "https://www.baidu.com"
API_BASE_URL = "http://127.0.0.1:48080/admin-api"  # 修改为你的实际API端点
EMAIL_CODE_ENDPOINT = "/cqp/cursoremailpool/getEmailCode"
TOKEN_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/saveRecord"

# 代理配置
USE_PROXY = False  # 是否使用代理
PROXY_HOST = "127.0.0.1"  # 代理服务器地址
PROXY_PORT = 7890  # 代理服务器端口

# 控制注册线程数量的常量
DEFAULT_THREADS = 1  # 默认并发线程数
MAX_THREADS = 20     # 最大并发线程数

# 全局引用，防止被垃圾回收
browser_windows = []

# 工具函数：将 Python 字符串安全转换为 JS 字符串字面量
def _js_str(value: str) -> str:
    """使用 json.dumps 生成安全的 JS 字符串字面量，防止 XPath/选择器中的单引号或反斜杠破坏脚本语法。"""
    return json.dumps(value)  # json.dumps 会自动添加包裹引号并转义内部字符

class RequestInterceptor(QWebEngineUrlRequestInterceptor):
    def interceptRequest(self, info):
        info.setHttpHeader(b"User-Agent", b"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

class CursorWebPage(QWebEnginePage):
    def javaScriptConsoleMessage(self, level, message, line, source):
        if level == QWebEnginePage.JavaScriptConsoleMessageLevel.InfoMessageLevel:
            logger.debug(f"JS控制台: {message} (行 {line})")
        elif level == QWebEnginePage.JavaScriptConsoleMessageLevel.WarningMessageLevel or \
             level == QWebEnginePage.JavaScriptConsoleMessageLevel.ErrorMessageLevel:
            logger.warning(f"JS警告: {message} (行 {line})")

class BrowserWindow(QMainWindow):
    """完整的浏览器窗口，包含地址栏、导航按钮等控件"""
    def __init__(self, profile_path=None):
        super().__init__()
        self.setWindowTitle("Cursor自动注册")
        self.resize(1200, 800)
        
        # 创建中央控件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)
        
        # 创建工具栏
        self.navigation_bar = QToolBar("导航")
        self.addToolBar(self.navigation_bar)
        
        # 后退按钮
        self.back_btn = QAction("←", self)
        self.back_btn.setToolTip("后退")
        self.back_btn.triggered.connect(lambda: self.browser.back())
        self.navigation_bar.addAction(self.back_btn)
        
        # 前进按钮
        self.forward_btn = QAction("→", self)
        self.forward_btn.setToolTip("前进")
        self.forward_btn.triggered.connect(lambda: self.browser.forward())
        self.navigation_bar.addAction(self.forward_btn)
        
        # 刷新按钮
        self.reload_btn = QAction("↻", self)
        self.reload_btn.setToolTip("刷新")
        self.reload_btn.triggered.connect(lambda: self.browser.reload())
        self.navigation_bar.addAction(self.reload_btn)
        
        # 地址栏
        self.url_bar = QLineEdit()
        self.url_bar.returnPressed.connect(self.navigate_to_url)
        self.navigation_bar.addWidget(self.url_bar)
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_label = QLabel()
        self.status_bar.addWidget(self.status_label)
        
        # 创建浏览器控件
        if profile_path:
            self.profile = QWebEngineProfile(profile_path)
            self.profile.setPersistentCookiesPolicy(QWebEngineProfile.PersistentCookiesPolicy.NoPersistentCookies)
            self.profile.setHttpCacheType(QWebEngineProfile.HttpCacheType.NoCache)
            interceptor = RequestInterceptor()
            self.profile.setUrlRequestInterceptor(interceptor)
        else:
            self.profile = QWebEngineProfile.defaultProfile()
            
        self.browser = QWebEngineView()
        self.page = CursorWebPage(self.profile, self.browser)
        self.browser.setPage(self.page)
        
        # 添加浏览器控件到布局
        self.layout.addWidget(self.browser)
        
        # 连接信号
        self.browser.urlChanged.connect(self.update_url)
        self.browser.loadStarted.connect(lambda: self.status_label.setText("加载中..."))
        self.browser.loadFinished.connect(lambda: self.status_label.setText("加载完成"))
        
        # 添加开发者工具快捷键
        self.dev_tools_shortcut = QShortcut(QKeySequence("F12"), self)
        self.dev_tools_shortcut.activated.connect(self.toggle_dev_tools)

        # 创建一个窗口用于开发者工具
        self.dev_tools_view = None
        
    def navigate_to_url(self):
        """根据地址栏导航到URL"""
        url = self.url_bar.text()
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
        self.browser.load(QUrl(url))
        
    def update_url(self, url):
        """更新地址栏显示的URL"""
        self.url_bar.setText(url.toString())
        
    def load_url(self, url):
        """加载指定URL"""
        if isinstance(url, str):
            qurl = QUrl(url)
        else:
            qurl = url
        self.browser.load(qurl)
        self.url_bar.setText(qurl.toString())
        
        # 确保URL被加载，使用多种方法确保导航发生
        logger.info(f"确保URL被加载: {qurl.toString()}")
        
        # 方法1: 使用page的load方法
        self.browser.page().load(qurl)
        
        # 方法2: 使用JS直接导航
        js_code = f"""
        (function() {{
            try {{
                window.location.href = {_js_str(qurl.toString())};
                return true;
            }} catch(e) {{
                return false;
            }}
        }})();
        """
        self.browser.page().runJavaScript(js_code)
        
        # 方法3: 延迟检查加载状态
        QTimer.singleShot(1000, lambda: self._check_url_loaded(qurl))

    def _check_url_loaded(self, expected_url):
        """检查URL是否已正确加载，如果没有则尝试重新加载"""
        current_url = self.browser.url().toString()
        logger.info(f"检查URL加载状态: 期望={expected_url.toString()}, 当前={current_url}")
        
        if not self.browser.page().isLoading() and (current_url == "about:blank" or not current_url):
            logger.warning(f"URL未正确加载，尝试重新加载: {expected_url.toString()}")
            # 尝试使用triggerAction方法刷新页面
            self.browser.page().triggerAction(QWebEnginePage.Reload)
            # 再次尝试直接加载
            self.browser.load(expected_url)

    def toggle_dev_tools(self):
        """切换开发者工具的显示状态"""
        if not self.dev_tools_view:
            # 首次创建开发者工具窗口
            self.dev_tools_view = QWebEngineView()
            self.page.setDevToolsPage(self.dev_tools_view.page())
            self.dev_tools_view.setWindowTitle("开发者工具")
            self.dev_tools_view.resize(1000, 600)
        
        # 切换显示状态
        if self.dev_tools_view.isVisible():
            self.dev_tools_view.hide()
        else:
            self.dev_tools_view.show()

class SignalHandler(QObject):
    finished = pyqtSignal(str, str, str)  # group_id, email, token
    error = pyqtSignal(str, str, str)  # group_id, email, error_msg

class CursorAutoRegister(QObject):
    def __init__(self, group_id, headless=False):
        super().__init__()
        self.group_id = group_id
        self.headless = headless
        self.email = None
        self.browser = None
        self.browser_window = None  # 新增：浏览器窗口
        self.profile_path = f"chrome-data-cursor/{self.group_id}"
        self.signals = SignalHandler()
        self._step = 0  # 用于流程推进
        self.token = None
        self.code = None
        self.magic_btn_xpath = "//button[@name='intent' and @type='submit' and @value='magic-code']"

    def setup_browser(self):
        logger.info(f"[{self.group_id}] 开始设置浏览器...")
        os.makedirs(self.profile_path, exist_ok=True)
        logger.info(f"[{self.group_id}] 创建配置文件目录: {self.profile_path}")
        try:
            # 创建浏览器窗口
            self.browser_window = BrowserWindow(self.profile_path)
            self.browser = self.browser_window.browser  # 获取窗口中的浏览器控件
            
            # 设置代理（如果启用）
            if USE_PROXY:
                logger.info(f"[{self.group_id}] 设置网络代理...")
                proxy = QNetworkProxy()
                proxy.setType(QNetworkProxy.ProxyType.HttpProxy)
                proxy.setHostName(PROXY_HOST)
                proxy.setPort(PROXY_PORT)
                QNetworkProxy.setApplicationProxy(proxy)
                logger.info(f"[{self.group_id}] 已设置浏览器代理: {PROXY_HOST}:{PROXY_PORT}")
                
            # 显示窗口（除非是无头模式）
            if not self.headless:
                logger.info(f"[{self.group_id}] 显示浏览器窗口...")
                self.browser_window.show()
            else:
                logger.info(f"[{self.group_id}] 使用无头模式，不显示浏览器...")
                
            logger.info(f"[{self.group_id}] 浏览器设置完成")
            
            # 连接加载完成信号
            self.browser.loadFinished.connect(self.on_load_finished)
            
            # 保存到全局列表，防止被垃圾回收
            global browser_windows
            browser_windows.append(self.browser_window)
            
        except Exception as e:
            logger.exception(f"[{self.group_id}] 设置浏览器时出现异常: {e}")
            raise

    def generate_email(self):
        """生成随机邮箱地址"""
        timestamp = int(time.time())
        random_num = random.randint(100, 999)
        return f"a965586934{timestamp}{random_num}@2925.com"
    
    def wait_for_page_load(self, timeout=10000):
        """等待页面加载完成"""
        loop = QEventLoop()
        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(loop.quit)
        self.browser.loadFinished.connect(loop.quit)
        timer.start(timeout)
        loop.exec()

    def execute_js(self, script):
        """执行JavaScript并返回结果"""
        try:
            result = self.browser.page().runJavaScript(script)
            return result
        except Exception as e:
            logger.warning(f"JS执行错误: {e}")
            return None
    
    def execute_js_safe(self, script, default_value=None):
        """安全执行JavaScript，自动添加错误处理并返回结果
        
        参数:
            script: 要执行的JavaScript代码
            default_value: 如果执行失败时返回的默认值
        """
        # 添加try-catch包装
        wrapped_script = f"""
        (function() {{
            try {{
                {script}
            }} catch(e) {{
                return {{'error': true, 'message': e.toString()}};
            }}
        }})();
        """
        
        try:
            result = self.browser.page().runJavaScript(wrapped_script)
            if isinstance(result, dict) and result.get('error'):
                logger.warning(f"JS执行错误: {result.get('message')}")
                return default_value
            return result
        except Exception as e:
            logger.warning(f"JS执行错误: {e}")
            return default_value
    
    def click_element(self, xpath, retries=3):
        """通过XPath点击元素"""
        logger.info(f"尝试点击元素: {xpath}")
        
        # 首先检查元素是否存在（注意转义 XPath 内的引号）
        if not self.check_element_exists(xpath):
            logger.warning(f"元素不存在: {xpath}")
            # 等待一下再尝试
            QTimer.singleShot(1000, lambda: None)
            if not self.check_element_exists(xpath):
                logger.error(f"等待后元素仍不存在: {xpath}")
                return False
        
        # 尝试直接使用简单脚本点击元素
        escaped_xpath = _js_str(xpath)  # 安全转义
        simple_script = """
        (function() {
            var element = document.evaluate(%s, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
            if(element) {
                element.scrollIntoView({behavior: 'smooth', block: 'center'});
                setTimeout(function() {
                    element.click();
                }, 200);
                return true;
            }
            return false;
        })();
        """ % escaped_xpath
        
        try:
            result = self.execute_js(simple_script)
            if result:
                logger.info(f"使用简单脚本成功点击元素")
                QTimer.singleShot(500, lambda: None)
                return True
        except Exception as e:
            logger.warning(f"简单点击失败: {e}")
        
        # 如果简单脚本失败，使用更复杂的点击脚本
        script = """
        (function() {
            try {
                const element = document.evaluate(
                    %s,
                    document,
                    null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE,
                    null
                ).singleNodeValue;
                
                if (!element) {
                    return {status: 'error', message: '元素未找到'};
                }
                
                // 检查元素是否可见
                const rect = element.getBoundingClientRect();
                if (rect.width === 0 || rect.height === 0) {
                    return {status: 'error', message: '元素不可见'};
                }
                
                // 输出元素信息用于调试
                console.log('找到元素:', element.outerHTML);
                
                // 滚动元素到可见区域
                element.scrollIntoView({behavior: 'smooth', block: 'center'});
                
                // 模拟点击
                setTimeout(() => {
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;
                    
                    // 使用多种方式尝试点击
                    try {
                        // 1. 原生点击
                        element.click();
                        console.log('原生点击成功');
                    } catch(e) {
                        console.log('原生点击失败:', e);
                        
                        try {
                            // 2. 模拟事件点击
                            const clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window,
                                clientX: centerX,
                                clientY: centerY
                            });
                            element.dispatchEvent(clickEvent);
                            console.log('事件点击成功');
                        } catch(e2) {
                            console.log('事件点击失败:', e2);
                        }
                    }
                }, 200);
                
                return {status: 'success'};
            } catch (error) {
                console.error('点击脚本错误:', error);
                return {status: 'error', message: error.toString()};
            }
        })();
        """ % escaped_xpath
        
        for attempt in range(retries):
            try:
                result = self.execute_js(script)
                logger.info(f"点击脚本结果: {result}")
                
                if result and isinstance(result, dict) and result.get('status') == 'success':
                    time.sleep(1)  # 等待动画或反应完成
                    return True
                else:
                    error_msg = "未知错误"
                    if isinstance(result, dict) and 'message' in result:
                        error_msg = result.get('message')
                    logger.warning(f"点击失败，尝试 {attempt+1}: {error_msg}")
                    time.sleep(1)
            except Exception as e:
                logger.error(f"点击元素时出错: {e}")
                time.sleep(1)
        
        # 使用替代方法：直接模拟键盘按键
        try:
            logger.info("尝试使用按键模拟方法点击元素")
            key_script = """
            (function() {
                var element = document.evaluate(%s, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if(element) {
                    element.focus();
                    return true;
                }
                return false;
            })();
            """ % escaped_xpath
            
            if self.execute_js(key_script):
                # 模拟按回车键
                enter_script = """
                (function() {
                    var event = new KeyboardEvent('keydown', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    });
                    document.activeElement.dispatchEvent(event);
                    return true;
                })();
                """
                self.execute_js(enter_script)
                logger.info("已尝试使用按键模拟方法")
                time.sleep(1)
                return True
        except Exception as e:
            logger.error(f"按键模拟也失败: {e}")
            
        logger.error(f"尝试 {retries} 次后点击元素失败")
        return False
    
    def check_element_exists(self, xpath):
        """检查元素是否存在"""
        escaped_xpath = _js_str(xpath)  # 使用全局函数而不是self._js_str
        script = f"""
        var element = document.evaluate({escaped_xpath}, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
        if (!element) return false;
        
        return {{
            exists: true,
            tag: element.tagName,
            id: element.id || '',
            classes: element.className || '',
            visible: element.offsetParent !== null
        }};
        """
        
        result = self.execute_js_safe(script, False)
        
        if result:
            logger.info(f"元素存在: {xpath}, 详情: {result}")
            return True
        return False
    
    def handle_turnstile(self):
        """处理Turnstile验证，更准确的方式"""
        logger.info(f"[{self.group_id}] 开始处理人机验证...")
        
        try:
            # 1. 先检查是否存在cf-turnstile元素
            turnstile_exists = self.element_exists_simple("//div[@id='cf-turnstile']")
            # 增加确认步骤：检查是否真的是验证页面而不是按钮禁用状态
            # 检查是否存在iframe，这是真实验证的特征
            has_iframe = self.element_exists_simple("//iframe[contains(@src, 'turnstile') or contains(@src, 'cloudflare')]")
            # 检查是否有明确的验证提示文本
            has_verify_text = self.execute_js("""
            (function() {
                try {
                    const text = document.body.innerText || '';
                    return text.includes('Verify you are human') || 
                           text.includes('人机验证') || 
                           text.includes('security check');
                } catch(e) {
                    return false;
                }
            })();
            """)
            
            # 只有当存在cf-turnstile元素 AND (存在验证iframe OR 存在验证文本) 时才确认是验证页面
            if not (turnstile_exists and (has_iframe or has_verify_text)):
                logger.warning(f"[{self.group_id}] 检测到cf-turnstile元素但无iframe或验证文本，可能是误判")
                # 检查是否是邮箱验证码输入页面
                has_input = self.check_element_exists("//input[@inputmode='numeric']")
                if has_input:
                    logger.info(f"[{self.group_id}] 实际上已经到达验证码输入页面")
                    return False  # 让流程继续到验证码输入
                
                # 检查处于等待状态（按钮已点击变灰）
                button_disabled = self.execute_js("""
                (function() {
                    const magicButtons = document.querySelectorAll('button[value="magic-code"]');
                    if (magicButtons.length > 0) {
                        const button = magicButtons[0];
                        return button.disabled || 
                               button.getAttribute('disabled') !== null ||
                               button.getAttribute('aria-disabled') === 'true' ||
                               button.classList.contains('disabled');
                    }
                    return false;
                })();
                """)
                
                if button_disabled:
                    logger.info(f"[{self.group_id}] 实际上是按钮已禁用状态，不需要人机验证处理")
                    return False  # 让流程继续自然等待
                
                logger.error(f"[{self.group_id}] 未能确认是否为人机验证页面")
                return False
            
            logger.info(f"[{self.group_id}] 找到cf-turnstile元素，开始处理...")
            
            # 2. 滚动到元素位置确保可见
            scroll_script = """
            (function() {
                var element = document.getElementById('cf-turnstile');
                if (!element) return false;
                element.scrollIntoView({behavior: 'smooth', block: 'center'});
                return true;
            })();
            """
            
            if not self.execute_js(scroll_script):
                logger.error(f"[{self.group_id}] 无法滚动到cf-turnstile元素")
            
            # 3. 等待确保元素完全可见
            time.sleep(1)
            
            # 4. 发送Tab和空格键 - 使用多种方式
            # 4.1 使用PyQt6的键盘事件
            from PyQt6.QtCore import Qt
            from PyQt6.QtGui import QKeyEvent
            
            # 确保窗口获得焦点
            if not self.headless:
                self.browser_window.activateWindow()
                self.browser_window.raise_()
            
            # 先点击文档主体以确保焦点正确
            body_click_script = """
            (function() {
                document.body.click();
                return true;
            })();
            """
            self.execute_js(body_click_script)
            time.sleep(0.5)
            
            # 发送Tab键 - 多发几次确保能到达验证框
            for _ in range(1):
                tab_event = QKeyEvent(QKeyEvent.Type.KeyPress, Qt.Key.Key_Tab, Qt.KeyboardModifier.NoModifier)
                QApplication.sendEvent(self.browser, tab_event)
                time.sleep(0.3)
            
            # 发送空格键
            space_event = QKeyEvent(QKeyEvent.Type.KeyPress, Qt.Key.Key_Space, Qt.KeyboardModifier.NoModifier)
            QApplication.sendEvent(self.browser, space_event)
            
            logger.info(f"[{self.group_id}] 已发送Tab+空格键组合")
            
            # 4.2 使用pyautogui发送物理按键
            if HAS_PYAUTOGUI and not self.headless:
                try:
                    self.browser_window.activateWindow()
                    self.browser_window.raise_()
                    time.sleep(0.5)
                    
                    # 多点几次Tab确保能够聚焦到正确元素
                    for _ in range(1):
                        pyautogui.press('tab')
                        time.sleep(0.3)
                    
                    pyautogui.press('space')
                    logger.info(f"[{self.group_id}] 已通过pyautogui发送物理按键")
                except Exception as e:
                    logger.error(f"[{self.group_id}] pyautogui按键失败: {e}")
            
            # 5. 添加一个备用方法 - 直接尝试操作iframe
            try:
                iframe_script = """
                (function() {
                    // 尝试找到所有iframe
                    var iframes = document.querySelectorAll('iframe');
                    for (var i = 0; i < iframes.length; i++) {
                        try {
                            iframes[i].focus();
                            // 尝试发送一个Tab键事件给iframe
                            var keyEvent = new KeyboardEvent('keydown', {
                                bubbles: true,
                                cancelable: true,
                                key: 'Tab',
                                code: 'Tab',
                                keyCode: 9
                            });
                            iframes[i].dispatchEvent(keyEvent);
                            
                            // 再发送一个空格键事件
                            setTimeout(function() {
                                var spaceEvent = new KeyboardEvent('keydown', {
                                    bubbles: true,
                                    cancelable: true,
                                    key: ' ',
                                    code: 'Space',
                                    keyCode: 32
                                });
                                document.activeElement.dispatchEvent(spaceEvent);
                            }, 300);
                        } catch(e) {
                            console.log('iframe操作失败:', e);
                        }
                    }
                    return true;
                })();
                """
                self.execute_js(iframe_script)
                logger.info(f"[{self.group_id}] 已尝试通过iframe发送按键")
            except Exception as e:
                logger.error(f"[{self.group_id}] iframe处理失败: {e}")
            
            # 6. 等待验证处理
            logger.info(f"[{self.group_id}] 人机验证处理中，等待结果...")
            time.sleep(5)
            
            return True
        except Exception as e:
            logger.error(f"[{self.group_id}] 处理人机验证出错: {e}")
            return False

    def element_exists_simple(self, xpath):
        """简单检查元素是否存在（备用方法）"""
        escaped_xpath = _js_str(xpath)
        script = f"""
        return !!document.evaluate({escaped_xpath}, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
        """
        result = self.execute_js_safe(script, False)
        return result

    def get_email_code(self, email):
        """从邮件中获取验证码"""
        max_attempts = 50  # 增加尝试次数到50次
        for attempt in range(max_attempts):
            try:
                # 创建请求参数和代理配置
                request_kwargs = {
                    "params": {"email": email},
                    "timeout": 10
                }
                
                # 添加代理配置（如果启用）
                if USE_PROXY:
                    request_kwargs["proxies"] = {
                        "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
                        "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
                    }
                
                response = requests.get(f"{API_BASE_URL}{EMAIL_CODE_ENDPOINT}", **request_kwargs)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code"):
                        logger.info(f"获取到邮箱验证码: {data.get('code')}")
                        return data.get("code")
                logger.info(f"验证码尚未准备好，等待中... (尝试 {attempt+1}/{max_attempts})")
                time.sleep(3)  # 保持3秒间隔，配合do_input_code的逻辑
            except Exception as e:
                logger.error(f"获取邮箱验证码时出错: {e}")
                time.sleep(3)
        
        logger.error(f"尝试{max_attempts}次后获取邮箱验证码失败")
        return None
    
    def input_verification_code(self, code):
        """输入验证码"""
        if not code or len(code) != 6:
            logger.error(f"无效的验证码: {code}")
            return False
        
        script = """
        (function() {
            try {
                const inputs = document.querySelectorAll('input[inputmode="numeric"]');
                if (inputs.length !== 6) {
                    return {status: 'error', message: '预期6个输入字段，找到 ' + inputs.length};
                }
                
                const code = '%s';
                for (let i = 0; i < code.length; i++) {
                    const digit = code.charAt(i);
                    inputs[i].focus();
                    inputs[i].value = digit;
                    
                    // 触发input事件
                    const event = new Event('input', { bubbles: true });
                    inputs[i].dispatchEvent(event);
                }
                
                return {status: 'success'};
            } catch (error) {
                return {status: 'error', message: error.toString()};
            }
        })();
        """ % code
        
        result = self.execute_js(script)
        if result and result.get('status') == 'success':
            time.sleep(1)
            return True
        else:
            logger.error(f"输入验证码时出错: {result.get('message') if result else '无结果'}")
            return False
    
    def get_token(self):
        """从cookies中提取WorkosCursorSessionToken"""
        script = """
        (function() {
            try {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    cookie = cookie.trim();
                    if (cookie.startsWith('WorkosCursorSessionToken=')) {
                        return cookie.substring('WorkosCursorSessionToken='.length);
                    }
                }
                return null;
            } catch (error) {
                return {error: error.toString()};
            }
        })();
        """
        token = self.execute_js(script)
        if token and not isinstance(token, dict):
            if '::' in token:
                token = token.split('::')[1]
            return token
        
        logger.error(f"提取token失败: {token}")
        return None
    
    def submit_token(self, email, token):
        """将token提交到API"""
        try:
            # 创建请求参数和代理配置
            request_kwargs = {
                "json": {"email": email, "token": token},
                "timeout": 10
            }
            
            # 添加代理配置（如果启用）
            if USE_PROXY:
                request_kwargs["proxies"] = {
                    "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
                    "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
                }
            
            response = requests.post(f"{API_BASE_URL}{TOKEN_SUBMIT_ENDPOINT}", **request_kwargs)
            
            if response.status_code == 200:
                logger.info(f"成功提交{email}的token")
                return True
            else:
                logger.error(f"提交token失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            logger.error(f"提交token时出错: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            # 不要立即关闭浏览器窗口，让用户自己关闭
            if self.headless and self.browser_window:
                self.browser_window.close()
                self.browser_window.deleteLater()
                
            # 从全局列表中移除，但不删除对象
            global browser_windows
            if self.browser_window in browser_windows:
                browser_windows.remove(self.browser_window)
                
            # 删除配置文件目录
            import shutil
            if os.path.exists(self.profile_path):
                shutil.rmtree(self.profile_path)
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")
    
    def run(self):
        self.email = self.generate_email()
        logger.info(f"[{self.group_id}] 使用邮箱开始注册: {self.email}")
        self.setup_browser()
        encoded_email = quote(self.email)
        url = SIGNUP_URL.format(encoded_email)
        logger.info(f"[{self.group_id}] 加载URL: {url}")
        self.browser_window.load_url(url)
        
        # 添加一个额外的检查，确保URL正确加载
        QTimer.singleShot(3000, lambda: self.ensure_url_loaded(url))
    
    def ensure_url_loaded(self, expected_url):
        """确保URL已正确加载，如果没有则强制重新加载"""
        current_url = self.execute_js("window.location.href")
        logger.info(f"[{self.group_id}] 检查URL加载状态: 当前={current_url}, 预期={expected_url}")
        
        # 检查页面是否已加载内容
        has_content = self.execute_js("""
        (function() {
            return {
                readyState: document.readyState,
                bodyLength: document.body ? document.body.innerHTML.length : 0,
                title: document.title,
                hasButtons: document.querySelectorAll('button').length > 0
            };
        })();
        """)
        
        logger.info(f"[{self.group_id}] 页面内容状态: {has_content}")
        
        # 如果页面未正确加载或内容为空，尝试强制重新加载
        if not current_url or current_url == "about:blank" or (
            isinstance(has_content, dict) and 
            (has_content.get('readyState') != 'complete' or 
             has_content.get('bodyLength', 0) < 100)
        ):
            logger.warning(f"[{self.group_id}] 检测到URL可能未正确加载，尝试强制重新加载...")
            
            # 方法1: 使用浏览器的reload方法
            self.browser.reload()
            
            # 方法2: 使用JavaScript强制重新加载
            self.execute_js(f"window.location.href = '{expected_url}';")
            
            # 方法3: 使用PyQt的load方法重新加载
            if isinstance(expected_url, str):
                self.browser.load(QUrl(expected_url))
            
            # 5秒后再次检查
            QTimer.singleShot(5000, lambda: self.check_navigation_status())
    
    def check_navigation_status(self):
        """检查导航状态，确认页面是否已加载"""
        current_url = self.execute_js("window.location.href")
        ready_state = self.execute_js("document.readyState")
        has_buttons = self.execute_js("document.querySelectorAll('button').length > 0")
        
        logger.info(f"[{self.group_id}] 导航状态检查: URL={current_url}, readyState={ready_state}, hasButtons={has_buttons}")
        
        if ready_state == "complete" and has_buttons:
            logger.info(f"[{self.group_id}] 页面已成功加载")
            # 如果页面已成功加载但没有触发loadFinished信号，手动调用处理函数
            if self._step == 0:
                logger.info(f"[{self.group_id}] 手动触发页面加载完成处理")
                self.on_load_finished(True)
        else:
            # 如果仍未加载，使用更直接的方法
            logger.warning(f"[{self.group_id}] 页面仍未正确加载，尝试使用更直接的方法...")
            
            # 使用导航栏的方式加载URL
            self.browser_window.navigate_to_url()
            
            # 再次检查
            QTimer.singleShot(5000, lambda: self.check_final_status())
    
    def check_final_status(self):
        """最终检查页面状态"""
        current_url = self.execute_js("window.location.href")
        ready_state = self.execute_js("document.readyState")
        
        logger.info(f"[{self.group_id}] 最终状态检查: URL={current_url}, readyState={ready_state}")
        
        if ready_state == "complete":
            # 如果页面已加载但流程未启动，手动启动流程
            if self._step == 0:
                logger.info(f"[{self.group_id}] 强制启动页面处理流程")
                self.on_load_finished(True)
        else:
            logger.error(f"[{self.group_id}] 多次尝试后页面仍未正确加载，可能需要手动干预")

    def on_load_finished(self, ok):
        if not ok:
            logger.error(f"[{self.group_id}] 页面加载失败")
            self.signals.error.emit(self.group_id, self.email, "页面加载失败")
            self.cleanup()
            return
        logger.info(f"[{self.group_id}] 页面加载完成，step={self._step}")
        
        # 直接进入页面分析和后续流程
        self.analyze_page()
        
        # 后续流程继续...
        if self._step == 0:
            self._step = 1
            # 增加延迟，确保页面完全渲染
            logger.info(f"[{self.group_id}] 页面加载后等待 4 秒，确保页面完全渲染...")
            QTimer.singleShot(4000, self.do_click_magic_code)
        elif self._step == 2:
            # 进入验证码输入页
            QTimer.singleShot(1200, self.do_input_code)
        elif self._step == 4:
            # 进入 cursor.com 重定向页，尝试获取 token
            QTimer.singleShot(1200, self.do_get_token)

    def analyze_page(self):
        """分析页面内容，找出可交互元素"""
        logger.info("分析页面内容...")
        
        # 检查所有按钮
        buttons_script = """
        (function() {
            try {
                let result = [];
                const buttons = document.querySelectorAll('button');
                buttons.forEach((btn, i) => {
                    result.push({
                        index: i,
                        text: btn.innerText,
                        attrs: {
                            name: btn.getAttribute('name'),
                            type: btn.getAttribute('type'),
                            value: btn.getAttribute('value'),
                            id: btn.getAttribute('id'),
                            class: btn.getAttribute('class')
                        },
                        visible: btn.offsetWidth > 0 && btn.offsetHeight > 0
                    });
                });
                return result;
            } catch(e) {
                console.error('按钮分析错误:', e);
                return {error: e.toString()};
            }
        })();
        """
        
        buttons = self.execute_js(buttons_script)
        if buttons and isinstance(buttons, list):
            logger.info(f"找到 {len(buttons)} 个按钮:")
            for btn in buttons:
                logger.info(f"按钮 {btn.get('index')}: {btn.get('text')} - 名称:{btn.get('attrs', {}).get('name')} - 值:{btn.get('attrs', {}).get('value')} - 可见:{btn.get('visible')}")
        
        # 尝试获取页面标题和URL
        title_script = "document.title"
        url_script = "window.location.href"
        title = self.execute_js(title_script)
        url = self.execute_js(url_script)
        logger.info(f"页面标题: {title}")
        logger.info(f"页面URL: {url}")

    def do_click_magic_code(self):
        logger.info(f"[{self.group_id}] 尝试点击magic-code按钮...")
        
        # 首先检查按钮的状态
        button_status_script = """
        (function() {
            try {
                // 查找magic-code按钮
                const magicButtons = document.querySelectorAll('button[value="magic-code"]');
                if (magicButtons.length === 0) {
                    return {exists: false, message: "未找到magic-code按钮"};
                }
                
                // 检查按钮状态
                const button = magicButtons[0];
                const isDisabled = button.disabled || 
                                  button.getAttribute('disabled') !== null ||
                                  button.getAttribute('aria-disabled') === 'true' ||
                                  button.classList.contains('disabled');
                
                // 返回状态
                return {
                    exists: true,
                    disabled: isDisabled,
                    text: button.textContent || '',
                    visible: button.offsetWidth > 0 && button.offsetHeight > 0
                };
            } catch(e) {
                return {error: e.toString()};
            }
        })();
        """
        
        button_status = self.execute_js(button_status_script)
        
        # 如果按钮已禁用，说明之前已经成功点击，直接等待下一步
        if isinstance(button_status, dict) and button_status.get('exists') and button_status.get('disabled'):
            logger.info(f"[{self.group_id}] 按钮已处于禁用状态，跳过点击，直接检查页面变化")
            # 直接检查页面是否有验证码输入框或其他变化
            QTimer.singleShot(2000, self.check_page_after_button)
            return
        
        # 如果按钮存在且可点击，则尝试点击
        self._try_click_button(retry_count=0, max_retries=3)

    def check_page_after_button(self):
        """点击按钮后检查页面状态，判断下一步操作"""
        logger.info(f"[{self.group_id}] 检查按钮点击后的页面状态...")
        
        # 1. 首先检查是否已进入验证码输入页面（这是最理想的情况）
        if self.check_element_exists("//input[@inputmode='numeric']"):
            logger.info(f"[{self.group_id}] 已直接进入验证码输入页面")
            self.check_for_code_input()
            return
            
        # 2. 检查是否有真正的人机验证框
        # 最可靠的特征是验证iframe的存在
        has_iframe = self.element_exists_simple("//iframe[contains(@src, 'turnstile') or contains(@src, 'cloudflare')]")
        # 或明显的验证文本
        has_verify_text = self.execute_js("""
        (function() {
            try {
                const text = document.body.innerText || '';
                return text.includes('Verify you are human') || 
                       text.includes('人机验证') || 
                       text.includes('security check');
            } catch(e) {
                return false;
            }
        })();
        """)
        
        if has_iframe or has_verify_text:
            logger.info(f"[{self.group_id}] 检测到真正的人机验证 (iframe:{has_iframe}, text:{has_verify_text})")
            # 确认是真正的验证页面，进入验证处理
            QTimer.singleShot(1000, self.wait_for_turnstile)
            return
            
        # 3. 检查按钮状态 - 可能仍在等待后端响应
        button_disabled = self.execute_js("""
        (function() {
            const magicButtons = document.querySelectorAll('button[value="magic-code"]');
            if (magicButtons.length > 0) {
                const button = magicButtons[0];
                return button.disabled || 
                       button.getAttribute('disabled') !== null ||
                       button.getAttribute('aria-disabled') === 'true' ||
                       button.classList.contains('disabled');
            }
            return false;
        })();
        """)
        
        if button_disabled:
            logger.info(f"[{self.group_id}] 按钮仍处于禁用状态，继续等待页面变化")
            # 继续等待，因为按钮禁用表示操作正在处理中
            QTimer.singleShot(3000, self.check_page_after_button)
            return
            
        # 4. 如果按钮可用，可能需要重新点击
        logger.warning(f"[{self.group_id}] 按钮变为可用状态，可能需要重新点击")
        QTimer.singleShot(1000, self.do_click_magic_code)

    def _try_click_button(self, retry_count=0, max_retries=5):
        """尝试点击按钮，失败后会重试"""
        if retry_count >= max_retries:
            logger.error(f"[{self.group_id}] 达到最大重试次数 {max_retries}，无法点击magic-code按钮")
            self.signals.error.emit(self.group_id, self.email, "点击magic-code按钮失败")
            self.cleanup()
            return
            
        logger.info(f"[{self.group_id}] 尝试点击 magic-code 按钮 (尝试 {retry_count + 1}/{max_retries})...")
        
        # 再次分析页面
        self.analyze_page()
        
        # 首先检查按钮是否已经是禁用状态
        button_status_script = """
        (function() {
            try {
                // 查找magic-code按钮
                const magicButtons = document.querySelectorAll('button[value="magic-code"]');
                if (magicButtons.length === 0) {
                    return {exists: false, message: "未找到magic-code按钮"};
                }
                
                // 检查按钮状态
                const button = magicButtons[0];
                const isDisabled = button.disabled || 
                                  button.getAttribute('disabled') !== null ||
                                  button.getAttribute('aria-disabled') === 'true' ||
                                  button.classList.contains('disabled');
                
                // 返回状态
                return {
                    exists: true,
                    disabled: isDisabled,
                    text: button.textContent || '',
                    visible: button.offsetWidth > 0 && button.offsetHeight > 0
                };
            } catch(e) {
                return {error: e.toString()};
            }
        })();
        """
        
        button_status = self.execute_js(button_status_script)
        
        # 如果按钮已禁用，说明之前已经成功点击，直接等待下一步
        if isinstance(button_status, dict) and button_status.get('exists') and button_status.get('disabled'):
            logger.info(f"[{self.group_id}] 按钮已处于禁用状态，跳过点击，直接检查页面变化")
            QTimer.singleShot(2000, self.check_page_after_button)
            return
        
        # 确认页面已完全加载
        ready_script = """
        (function() {
            return {
                readyState: document.readyState,
                buttonCount: document.querySelectorAll('button').length,
                formCount: document.querySelectorAll('form').length
            };
        })();
        """
        result = self.execute_js(ready_script)
        if result:
            logger.info(f"页面状态: {result}")
        
        # 首先尝试直接通过索引点击带有"magic-code"值的按钮
        result = self.click_by_index_or_text("Email sign-in code", "magic-code")
        if result:
            logger.info(f"[{self.group_id}] 已通过索引或文本点击magic-code按钮")
            logger.info(f"[{self.group_id}] 已点击magic-code按钮")
            # 点击按钮后直接进入等待人机验证出现的阶段
            QTimer.singleShot(5000, self.wait_for_turnstile)
            return
            
        # 尝试多种选择器
        selectors = [
            ("xpath", "//button[@name='intent' and @type='submit' and @value='magic-code']"),
            ("css", "button[name='intent'][type='submit'][value='magic-code']"),
            ("css", "button[name='intent']"),
            ("css", "form button[type='submit']"),
            ("css", ".magic-code"),
            ("css", "button"),  # 最后尝试任何按钮
            ("text", "继续"),
            ("text", "Continue"),
            ("text", "Magic Code"),
            ("text", "Send Code"),
            ("text", "Email"),
            ("text", "Next"),
        ]
        
        for selector_type, selector in selectors:
            logger.info(f"尝试使用 {selector_type} 选择器: {selector}")
            
            if selector_type == "xpath":
                if self.click_element(selector):
                    logger.info(f"[{self.group_id}] 已通过XPath点击magic-code按钮")
                    logger.info(f"[{self.group_id}] 已点击magic-code按钮")
                    QTimer.singleShot(5000, self.wait_for_turnstile)
                    return
            elif selector_type == "css":
                if self.click_by_css(selector):
                    logger.info(f"[{self.group_id}] 已通过CSS点击magic-code按钮")
                    logger.info(f"[{self.group_id}] 已点击magic-code按钮")
                    QTimer.singleShot(5000, self.wait_for_turnstile)
                    return
            elif selector_type == "text":
                if self.click_by_text(selector):
                    logger.info(f"[{self.group_id}] 已通过按钮文本点击")
                    logger.info(f"[{self.group_id}] 已点击magic-code按钮")
                    QTimer.singleShot(5000, self.wait_for_turnstile)
                    return
        
        # 再次检查按钮是否已经变为禁用状态（可能在尝试点击过程中被其他操作点击了）
        button_status = self.execute_js(button_status_script)
        if isinstance(button_status, dict) and button_status.get('exists') and button_status.get('disabled'):
            logger.info(f"[{self.group_id}] 检测到按钮已变为禁用状态，可能已被点击")
            QTimer.singleShot(5000, self.wait_for_turnstile)
            return
        
        # 如果所有选择器都失败，等待并重试
        logger.warning(f"[{self.group_id}] 当前尝试失败，将在2秒后重试...")
        QTimer.singleShot(2000, lambda: self._try_click_button(retry_count + 1, max_retries))

    def wait_for_turnstile(self):
        """等待并监测人机验证框出现"""
        logger.info(f"[{self.group_id}] 等待人机验证框出现...")
        self._check_for_verification_or_next_step(retry_count=0, max_retries=15)
    
    def _check_for_verification_or_next_step(self, retry_count=0, max_retries=15):
        """轮询检查人机验证框是否出现或是否直接进入下一步"""
        if retry_count >= max_retries:
            logger.error(f"[{self.group_id}] 等待超时，未能检测到人机验证框或验证码输入框")
            self.signals.error.emit(self.group_id, self.email, "无法检测页面状态")
            self.cleanup()
            return
        
        logger.info(f"[{self.group_id}] 检查页面状态 (尝试 {retry_count+1}/{max_retries})...")
        
        # 检查页面内容
        if retry_count % 3 == 0:  # 每3次尝试记录一次页面内容
            self.analyze_page()
        
        # 1. 直接检查验证码输入框是否出现（跳过验证）
        if self.check_element_exists("//input[@inputmode='numeric']"):
            logger.info(f"[{self.group_id}] 检测到验证码输入框，无需人机验证处理")
            self.check_for_code_input()
            return
            
        # 2. 检测人机验证框的不同方式
        
        # 2.1 检测Turnstile iframe
        # 使用更安全的方式检测iframe，避免JS错误
        iframe_script = """
        (function() {
            try {
                var iframes = document.querySelectorAll('iframe');
                for (var i = 0; i < iframes.length; i++) {
                    var src = iframes[i].src || '';
                    if (src.indexOf('turnstile') !== -1 || src.indexOf('cloudflare') !== -1) {
                        return true;
                    }
                }
                return false;
            } catch(e) {
                console.error('iframe检测错误:', e);
                return false;
            }
        })();
        """
        try:
            has_turnstile_iframe = self.execute_js(iframe_script)
            if has_turnstile_iframe is None:  # 如果返回None，说明可能有JS错误
                has_turnstile_iframe = False
        except Exception as e:
            logger.error(f"[{self.group_id}] iframe检测脚本执行错误: {e}")
            has_turnstile_iframe = False
        
        # 2.2 检测 cf-turnstile 元素（既匹配 id，也匹配包含 cf-turnstile 类名的情况）
        has_turnstile_div = self.check_element_exists("//div[@id='cf-turnstile' or contains(@class, 'cf-turnstile')]")
        
        # 2.3 检测验证文本
        # 使用更安全的方式检测验证文本，避免JS错误
        verify_text_script = """
        (function() {
            try {
                var text = document.body.innerText || '';
                var patterns = ["Verify you are human", "Before continuing", "human verification", "security check", "I'm not a robot", "人机验证"];
                for (var i = 0; i < patterns.length; i++) {
                    if (text.indexOf(patterns[i]) !== -1) {
                        return true;
                    }
                }
                return false;
            } catch(e) {
                console.error('文本检测错误:', e);
                return false;
            }
        })();
        """
        try:
            has_verify_text = self.execute_js(verify_text_script)
            if has_verify_text is None:  # 如果返回None，说明可能有JS错误
                has_verify_text = False
        except Exception as e:
            logger.error(f"[{self.group_id}] 验证文本检测脚本执行错误: {e}")
            has_verify_text = False
        
        # 2.4 检查页面标题和URL，这些通常更可靠
        page_title = self.execute_js("document.title") or ""
        page_url = self.execute_js("window.location.href") or ""
        has_verify_in_title_or_url = ("verify" in page_title.lower() or 
                                     "verify" in page_url.lower() or 
                                     "challenge" in page_url.lower() or
                                     "captcha" in page_url.lower())
        
        if has_verify_in_title_or_url:
            logger.info(f"[{self.group_id}] 通过标题或URL检测到验证页面: {page_title} / {page_url}")
        
        # 3. 如果检测到人机验证，使用不同方法依次尝试处理
        # 更严格的验证条件：必须有iframe或验证文本，div元素本身不足以确认
        # 添加标题/URL检测作为额外的判断依据
        if (has_turnstile_iframe or has_verify_text or has_verify_in_title_or_url or 
           (has_turnstile_div and (has_turnstile_iframe or has_verify_text or has_verify_in_title_or_url))):
            logger.info(f"[{self.group_id}] 检测到人机验证 (iframe:{has_turnstile_iframe}, div:{has_turnstile_div}, text:{has_verify_text}, title/url:{has_verify_in_title_or_url})")
            
            # 检查页面上是否有明显的验证框特征 - 比如"Verify you are human"文本或复选框
            checkbox_exists = self.check_element_exists("//input[@type='checkbox']") or self.check_element_exists("//div[contains(@class, 'checkbox')]")
            if checkbox_exists:
                logger.info(f"[{self.group_id}] 检测到验证复选框元素")
            
            # 先尝试iframe方式处理（模拟Selenium方式）
            logger.info(f"[{self.group_id}] 尝试使用iframe方式处理人机验证...")
            if self.handle_iframe_turnstile():
                logger.info(f"[{self.group_id}] iframe方式处理完成，等待验证结果...")
                QTimer.singleShot(3000, self.check_for_code_input)
                return
                
            # 如果iframe方式失败，尝试常规方式处理
            logger.info(f"[{self.group_id}] iframe方式处理未成功，尝试常规方式...")
            if self.handle_turnstile():
                logger.info(f"[{self.group_id}] 常规方式处理完成，等待验证结果...")
                QTimer.singleShot(3000, self.check_for_code_input)
                return
                
            # 如果仍然无法处理，尝试再次发送Tab+空格键
            logger.info(f"[{self.group_id}] 人机验证处理未成功，尝试直接发送按键...")
            
            # 使用PyQt发送按键
            try:
                from PyQt6.QtCore import Qt
                from PyQt6.QtGui import QKeyEvent
                
                tab_event = QKeyEvent(QKeyEvent.Type.KeyPress, Qt.Key.Key_Tab, Qt.KeyboardModifier.NoModifier)
                QApplication.sendEvent(self.browser, tab_event)
                time.sleep(1)
                
                space_event = QKeyEvent(QKeyEvent.Type.KeyPress, Qt.Key.Key_Space, Qt.KeyboardModifier.NoModifier)
                QApplication.sendEvent(self.browser, space_event)
                
                logger.info(f"[{self.group_id}] 已直接发送Qt按键事件")
            except Exception as e:
                logger.error(f"[{self.group_id}] Qt按键发送失败: {e}")
                
            # 使用pyautogui发送物理按键
            if HAS_PYAUTOGUI and not self.headless:
                try:
                    self.browser_window.activateWindow()
                    self.browser_window.raise_()
                    time.sleep(0.5)
                    
                    pyautogui.press('tab')
                    time.sleep(1)
                    pyautogui.press('space')
                    logger.info(f"[{self.group_id}] 已通过pyautogui发送物理按键")
                except Exception as e:
                    logger.error(f"[{self.group_id}] 物理按键发送失败: {e}")
            
            # 继续等待处理结果
            QTimer.singleShot(5000, lambda: self._check_for_verification_or_next_step(retry_count + 1, max_retries))
            return
            
        # 4. 检查是否在按钮等待状态（按钮已点击，变灰，但尚未出现人机验证）
        # 通过检查页面上是否有disabled或aria-disabled属性的magic-code按钮
        button_status_script = """
        (function() {
            try {
                // 查找magic-code按钮
                const magicButtons = document.querySelectorAll('button[value="magic-code"]');
                if (magicButtons.length === 0) {
                    return {exists: false, message: "未找到magic-code按钮"};
                }
                
                // 检查按钮状态
                const button = magicButtons[0];
                const isDisabled = button.disabled || 
                                  button.getAttribute('disabled') !== null ||
                                  button.getAttribute('aria-disabled') === 'true' ||
                                  button.classList.contains('disabled');
                
                // 如果按钮存在但已禁用，表示已点击正在等待
                if (isDisabled) {
                    return {exists: true, disabled: true, message: "按钮已点击，处于等待状态"};
                } else {
                    return {exists: true, disabled: false, message: "按钮可点击"};
                }
            } catch(e) {
                return {error: e.toString()};
            }
        })();
        """
        
        button_status = self.execute_js(button_status_script)
        if isinstance(button_status, dict) and button_status.get('exists') and button_status.get('disabled'):
            logger.info(f"[{self.group_id}] 检测到magic-code按钮已点击并处于等待状态，继续等待...")
            QTimer.singleShot(3000, lambda: self._check_for_verification_or_next_step(retry_count + 1, max_retries))
            return
            
        # 5. 检查是否仍在初始页面且按钮可点击（按钮点击可能失败）
        elif isinstance(button_status, dict) and button_status.get('exists') and not button_status.get('disabled'):
            logger.warning(f"[{self.group_id}] 检测到magic-code按钮可点击，尝试点击")
            QTimer.singleShot(1000, self.do_click_magic_code)
            return
            
        # 6. 如果以上都不匹配，继续等待
        logger.info(f"[{self.group_id}] 页面状态不明确，继续等待...")
        QTimer.singleShot(5000, lambda: self._check_for_verification_or_next_step(retry_count + 1, max_retries))

    def detect_captcha(self):
        """检测页面上是否有人机验证"""
        # 方法1：检查典型的验证元素
        captcha_elements = [
            "//div[@id='cf-turnstile']",
            "//iframe[contains(@src, 'cloudflare') or contains(@src, 'turnstile')]",
            "//div[contains(@class, 'cf-') or contains(@class, 'turnstile')]",
            "//div[@data-testid='challenge-stage']",
            "//div[@aria-label='Human verification']"
        ]
        
        for xpath in captcha_elements:
            if self.check_element_exists(xpath):
                logger.info(f"[{self.group_id}] 通过元素检测到验证框: {xpath}")
                return True
                
        # 方法2：检查页面文本
        patterns = [
            "Verify you are human",
            "security check",
            "human verification",
            "captcha",
            "cloudflare",
            "turnstile",
            "challenge",
            "人机验证",
            "安全检查"
        ]
        
        # 使用更安全的JS执行方式
        patterns_js = self._js_str(json.dumps(patterns))
        captcha_text_script = f"""
        var text = document.body.innerText || '';
        var patterns = {patterns_js};
        
        for (var i = 0; i < patterns.length; i++) {{
            if (text.toLowerCase().indexOf(patterns[i].toLowerCase()) !== -1) {{
                return {{found: true, pattern: patterns[i]}};
            }}
        }}
        return {{found: false}};
        """
        
        text_result = self.execute_js_safe(captcha_text_script, {"found": False})
        if text_result and text_result.get("found"):
            logger.info(f"[{self.group_id}] 通过文本检测到验证框: {text_result.get('pattern')}")
            return True
        
        # 方法3：检查页面标题和URL
        title_url_patterns = ["verify", "security", "captcha", "cloudflare", "turnstile", "challenge", "人机验证"]
        title_url_patterns_js = self._js_str(json.dumps(title_url_patterns))
        
        title_url_script = f"""
        var title = document.title || '';
        var url = window.location.href || '';
        var patterns = {title_url_patterns_js};
        
        for (var i = 0; i < patterns.length; i++) {{
            var pattern = patterns[i].toLowerCase();
            if (title.toLowerCase().indexOf(pattern) !== -1 || url.toLowerCase().indexOf(pattern) !== -1) {{
                return {{found: true, pattern: patterns[i], where: title.toLowerCase().indexOf(pattern) !== -1 ? 'title' : 'url'}};
            }}
        }}
        return {{found: false}};
        """
        
        title_url_result = self.execute_js_safe(title_url_script, {"found": False})
        if title_url_result and title_url_result.get("found"):
            logger.info(f"[{self.group_id}] 通过{title_url_result.get('where')}检测到验证框: {title_url_result.get('pattern')}")
            return True
            
        return False
        
    def check_for_code_input(self):
        """专门检查验证码输入框是否出现"""
        logger.info(f"[{self.group_id}] 检查验证码输入框是否出现...")
        
        input_xpath = "//input[@inputmode='numeric' and @data-index='0']"
        if self.check_element_exists(input_xpath):
            logger.info(f"[{self.group_id}] 验证码输入框已出现，开始获取验证码...")
            self._step = 2
            # 开始尝试获取验证码并输入
            self.do_input_code()
            return
            
        # 检查是否仍有验证框
        if self.detect_captcha():
            logger.info(f"[{self.group_id}] 仍有验证框，尝试再次处理人机验证")
            if self.handle_turnstile():
                logger.info(f"[{self.group_id}] 人机验证处理完成，5秒后再次检查验证码输入框")
                QTimer.singleShot(5000, self.check_for_code_input)
            else:
                logger.info(f"[{self.group_id}] 人机验证处理失败，3秒后再次检查")
                QTimer.singleShot(3000, self.check_for_code_input)
            return
            
        # 检查页面状态
        self.analyze_page()
        
        # 检测是否有按钮需要重新点击
        buttons_with_magic = self.execute_js("""
        (function() {
            var btns = document.querySelectorAll('button[value="magic-code"]');
            return btns.length > 0;
        })();
        """)
        
        if buttons_with_magic:
            logger.info(f"[{self.group_id}] 检测到magic-code按钮，尝试重新点击")
            QTimer.singleShot(1000, self.do_click_magic_code)
            return
            
        # 如果5次检查都没发现输入框，报错
        if not hasattr(self, '_input_check_count'):
            self._input_check_count = 0
        
        self._input_check_count += 1
        if self._input_check_count >= 5:
            logger.error(f"[{self.group_id}] 多次检查未发现验证码输入框")
            self.signals.error.emit(self.group_id, self.email, "未找到验证码输入字段")
            self.cleanup()
            return
            
        # 继续等待验证码输入框
        logger.info(f"[{self.group_id}] 继续等待验证码输入框出现... ({self._input_check_count}/5)")
        QTimer.singleShot(3000, self.check_for_code_input)

    def do_input_code(self, retry_count=0, max_retries=50):
        """获取并输入验证码，最多尝试50次，每次间隔3秒"""
        logger.info(f"[{self.group_id}] 正在获取{self.email}的验证码，尝试 {retry_count+1}/{max_retries}...")
        
        # 尝试获取验证码
        self.code = self.get_email_code(self.email)
        
        if not self.code:
            # 如果没有获取到验证码，且尝试次数未达到上限，则继续尝试
            if retry_count < max_retries:
                logger.info(f"[{self.group_id}] 未获取到验证码，3秒后重试...")
                QTimer.singleShot(3000, lambda: self.do_input_code(retry_count + 1, max_retries))
                return
            else:
                # 达到最大重试次数，报告失败
                logger.error(f"[{self.group_id}] 尝试{max_retries}次后仍未能获取验证码")
                self.signals.error.emit(self.group_id, self.email, f"获取验证码失败，已尝试{max_retries}次")
                self.cleanup()
                return
                
        # 获取到验证码，进行输入
        logger.info(f"[{self.group_id}] 获取到验证码: {self.code}，开始输入")
        if not self.input_verification_code(self.code):
            self.signals.error.emit(self.group_id, self.email, "输入验证码失败")
            self.cleanup()
            return
            
        logger.info(f"[{self.group_id}] 验证码输入成功，等待重定向...")
        QTimer.singleShot(3000, self.check_redirect)

    def check_redirect(self):
        current_url = self.browser.url().toString()
        logger.info(f"[{self.group_id}] 当前URL: {current_url}")
        if "www.cursor.com" in current_url:
            self._step = 4
            self.on_load_finished(True)
            return
        # 最多等待20秒
        if not hasattr(self, '_redirect_wait'): self._redirect_wait = 0
        self._redirect_wait += 1
        if self._redirect_wait > 20:
            self.signals.error.emit(self.group_id, self.email, "重定向到cursor.com失败")
            self.cleanup()
            return
        QTimer.singleShot(1000, self.check_redirect)

    def do_get_token(self):
        self.token = self.get_token()
        if not self.token:
            self.signals.error.emit(self.group_id, self.email, "获取token失败")
            self.cleanup()
            return
        logger.info(f"[{self.group_id}] 成功获取{self.email}的token")
        if self.submit_token(self.email, self.token):
            self.signals.finished.emit(self.group_id, self.email, self.token)
        else:
            self.signals.error.emit(self.group_id, self.email, "提交token到API失败")
        self.cleanup()

    def handle_iframe_turnstile(self):
        """处理iframe内部的Turnstile验证，模拟Selenium的iframe切换方式"""
        logger.info(f"[{self.group_id}] 尝试通过iframe直接处理Turnstile...")
        
        try:
            # 1. 找到turnstile iframe
            iframe_script = """
            (function() {
                try {
                    // 寻找iframe
                    let iframes = document.querySelectorAll('iframe');
                    let turnstileIframe = null;
                    let frameId = null;
                    
                    for (let i = 0; i < iframes.length; i++) {
                        let src = iframes[i].src || '';
                        if (src.includes('cloudflare') || src.includes('turnstile')) {
                            turnstileIframe = iframes[i];
                            frameId = i;
                            break;
                        }
                    }
                    
                    if (!turnstileIframe) {
                        return {found: false, message: "未找到Turnstile iframe"};
                    }
                    
                    // 返回iframe信息
                    return {
                        found: true,
                        id: frameId,
                        src: turnstileIframe.src,
                        visible: turnstileIframe.offsetWidth > 0 && turnstileIframe.offsetHeight > 0
                    };
                } catch (e) {
                    return {found: false, message: e.toString()};
                }
            })();
            """
            
            iframe_info = self.execute_js(iframe_script)
            
            if not iframe_info or not iframe_info.get('found'):
                logger.error(f"[{self.group_id}] 未找到Turnstile iframe: {iframe_info.get('message') if iframe_info else 'unknown'}")
                return False
                
            logger.info(f"[{self.group_id}] 找到Turnstile iframe: {iframe_info}")
            
            # 2. 确保iframe可见并获取焦点
            focus_iframe_script = """
            (function() {
                try {
                    let iframes = document.querySelectorAll('iframe');
                    let iframe = iframes[%d];
                    
                    if (!iframe) return false;
                    
                    // 滚动确保可见 (behavior 仅支持 'auto' 或 'smooth')
                    iframe.scrollIntoView({behavior: 'auto', block: 'center'});
                    
                    // 点击iframe获取焦点
                    iframe.focus();
                    iframe.click();
                    
                    return true;
                } catch (e) {
                    console.error('iframe焦点处理失败:', e);
                    return false;
                }
            })();
            """ % iframe_info.get('id', 0)
            
            if not self.execute_js(focus_iframe_script):
                logger.error(f"[{self.group_id}] 无法聚焦到iframe")
                return False
                
            logger.info(f"[{self.group_id}] 已聚焦到Turnstile iframe")
            time.sleep(1)
            
            # 3. 通过iframe直接操作其内部的复选框
            # 这个操作类似于Selenium的switchTo().frame()
            checkbox_script = """
            (function() {
                try {
                    let iframes = document.querySelectorAll('iframe');
                    let iframe = iframes[%d];
                    
                    if (!iframe) return false;
                    
                    // 尝试访问iframe内容并点击复选框
                    // 注意：由于同源策略，这可能会失败
                    try {
                        let iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        let checkbox = iframeDoc.querySelector('input[type="checkbox"]');
                        
                        if (checkbox) {
                            checkbox.click();
                            return {success: true, method: "direct"};
                        }
                    } catch (securityError) {
                        // 同源策略阻止访问iframe内容
                        console.log('无法直接访问iframe内容，尝试其他方法');
                    }
                    
                    // 备用方法：使用Tab键来导航到复选框
                    iframe.focus();
                    return {success: true, method: "focus"};
                } catch (e) {
                    console.error('iframe内容处理失败:', e);
                    return {success: false, error: e.toString()};
                }
            })();
            """ % iframe_info.get('id', 0)
            
            result = self.execute_js(checkbox_script)
            logger.info(f"[{self.group_id}] iframe内部处理结果: {result}")
            
            # 4. 发送Tab和空格键，让浏览器内部处理焦点
            # 这模拟了Java代码中的actions.sendKeys(Keys.TAB).pause(1000).sendKeys(Keys.SPACE).perform();
            
            # 4.1 使用PyQt发送按键
            from PyQt6.QtCore import Qt
            from PyQt6.QtGui import QKeyEvent
            
            # 发送Tab键
            tab_event = QKeyEvent(QKeyEvent.Type.KeyPress, Qt.Key.Key_Tab, Qt.KeyboardModifier.NoModifier)
            QApplication.sendEvent(self.browser, tab_event)
            time.sleep(1)
            
            # 发送空格键
            space_event = QKeyEvent(QKeyEvent.Type.KeyPress, Qt.Key.Key_Space, Qt.KeyboardModifier.NoModifier)
            QApplication.sendEvent(self.browser, space_event)
            
            logger.info(f"[{self.group_id}] 已通过Qt事件发送Tab+空格键")
            
            # 4.2 使用pyautogui发送按键
            if HAS_PYAUTOGUI and not self.headless:
                try:
                    self.browser_window.activateWindow()
                    self.browser_window.raise_()
                    time.sleep(0.5)
                    
                    pyautogui.press('tab')
                    time.sleep(1)
                    pyautogui.press('space')
                    logger.info(f"[{self.group_id}] 已通过pyautogui发送物理按键")
                except Exception as e:
                    logger.error(f"[{self.group_id}] pyautogui按键发送失败: {e}")
            
            # 5. 等待验证处理
            logger.info(f"[{self.group_id}] iframe处理完成，等待验证结果...")
            time.sleep(4)
            
            return True
            
        except Exception as e:
            logger.error(f"[{self.group_id}] iframe处理出错: {e}")
            return False

    def click_by_index_or_text(self, button_text=None, value=None):
        """通过按钮索引或文本直接点击按钮"""
        logger.info(f"尝试直接通过索引或文本点击按钮: 文本={button_text}, 值={value}")
        
        # 获取所有按钮
        script = """
        (function() {
            var buttons = document.querySelectorAll('button');
            var buttonData = [];
            for (var i = 0; i < buttons.length; i++) {
                var btn = buttons[i];
                buttonData.push({
                    index: i,
                    text: btn.textContent.trim(),
                    value: btn.getAttribute('value'),
                    name: btn.getAttribute('name'),
                    visible: btn.offsetWidth > 0 && btn.offsetHeight > 0
                });
            }
            return buttonData;
        })();
        """
        
        buttons = self.execute_js(script)
        if not buttons:
            logger.warning("未能获取按钮列表")
            return False
        
        # 查找匹配的按钮
        target_button = None
        for button in buttons:
            if value and button.get('value') == value:
                target_button = button
                logger.info(f"找到按值匹配的按钮: {button}")
                break
                
            if button_text and button_text in button.get('text', ''):
                target_button = button
                logger.info(f"找到按文本匹配的按钮: {button}")
                break
        
        # 如果没找到匹配的按钮，尝试使用第二个按钮(索引1)，这通常是主要操作按钮
        if not target_button and len(buttons) > 1:
            target_button = buttons[1]
            logger.info(f"使用默认的第二个按钮: {target_button}")
        
        # 点击找到的按钮
        if target_button:
            index = target_button.get('index')
            click_script = """
            (function() {
                var buttons = document.querySelectorAll('button');
                if (buttons.length > %d) {
                    buttons[%d].click();
                    return true;
                }
                return false;
            })();
            """ % (index, index)
            
            result = self.execute_js(click_script)
            logger.info(f"按钮索引点击结果: {result}")
            return result
        
        return False

    def click_by_css(self, css_selector):
        """通过CSS选择器点击元素"""
        logger.info(f"尝试通过CSS点击元素: {css_selector}")
        # 使用 _js_str 确保选择器安全嵌入
        escaped_selector = _js_str(css_selector)
        script = f"""
        (function() {{
            var element = document.querySelector({escaped_selector});
            if (!element) {{
                return false;
            }}
            element.click();
            return true;
        }})();
        """
         
        result = self.execute_js(script)
        if result:
            time.sleep(1)
            return True
        return False
        
    def click_by_text(self, button_text):
        """通过按钮文本内容点击"""
        logger.info(f"尝试通过文本内容点击按钮: {button_text}")
        escaped_text = _js_str(button_text)
        script = f"""
        (function() {{
            var buttons = document.querySelectorAll('button');
            for (var i = 0; i < buttons.length; i++) {{
                var btn = buttons[i];
                if (btn.innerText && btn.innerText.indexOf({escaped_text}) !== -1) {{
                    btn.click();
                    return true;
                }}
            }}
            return false;
        }})();
        """
         
        result = self.execute_js(script)
        if result:
            time.sleep(1)
            return True
        return False

class CursorRegistrationManager:
    def __init__(self, num_threads=DEFAULT_THREADS, headless=False):
        self.num_threads = min(num_threads, MAX_THREADS)  # 限制线程数不超过最大值
        self.headless = headless
        self.active_threads = []
        self.successful_registrations = []
        self.failed_registrations = []
        self.register_instances = []  # 保存注册实例的引用
    
    def on_registration_finished(self, group_id, email, token):
        logger.info(f"注册成功: {email} (组: {group_id})")
        self.successful_registrations.append({
            "group_id": group_id,
            "email": email,
            "token": token,
            "timestamp": datetime.now().isoformat()
        })
    
    def on_registration_error(self, group_id, email, error):
        logger.error(f"注册失败: {email} (组: {group_id}): {error}")
        self.failed_registrations.append({
            "group_id": group_id,
            "email": email,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
    
    def start_registrations(self):
        """启动注册线程"""
        logger.info(f"启动 {self.num_threads} 个注册线程")
        
        for i in range(self.num_threads):
            group_id = f"group_{i}_{int(time.time())}_{random.randint(100, 999)}"
            register = CursorAutoRegister(group_id, self.headless)
            
            # 保存实例引用
            self.register_instances.append(register)
            
            # 连接信号
            register.signals.finished.connect(self.on_registration_finished)
            register.signals.error.connect(self.on_registration_error)
            
            # 直接调用run方法
            register.run()
        
        # 总结
        logger.info(f"注册初始化完成。成功: {len(self.successful_registrations)}, 失败: {len(self.failed_registrations)}")
        return self.successful_registrations, self.failed_registrations

def main():
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="Cursor自动注册工具")
    parser.add_argument("-t", "--threads", type=int, default=DEFAULT_THREADS, help=f"并行注册线程数量 (最大 {MAX_THREADS})")
    parser.add_argument("--headless", action="store_true", help="使用无头模式运行")
    parser.add_argument("--proxy", action="store_true", help="启用代理",  default=True)
    args = parser.parse_args()
    
    # 如果命令行指定了启用代理，则覆盖默认设置
    global USE_PROXY
    if args.proxy:
        USE_PROXY = True
        logger.info(f"已启用代理: {PROXY_HOST}:{PROXY_PORT}")
    
    # 创建QApplication
    logger.info("开始创建QApplication...")
    try:
        app = QApplication(sys.argv)
        app.setApplicationName("Cursor自动注册")
        app.setQuitOnLastWindowClosed(False)  # 防止最后一个窗口关闭时退出
        logger.info("QApplication创建成功")
    except Exception as e:
        logger.exception(f"创建QApplication失败: {e}")
        return
    
    # 创建并启动注册管理器
    logger.info(f"创建注册管理器，线程数: {args.threads}，无头模式: {args.headless}")
    global manager  # 保持全局引用
    try:
        def start_registration():
            global manager
            manager = CursorRegistrationManager(args.threads, args.headless)
            manager.start_registrations()
            logger.info("注册流程已启动，请勿关闭此窗口，窗口将保持打开状态")

        QTimer.singleShot(1000, start_registration)  # 延迟启动，确保事件循环开始运行
        sys.exit(app.exec())  # 开始事件循环，直到用户手动关闭
    except Exception as e:
        logger.exception(f"注册过程中出现异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()