from DrissionPage import ChromiumOptions, ChromiumPage
import time
import os
import signal
import random
import logging
import json
import sys
from urllib.parse import quote
import requests
from datetime import datetime
import threading
import psutil
from colorama import Fore, Style
import argparse

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cursor_registration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
SIGNUP_URL = "https://authenticator.cursor.sh/password?email={}&redirect_uri=https%3A%2F%2Fcursor.com%2Fapi%2Fauth%2Fcallback"
API_BASE_URL = "http://127.0.0.1:48080/admin-api"  # 修改为你的实际API端点
EMAIL_CODE_ENDPOINT = "/cqp/cursoremailpool/getEmailCode?token=ed97fe98dc91428096289b9ffa1f9d61"
TOKEN_SUBMIT_ENDPOINT = "/cqp/cursoremailpool/saveRecord?token=ed97fe98dc91428096289b9ffa1f9d61"

# 代理配置
USE_PROXY = False  # 是否使用代理
PROXY_HOST = "127.0.0.1"  # 代理服务器地址
PROXY_PORT = 7890  # 代理服务器端口

# 控制注册线程数量的常量
DEFAULT_THREADS = 1  # 默认并发线程数
MAX_THREADS = 20  # 最大并发线程数
DEFAULT_REGISTRATION_COUNT = 1000  # 每个线程默认注册次数

# 全局变量，用于存储浏览器进程ID
_chrome_process_ids = []

class DrissionCursorRegister:
    def __init__(self, group_id, headless=False):
        self.group_id = group_id
        self.headless = headless
        self.email = None
        self.page = None
        self.token = None
        self.code = None
        self.profile_path = f"chrome-data-cursor/group_{group_id}"  # 简化命名为固定格式
        self.max_attempts = 10  # 最大尝试次数
        self.current_attempt = 0
        self.before_pids = []
        self.success_callback = None
        self.error_callback = None
        
    def set_callbacks(self, success_callback, error_callback):
        """设置回调函数"""
        self.success_callback = success_callback
        self.error_callback = error_callback
        
    def generate_email(self):
        """生成随机邮箱地址"""
        timestamp = int(time.time())
        random_num = random.randint(100, 999)
        return f"a965586934{timestamp}{random_num}@2925.com"
        
    def setup_browser(self):
        """设置浏览器"""
        logger.info(f"[{self.group_id}] 开始设置浏览器...")

        # 先尝试删除已存在的目录
        try:
            if os.path.exists(self.profile_path):
                import shutil
                shutil.rmtree(self.profile_path)
                logger.info(f"[{self.group_id}] 已删除旧的浏览器配置文件夹: {self.profile_path}")
        except Exception as e:
            logger.warning(f"[{self.group_id}] 删除旧的浏览器配置文件夹时出错: {e}")

        # 创建新的目录
        os.makedirs(self.profile_path, exist_ok=True)
        
        try:
            # 记录启动前的浏览器进程
            self.before_pids = self.get_browser_processes()
            
            # 设置浏览器选项
            co = ChromiumOptions()
            
            # 设置浏览器路径
            browser_path = self.get_default_browser_path()
            co.set_browser_path(browser_path)
            
            # 设置用户数据目录
            co.set_user_data_path(self.profile_path)
            
            # 使用隐身模式
            co.set_argument("--incognito")
            
            # 设置代理（如果启用）
            if USE_PROXY:
                co.set_proxy(f"{PROXY_HOST}:{PROXY_PORT}")
                logger.info(f"[{self.group_id}] 已设置浏览器代理: {PROXY_HOST}:{PROXY_PORT}")
            
            # Linux特殊设置
            if sys.platform == "linux":
                co.set_argument("--no-sandbox")
                
            # 设置随机端口
            co.auto_port()
            
            # 设置无头模式
            co.headless(self.headless)
            
            # 启动浏览器
            logger.info(f"[{self.group_id}] 启动浏览器")
            self.page = ChromiumPage(co)
            
            # 等待一段时间确保浏览器完全启动
            time.sleep(1)
            
            # 记录新增的浏览器进程
            self.track_browser_processes()
            
            logger.info(f"[{self.group_id}] 浏览器设置完成")
            return True
            
        except Exception as e:
            logger.exception(f"[{self.group_id}] 设置浏览器时出现异常: {e}")
            return False
            
    def get_default_browser_path(self):
        """获取默认浏览器路径"""
        # 简化示例，实际使用时可以依据不同操作系统获取更精确的路径
        if sys.platform == "win32":
            return "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        elif sys.platform == "darwin":
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            return "/usr/bin/google-chrome"
            
    def get_browser_processes(self):
        """获取当前浏览器进程ID列表"""
        try:
            browser_process_names = ['chrome', 'chromium', 'msedge', 'edge', 'brave']
            return [p.pid for p in psutil.process_iter() if any(name in p.name().lower() for name in browser_process_names)]
        except:
            return []
            
    def track_browser_processes(self):
        """跟踪新启动的浏览器进程"""
        global _chrome_process_ids
        try:
            after_pids = self.get_browser_processes()
            new_pids = [pid for pid in after_pids if pid not in self.before_pids]
            _chrome_process_ids.extend(new_pids)
            
            if new_pids:
                logger.info(f"[{self.group_id}] 跟踪到 {len(new_pids)} 个新浏览器进程")
            else:
                logger.warning(f"[{self.group_id}] 未检测到新的浏览器进程")
        except Exception as e:
            logger.error(f"[{self.group_id}] 跟踪进程失败: {e}")
            
    def cleanup(self):
        """清理资源"""
        try:
            # 关闭浏览器
            if self.page:
                self.page.quit()
            
            # 删除配置文件目录
            import shutil
            if os.path.exists(self.profile_path):
                shutil.rmtree(self.profile_path)
                
            logger.info(f"[{self.group_id}] 资源清理完成")
        except Exception as e:
            logger.error(f"[{self.group_id}] 清理资源时出错: {e}")
            
    def handle_turnstile(self):
        """处理 Turnstile 验证"""
        logger.info(f"[{self.group_id}] 开始处理 Turnstile 人机验证...")
        
        # 等待turnstile元素出现
        turnstile = self.page.ele("@id=cf-turnstile", timeout=5)
        if not turnstile:
            logger.error(f"[{self.group_id}] 未找到 cf-turnstile 元素")
            return False
            
        try:
            # 尝试重置turnstile
            # self.page.run_js("try { turnstile.reset() } catch(e) { }")
            # time.sleep(2)
            
            # 定位验证框元素并点击
            try:
                challenge_check = (
                    turnstile.child()
                    .shadow_root.ele("tag:iframe")
                    .ele("tag:body")
                    .sr("tag:input")
                )
                
                if challenge_check:
                    logger.info(f"[{self.group_id}] 检测到验证框，点击验证")
                    challenge_check.click()
                    time.sleep(2)
                    
                    # 检查验证结果
                    if self.check_verification_success():
                        logger.info(f"[{self.group_id}] Turnstile 验证通过")
                        return True
            except:
                # 如果精确定位失败，尝试直接点击turnstile
                turnstile.click()
                time.sleep(2)
                
                # 检查验证结果
                if self.check_verification_success():
                    logger.info(f"[{self.group_id}] Turnstile 验证通过")
                    return True
                    
            # 最后一次尝试
            turnstile.click()
            time.sleep(3)
            if self.check_verification_success():
                logger.info(f"[{self.group_id}] Turnstile 验证通过")
                return True
                
            logger.warning(f"[{self.group_id}] Turnstile 验证未通过")
            return False
            
        except Exception as e:
            logger.error(f"[{self.group_id}] 处理验证时出错: {e}")
            return False
            
    def check_verification_success(self):
        """检查验证是否成功"""
        try:
            # 检查是否存在后续表单元素
            if (self.page.ele("@name=password", timeout=0.5) or
                self.page.ele("@name=email", timeout=0.5) or
                self.page.ele("@data-index=0", timeout=0.5) or
                self.page.ele("Account Settings", timeout=0.5)):
                return True
                
            # 检查是否有错误信息
            error_xpaths = [
                'xpath://div[contains(text(), "Can\'t verify the user is human")]',
                'xpath://div[contains(text(), "Error: 600010")]',
                'xpath://div[contains(text(), "Please try again")]'
            ]
            
            for xpath in error_xpaths:
                if self.page.ele(xpath):
                    return False
                    
            return False
        except:
            return False
            
    def input_verification_code(self, code):
        """输入验证码"""
        if not code or len(code) != 6:
            logger.error(f"[{self.group_id}] 无效的验证码: {code}")
            return False
            
        try:
            # 查找验证码输入框
            for i, digit in enumerate(code):
                input_ele = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='" + str(i) + "']")
                if input_ele:
                    input_ele.input(digit)
                    time.sleep(0.2)
                else:
                    logger.error(f"[{self.group_id}] 未找到第{i}个验证码输入框")
                    return False
                    
            logger.info(f"[{self.group_id}] 验证码输入成功")
            return True
            
        except Exception as e:
            logger.error(f"[{self.group_id}] 输入验证码出错: {e}")
            return False
            
    def get_email_code(self, email):
        """从邮件中获取验证码"""
        try:
            # 准备请求参数
            request_kwargs = {
                "params": {"email": email},
                "timeout": 10
            }
            
            # 添加代理配置（如果启用）
            # if USE_PROXY:
            #     request_kwargs["proxies"] = {
            #         "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
            #         "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
            #     }
                
            response = requests.get(f"{API_BASE_URL}{EMAIL_CODE_ENDPOINT}", **request_kwargs)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0 and data.get("data"):
                    logger.info(f"[{self.group_id}] 获取到邮箱验证码: {data.get('data')}")
                    return data.get("data")
                else:
                    logger.error(f"[{self.group_id}] 获取邮箱"+email+"验证码失败: {data.get('message')}")
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取邮箱验证码时出错: {e}")
            return None
            
    def get_token(self):
        """从cookies中提取WorkosCursorSessionToken"""
        try:
            cookies = self.page.cookies()
            for cookie in cookies:
                if cookie.get('name') == 'WorkosCursorSessionToken':
                    token = cookie.get('value')
                    # if token and '::' in token:
                    #     return token.split('::')[1]
                    return token
            return None
        except Exception as e:
            logger.error(f"[{self.group_id}] 获取token出错: {e}")
            return None
            
    def submit_token(self, email, token):
        """将token提交到API"""
        try:
            # 准备请求参数
            request_kwargs = {
                "json": {"email": email, "token": token},
                "timeout": 20
            }
            
            # 添加代理配置（如果启用）
            # if USE_PROXY:
            #     request_kwargs["proxies"] = {
            #         "http": f"http://{PROXY_HOST}:{PROXY_PORT}",
            #         "https": f"http://{PROXY_HOST}:{PROXY_PORT}"
            #     }
                
            response = requests.post(f"{API_BASE_URL}{TOKEN_SUBMIT_ENDPOINT}", **request_kwargs)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info(f"[{self.group_id}] 成功提交{email}的token")
                    return True
                    
            logger.error(f"[{self.group_id}] 提交token失败: {response.status_code} - {response.text}")
            return False
        except Exception as e:
            logger.error(f"[{self.group_id}] 提交token时出错: {e}")
            return False
            
    def process_registration(self):
        """处理注册流程"""
        if self.current_attempt >= self.max_attempts:
            logger.warning(f"[{self.group_id}] 达到最大尝试次数 {self.max_attempts}，终止当前注册")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, f"达到最大尝试次数 {self.max_attempts}")
            # 不再调用cleanup，只返回失败状态
            return False
            
        self.current_attempt += 1
        logger.info(f"[{self.group_id}] 执行注册尝试 {self.current_attempt}/{self.max_attempts}")
        
        # 等待页面加载
        time.sleep(3)
        has_overlay = bool(self.page.ele('xpath://div[@inert and @aria-hidden="true" and not(@class)]',timeout=1))
        # 1. 查找并点击magic-code按钮

        if not has_overlay:
            magic_code_button = self.page.ele("xpath://button[@name='intent' and @type='submit' and @value='magic-code']",timeout=1)
            if bool(magic_code_button):
                logger.info(f"[{self.group_id}] 找到magic-code按钮，点击")
                magic_code_button.click()
                time.sleep(3)
            
        # 2. 检查是否存在人机验证
        turnstile = self.page.ele("@id=cf-turnstile", timeout=1)
        if has_overlay :
            if bool(turnstile):
                logger.info(f"[{self.group_id}] 检测到人机验证")
                self.handle_turnstile()
                time.sleep(3)
            
        # 3. 检查是否存在验证码输入框
        code_input = self.page.ele("xpath://input[@inputmode='numeric' and @data-index='0']", timeout=1)
        if bool(code_input):
            logger.info(f"[{self.group_id}] 检测到验证码输入框，尝试获取验证码")
            self.process_verification_code()
            return True
            
        # 等待一段时间后继续尝试
        time.sleep(2)
        return self.process_registration()
        
    def process_verification_code(self, attempt=0):
        """处理验证码获取和输入"""
        if attempt >= 30:
            logger.error(f"[{self.group_id}] 验证码获取失败，达到最大尝试次数")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "验证码获取失败")
            return
            
        code = self.get_email_code(self.email)
        
        if not code:
            logger.info(f"[{self.group_id}] 尚未获取到验证码，3秒后重试 ({attempt + 1}/30)")
            time.sleep(3)
            self.process_verification_code(attempt + 1)
            return
            
        logger.info(f"[{self.group_id}] 获取到验证码，开始输入")
        if not self.input_verification_code(code):
            logger.error(f"[{self.group_id}] 验证码输入失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "验证码输入失败")
            return
            
        # 等待页面加载完成
        time.sleep(5)
            
        # 获取并提交token
        self.get_and_submit_token()
        
    def get_and_submit_token(self):
        """获取并提交token"""
        # 检查是否跳转到cursor.com
        current_url = self.page.url
        
        if "cursor.com" not in current_url:
            logger.warning(f"[{self.group_id}] 未跳转到cursor.com，尝试访问settings页面")
            self.page.get("https://www.cursor.com/settings")

        time.sleep(3)
        token = self.get_token()
        if not token:
            logger.error(f"[{self.group_id}] 获取token失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "获取token失败")
            return
            
        if self.submit_token(self.email, token):
            logger.info(f"[{self.group_id}] 注册成功: {self.email}")
            if self.success_callback:
                self.success_callback(self.group_id, self.email, token)
        else:
            logger.error(f"[{self.group_id}] 提交token失败")
            if self.error_callback:
                self.error_callback(self.group_id, self.email, "提交token失败")
        
        # 清除cookie，以便下次注册更干净
        try:
            self.page.run_cdp('Network.clearBrowserCookies')
            logger.info(f"[{self.group_id}] 已清除cookies")
        except Exception as e:
            logger.error(f"[{self.group_id}] 清除cookies出错: {e}")
                
        # 不再调用cleanup，保持浏览器打开状态
        
    def run(self):
        """运行注册流程"""
        # 初始化浏览器（只初始化一次）
        if not self.setup_browser():
            logger.error(f"[{self.group_id}] 浏览器设置失败，线程终止")
            if self.error_callback:
                self.error_callback(self.group_id, "None", "浏览器初始化失败")
            return
            
        logger.info(f"[{self.group_id}] 浏览器初始化成功，准备开始注册流程")
        
        # 运行指定次数的注册流程
        registration_count = 0
        
        try:
            while registration_count < DEFAULT_REGISTRATION_COUNT:
                registration_count += 1
                logger.info(f"[{self.group_id}] 开始第 {registration_count}/{DEFAULT_REGISTRATION_COUNT} 次注册流程")
                
                # 重置当前尝试次数
                self.current_attempt = 0
                
                # 生成新邮箱
                self.email = self.generate_email()
                logger.info(f"[{self.group_id}] 准备注册新账号: {self.email}")
                
                # 加载注册页面
                encoded_email = quote(self.email)
                url = SIGNUP_URL.format(encoded_email)
                self.page.get(url)
                
                # 开始处理注册
                time.sleep(2)
                self.process_registration()
                
                # 注册流程结束后休息2秒
                time.sleep(2)
            
            logger.info(f"[{self.group_id}] 完成全部 {DEFAULT_REGISTRATION_COUNT} 次注册流程")
        finally:
            # 所有注册完成后，清理资源
            self.cleanup()
            logger.info(f"[{self.group_id}] 线程资源已清理")


class RegistrationManager:
    def __init__(self, num_threads=DEFAULT_THREADS, headless=False):
        self.num_threads = min(num_threads, MAX_THREADS)
        self.headless = headless
        self.registers = []
        self.successful = []
        self.failed = []
        
    def on_registration_finished(self, group_id, email, token):
        self.successful.append({
            "group_id": group_id,
            "email": email,
            "token": token,
            "timestamp": datetime.now().isoformat()
        })
        logger.info(f"注册成功: {email} (组: {group_id})")
        
#         # 将成功信息保存到文件
#         with open("successful_registrations.txt", "a", encoding="utf-8") as f:
#             f.write(f"{datetime.now().isoformat()} | {email} | {token}\n")
            
    def on_registration_error(self, group_id, email, error):
        self.failed.append({
            "group_id": group_id,
            "email": email,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        logger.error(f"注册失败: {email} (组: {group_id}): {error}")
        
    def start(self):
        """开始注册进程"""
        logger.info(f"启动 {self.num_threads} 个注册线程")
        
        # 清理浏览器进程
        cleanup_chrome_processes()
        
        for i in range(self.num_threads):
            group_id = f"{i}"  # 简化为数字索引
            register = DrissionCursorRegister(group_id, self.headless)
            
            # 设置回调
            register.set_callbacks(self.on_registration_finished, self.on_registration_error)
            
            # 创建线程运行注册实例
            thread = threading.Thread(target=register.run, daemon=True)
            
            # 保存实例
            self.registers.append(register)
            
            # 启动线程
            thread.start()
            
        logger.info("所有注册线程已启动")


def cleanup_chrome_processes():
    """清理Chrome进程"""
    global _chrome_process_ids
    
    if not _chrome_process_ids:
        logger.info("没有需要清理的Chrome进程...")
        return
        
    logger.info(f"清理 {len(_chrome_process_ids)} 个Chrome进程...")
    try:
        if os.name == 'nt':
            for pid in _chrome_process_ids:
                try:
                    os.system(f'taskkill /F /PID {pid} /T 2>nul')
                except:
                    pass
        else:
            for pid in _chrome_process_ids:
                try:
                    os.kill(pid, signal.SIGTERM)
                except:
                    pass
        _chrome_process_ids = []
    except Exception as e:
        logger.error(f"清理进程时出错: {e}")


def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info("接收到退出信号，正在清理资源...")
    cleanup_chrome_processes()
    sys.exit(0)


def main():
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 全局变量声明
    global USE_PROXY
    global DEFAULT_REGISTRATION_COUNT
    
    # 命令行参数
    parser = argparse.ArgumentParser(description="Cursor自动注册工具")
    parser.add_argument("-t", "--threads", type=int, default=DEFAULT_THREADS,
                        help=f"并行注册线程数量 (最大 {MAX_THREADS})")
    parser.add_argument("--headless", action="store_true", help="使用无头模式运行")
    parser.add_argument("--proxy", action="store_true", help="启用代理")
    parser.add_argument("-n", "--count", type=int, default=DEFAULT_REGISTRATION_COUNT,
                        help="每个线程的注册次数")
    args = parser.parse_args()
    
    # 设置代理
    USE_PROXY = True
    logger.info(f"已启用代理: {PROXY_HOST}:{PROXY_PORT}")
        
    # 设置注册次数
    if args.count and args.count > 0:
        DEFAULT_REGISTRATION_COUNT = args.count
        logger.info(f"每个线程的注册次数已设置为: {DEFAULT_REGISTRATION_COUNT}")
        
    # 启动管理器
    manager = RegistrationManager(args.threads, args.headless)
    manager.start()
    
    # 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到用户中断，正在退出...")
        cleanup_chrome_processes()


if __name__ == "__main__":
    main() 